
Static ArenaHi Malloc 
Create a permanent allocation from the top of the arena of a custom size 
Edit <static_ArenaHi_Malloc> data to change the allocation size 
[Punk<PERSON>, DRGN] 
##<static_ArenaHi_Malloc> NTSC 1.02 
##000 40000  # half a megabyte 
  
NTSC 1.02 --- 80375324 ---- 93810008 -> Branch 
##lis r0, <<static_ArenaHi_Malloc>>@h 
##ori r4, r0, <<static_ArenaHi_Malloc>>@l 
##lis r0, <<static_ArenaHi_Malloc>>@h 
##ori r4, r0, <<static_ArenaHi_Malloc>>@l 
##lwz r3, 0x0(r4) 
##li r4, 4 
##bl 0x80344514 
##  
##_return: 
##stw r28, 0x0008 (sp) 
##.long 0

3C000000 60040000
80640000 38800004
bl 0x80344514
93810008 00000000


	-==-


Load codes.bin
[Punkline]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x803753E4 ---- 8001001C -> Branch

malloc = 0x817f8ac0 - 0x80000 
  
bl _data 
b _code 
  _data: blrl 
  0: .asciz "codes.bin" 
  .align 2 
   
_code: 
mflr r3 
lis r0, malloc@h 
lmw r28, 0x8(sp)     # r3 = path string 
ori r4, r0, malloc@l # r4 = allocation address 
addi r5, sp, 0x0C    # r5 = returns size value 
bl 0x8001668c  # $!_load_fromDVD 
  
_return: 
lwz r0, 0x001C (sp) 
.long 0


-==-
  
 
ASM - Modified Camera Info Flag Initialization
Allows you to specify new default flag states for the camera info struct. The default state does  
 not require developer mode to be enabled, and can still be modified freely by the usual developer  
 controller mode hotkeys.
[Punkline]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x80028dc8 ---- 981F039A -> Branch
 
stb r0, 0x039A (r31)
# original instruction
  
li r0, 0                # default base
flags: xori r0, r0, 0x0000   
# modifiable bools at flags+2:
# + 0x8000 : hide players
# + 0x2000 : Disable Background
# + 0x1000 : Disable Background Particles
# + 0x0800 : Disable Background Lights (?)
# + 0x0400 : Disable Background Color Effects
# + 0x0010 : Enable ECB, stage geometry, and other visualizations
# + 0x0008 : Enable camera facing orientation visualizations
# + 0x0004 : Disable Background and stage geometry display
# + 0x0002 : ?
# + 0x0001 : Disable Ground Textures (?)
  
sth r0, 0x398(r31)
.long 0
