



801d1e20 000000d8 801d1e20 0 PokemonStadium_CheckToSpawnFirework



falcon
80452f88 42004ae7
80452f8c 40c80aff
80452f90 4197e801


cam 1 range ~ 80452f30 - 80452fb0

written to by Camera_PauseScreenInitialize



stack:

8016ca68	Pause_IngameCheckPauseOnOff	(if 8046b6a4 == 0 and 8046b6a5 != 0: initiate pause)
8016bc74		Pause_CheckButtonInputsToPause
				[for scene 0xE, runs to 8016bd94 before real work]
				[loops through all players; r30 holds current player index]
				[break on 8016be40 to break on pause]
				[8016be60: can force pause by setting r3 to 0 
				 (or another player index) at the end of this function]
			MatchController_FreezeScreen_SetBool
			[nop 8016cb50 to initialize normal pause camera]
80165108		Pause_InitializeVSPauseCamera
8002f73c			Camera_PauseScreenInitializeBegin_R5=0
8002F4D4				Camera_PauseScreenInitialize
						StageInfo_PauseInitialZPosition_Load



		Pause_Camera_Translation&More_PerFrame
8000d8f4		Math_Camera_CorrectPositionFromBoundaries



lbz	r0, 0x24CC (r29)
r29 = 8046b6a0

first write in this function: 8002f55c

	r31 = 80452C68


major checks occur at 8002f5cc

	calls StageInfo_PauseInitialZPosition_Load




8016e730 00000204 8016e730 0 StartMelee

8016e934 00000094 8016e934 0 SceneLoad_InGame
8016e9a4 00000024 8016e9a4 0 SceneLoad_Melee

801a583c 000002b4 801a583c 0 Match_InitMinorData(r3=,r4,r5,r6=StartMeleeCallback)


801c57a4 00000024 801c57a4 0 PlayerBlock_LoadP1DataOffsetStart

800693ac 00000e10 800693ac 0 ActionStateChange		(r3=pointer to Player Entity Struct, r4=action state ID, r31=Player Character Data address)


	spawning

80032070 0000012c 80032070 0 RespawnMain(r3=PlayerSlot,r4=FollowerBool)
80032cf0 00000090 80032cf0 0 PlayerBlock_LoadRespawnXYZ
800976a4 00000230 800976a4 0 HitGround_SpawnGFX+SFX
800bfe74 00000020 800bfe74 0 SubactionEvent_0x28_GFXSpawn_Prefunction
800bfeb4 00000020 800bfeb4 0 SubactionEvent_0x58_SFXSpawn
8016719c 00000184 8016719c 0 RespawnStart(r3=PlayerSlot,r4=FollowBool)
8016deec 00000238 8016deec 0 Player_InitializeSpawnPositions?
80224e64 00000178 80224e64 0 SpawnPoint_GetXYZFromIDInput            (r3=4 to scan upward for solid ground)


		order:
	Player_InitializeSpawnPositions?
		SpawnPoint_GetXYZFromIDInput




	gravity

stored @ 0x5C of the first struct in Pl__.dat files.

80036a08 000002e8 80036a08 0 StaticPlayerBlock_InitializeCommonValues
80036d58 0000004c 80036d58 0 StaticPlayerBlock_Init
80068354 00000390 80068354 0 InitializePlayerDataValues
80068914 0000052c 80068914 0 PlayerDataBlock_Initalize
80068e98 00000514 80068e98 0 AllocateAndInitPlayer
80074148 00000028 80074148 0 Fighter_InitPObj?
8007482c 00000050 8007482c 0 Fighter_InstantiateFighterJObj




801c06b8 0000009c 801c06b8 0 Stages_GetFileNameAndLoad
801c0754 000000ac 801c0754 0 Stages_LoadStage_InitAndLoad
801c0800 00000270 801c0800 0 StageInitialization
801c0a70 000001bc 801c0a70 0 BombombRain_Randomization
801c0c2c 0000034c 801c0c2c 0 BombRain_Think?
801c0f78 00000040 801c0f78 0 Stage_RunOnLoadFunction
801c0fb8 000000bc 801c0fb8 0 Stage_RunOnGO!Function
801c1e94 00000168 801c1e94 0 Stage_InitBackground



80210c7c 0000008c 80210c7c 0 Stages_Dreamland_InitDededeCounter				ITS A SECRET TO EVERYBODY


Call order:

Match_InitMinorData
SceneLoad_InGame
StartMelee
SceneLoad_Melee (player entity structs now exist)



											+0xB0 = Pointer to Player Entity Struct
80453080	= P1 Static Player Block		80453130
80453F10	= P2 Static Player Block		80453FC0


From Player Entity Struct, +0x2C = Pointer to Player Character Data


From Player Character Data, +0x10 = Action State ID



Player Entity Struct
	|
	V		+0x2C
80C70180	->		80C701AC	Pointer = 80C701E0