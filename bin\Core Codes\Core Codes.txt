

Enable OSReport Print on Crash
Enables an on-screen crash printout (stack trace) in cases where the game crashes; very useful for debugging. If this mod is to be used, the External Frame Buffer (XFB) option in Dolphin should be enabled, and the Aux Code Regions in MCM's Code-Space Options should not be used (i.e. that region should be vanilla code). This mod will automatically be selected by default if it's found in your library. You may change this behavior by setting the "alwaysEnableCrashReports" option in the "settings.py" file to False.
<https://smashboards.com/threads/enable-osreport-print-on-crash.456513/>
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x80397878 ---- 801E0000 -> 4800020C
NTSC 1.01 --- 0x80396B98 ---- 801E0000 -> 4800020C
NTSC 1.00 --- 0x803959B4 ---- 801E0000 -> 4800020C
PAL 1.00 ---- 0x803977A0 ---- 801E0000 -> 4800020C


	-==-


Auto-Pause
Automatically pauses the game on a specific frame of a match.
Configurations:
    uint16 Target Frame = 240 # The frame to pause on
    uint16 Who = 0; 0-5 # Player index for who paused
[DRGN]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8016be60 --- 3860FFFF -> Branch

# Injects into the end of Pause_CheckButtonInputsToPause
# Should execute just once, once the scene reaches the specified frame.

# Check the scene's current frame to see if we should execute
lis r15, 0x80479D30@h		# Load scene controller address
ori r15, r15, 0x80479D30@l
lwz r14, 0x2C(r15)		# Load GX frame count
cmpwi r14, [[Target Frame]]
bne+ OrigLine

# Make the game think a player paused the match
li r3, [[Who]]
b END

OrigLine:
li r3, -1
END:
b 0


	-==-


Enter Action State On Match Start
Based on Punkline's Internal Action State Hack, and a rework of Dan Salvato's action state hack using internal player data offsets:
0x219C - new starting frame (float)
0x21A0 - new action state ID (int)
This variation of the code is designed to set animation states for P1 & P2 on match start.
Configurations:
    float Start Frame = 0.0
    uint32 Action State ID = 0
    uint16 Delay = 72 # Takes 72 (0x48) frames for two characters to leave entry platform
<https://smashboards.com/threads/internal-action-state-hack.440318/>
[Dan Salvato, Punkline, DRGN]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
## Treat non-pointer values in player data offsets as 0
1.02 ----- 0x8006b7f8 --- 280C0000 -> 2C0C0000
1.02 ----- 0x8006b7fc --- 41820010 -> 40800010
1.02 ----- 0x8006ab64 --- 280C0000 -> 2C0C0000

1.02 ----- 0x8006ab74 --- 4E800021 -> Branch

# Handle Action State ID at 0x21A0
41A00014 7D846378
3D808008 618CCFAC
7D8803A6 4E800021
00000000

1.02 ----- 0x800693cc --- FFA00890 -> Branch

# Handle start frame at 0x219C
80E3002C 800721A0
54000029 40820014
8007219C 2C000000
40810008 C027219C
FFA00890 00000000

---------- 0x801a4d9c --- 38000000 -> Branch

# Set Start Frame and Action State values for P1/P2 at match start.
# Injects into "updateFunction", just after the start of its loop.
# Will execute just once, when the scene reaches the specified frame.

# Check the scene's current frame to see if we should execute
lis r15, 0x80479D30@h		# Load scene controller address
ori r15, r15, 0x80479D30@l
lwz r14, 0x2C(r15)		# Load GX frame count
cmpwi r14, [[Delay]]
bne+	OrigLine

# Set Start Frame and Action State IDs for both players
lis r14, 0x80453130@h		# Load pointer to P1 Player Entity Struct (0x80453080 + 0xB0)
lwz r14, 0x80453130@l(r14)
lwz r14, 0x2C(r14)		# Load pointer to P1 Player Character Data (at 0x2C of P1 Player Entity Struct) to r14
lis r15, [[Start Frame]]@h	# Load the frame to start on
ori r15, r15, [[Start Frame]]@l
stw r15, 0x219C(r14)		# Store start frame to 0x219C of P1 Player Character Data
li r15, [[Action State ID]]	# Load the action state ID
stw r15, 0x21A0(r14)		# Store action state ID to 0x21A0 of P1 Player Character Data

lis r14, 0x80453FC0@h		# Load pointer to P2 Player Entity Struct
lwz r14, 0x80453FC0@l(r14)
lwz r14, 0x2C(r14)		# Load pointer to P2 Player Character Data (at 0x2C of P2 Player Entity Struct) to r14
lis r15, [[Start Frame]]@h	# Load the frame to start on
ori r15, r15, [[Start Frame]]@l
stw r15, 0x219C(r14)		# Store start frame to 0x219C of P2 Player Character Data
li r15, [[Action State ID]]	# Load the action state ID
stw r15, 0x21A0(r14)		# Store action state ID to 0x21A0 of P2 Player Character Data

OrigLine:
li r0, 0	# Original code line
b 0			# Placeholder for branch back


	-==-

!
Force Jump for CSP
Simulates pressing jump for characters that need their CSP screenshot taken in the air.
[DRGN]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x80019900 --- 7c0802a6 -> Branch

# Injects into the start of ButtonPresses_CopyInputsToStruct
# Check the scene's current frame to see if we should execute
lis r15, 0x80479D30@h		# Load scene controller address
ori r15, r15, 0x80479D30@l
lwz r14, 0x2C(r15)		# Load GX frame count
cmpwi r14, 0x5C			# 72 (0x48) frames for characters to leave entry platform, + 20 for fall/land
bne+ 	OrigLine

Jump:
# Simulate pressing jump
lis r14, 0x804c1fac@h		# Load address for the input struct
ori r14, r14, 0x804c1fac@l
li r15, 0x800				# Set bit for Y button
stw r15, 0(r14)

OrigLine:
mflr	r0	# Original code line
b 0			# Placeholder for branch back


	-==-


Standard Pause Camera in Dev-Mode Match
Enables the regular VS Pause Camera in Debug Mode, plus unrestricted camera.
[DRGN]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8016cb50 ---- 41820068 -> 60000000 # Enable regular VS Pause Camera in Debug Mode
------------- 0x8000d8f4 ---- 9421FFD8 -> 4E800020 # Unrestricted camera


	-==-


Unrestricted Pause Camera
[Achilles]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x80224C24 ---- C0230040 -> 39E00000
------------- 0x8002F5B0 ---- D03F02F8 -> 91FF02F8
------------- 0x80224C44 ---- C0230048 -> 3DE04700
------------- 0x8002F5BC ---- D03F02FC -> 91FF02FC
------------- 0x80224C5C ---- EC210032 -> 3DE04700
------------- 0x8002F58C ---- D03F02E8 -> 91FF02E8
------------- 0x80224C74 ---- EC210032 -> 3DE04700
------------- 0x8002F594 ---- D03F02EC -> 91FF02EC
------------- 0x80224C8C ---- EC210032 -> 3DE04700
------------- 0x8002F5A4 ---- D03F02F4 -> 91FF02F4
------------- 0x80224CA4 ---- EC210032 -> 3DE04700
------------- 0x8002F59C ---- D03F02F0 -> 91FF02F0


	-==-


CSP Camera
Sets up the camera coordinates and background color for taking CSP screenshots. Also prevents the stage and background from being displayed.
Configurations: # To be taken from the CSP Configuration file
    uint32 X Coord = 0
    uint32 Y Coord = 0
    uint32 Z Coord = 0
    uint32 BG Color = 0xFF00FF # RGB; magenta by default
[DRGN]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -

## CameraInfo_Init
##80028d00
##80028d0c
##80028d18
##80028d3c

##80028d88
##li r0, 0xC4

##first r/w: 80028cf8

NTSC 1.02 --- 0x8002bfd8 ---- 907e0000 -> Branch

# Injecting into Camera_AdjustPosition (last function to adjust pause camera coords)
# While in the process of pausing, set custom X/Y/Z camera coords
# Load and store the X/Y/Z coords of the pause camera
lis r15, 0x80452f88@h			# Load address for storage (Cam1 X/Y/Z target)
ori r15, r15, 0x80452f88@l
lis r14, [[X Coord]]@h
ori r14, r14, [[X Coord]]@l
stw r14, 0(r15)
lis r14, [[Y Coord]]@h
ori r14, r14, [[Y Coord]]@l
stw r14, 4(r15)
lis r14, [[Z Coord]]@h
ori r14, r14, [[Z Coord]]@l
stw r14, 8(r15)

# Set background color
.set BG_Color, [[BG Color]] << 8
lis r15, 0x80452C70@h
ori r15, r15, 0x80452C70@l
lis r14, BG_Color@h
ori r14, r14, BG_Color@l
stw r14, 0(r15)

# No Stage Textures
#lis r15, 0x80453000@h
#ori r15, r15, 0x80453000@l
#lis r14, 0xC4
#stw r14, 0(r15)

# Original code line
lwz	r0, 0x0018 (sp)

# Return to normal execution
b 0x8002bfec

## Set background color
##------------- 0x801c1fb8 ---- 887d0018 ->
## Red
##.set BG_Color, [[BG Color]]
##li r3, BG_Color >> 16
##------------- 0x801c1fd0 ---- 38600000 ->
## Red
##.set BG_Color, [[BG Color]]
##li r3, BG_Color >> 16
##------------- 0x801c1fbc ---- 889d0019 ->
## Green
##.set BG_Color, [[BG Color]]
##li r3, (BG_Color >> 8 ) & 0xFF
##------------- 0x801c1fd4 ---- 38800000 ->
## Green
##.set BG_Color, [[BG Color]]
##li r3, (BG_Color >> 8 ) & 0xFF
##------------- 0x801c1fc0 ---- 88bd001a ->
## Blue
##.set BG_Color, [[BG Color]]
##li r3, BG_Color & 0xFF
##------------- 0x801c1fd8 ---- 38a00000 ->
## Blue
##.set BG_Color, [[BG Color]]
##li r3, BG_Color & 0xFF

##------------- 0x80030748 ---- 98660008 98860009 98a6000a ->

##.set BG_Color, [[BG Color]] << 8
##lis r3, BG_Color@h
##ori r3, r3, BG_Color@l
##stw r3, 8(r6)


	-==-


Action State Freeze
Freezes any character in-place once they've reached a specific frame of a specific action state. Note that the Frame ID is the first 4 digits of a frame number after converting it to a float.
Configurations:
    uint16 Action State ID = 0; 0-0x10000
    uint16 Frame ID = 0; 0-0x10000
[Achilles]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8016CDA0 ---- 9421FFD0 -> Branch

81EDC18C 81EF0020
81EF002C 820F0010
2C10[[Action State ID]] 40820018
A20F0894 2C10[[Frame ID]]
4082000C 3A000007
9A0F2219 81EF0000
81EF0008 2C0F0000
40A2FFD0 9421FFD0
48000000


	-==-

!
Static ArenaHi Malloc 
Create a permanent allocation from the top of the arena of a custom size 
Edit <static_ArenaHi_Malloc> data to change the allocation size 
[Punkline, DRGN] 
##<static_ArenaHi_Malloc> NTSC 1.02 
##000 40000  # half a megabyte 
  
NTSC 1.02 --- 80375324 ---- 93810008 -> Branch 
##lis r0, <<static_ArenaHi_Malloc>>@h 
##ori r4, r0, <<static_ArenaHi_Malloc>>@l 
##lis r0, <<static_ArenaHi_Malloc>>@h 
##ori r4, r0, <<static_ArenaHi_Malloc>>@l 
##lwz r3, 0x0(r4) 
##li r4, 4 
##bl 0x80344514 
##  
##_return: 
##stw r28, 0x0008 (sp) 
##.long 0

3C000000 60040000
80640000 38800004
bl 0x80344514
93810008 00000000


	-==-

!
Load codes.bin
[Punkline]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x803753E4 ---- 8001001C -> Branch

malloc = 0x817f8ac0 - 0x80000 
  
bl _data 
b _code 
  _data: blrl 
  0: .asciz "codes.bin" 
  .align 2 

_code:
mflr r3 
lis r0, malloc@h 
lmw r28, 0x8(sp)     # r3 = path string 
ori r4, r0, malloc@l # r4 = allocation address 
addi r5, sp, 0x0C    # r5 = returns size value 
bl 0x8001668c  # $!_load_fromDVD 

_return:
lwz r0, 0x001C (sp)
.long 0


	-==-


Modified Camera Info Flag Initialization
Allows you to specify new default flag states for the camera info struct. The default state does  
 not require developer mode to be enabled, and can still be modified freely by the usual developer  
 controller mode hotkeys.
Configurations:
    mask16 Camera Flags = 0x3004
	0x8000 : Hide players
	0x2000 : Disable Background
	0x1000 : Disable Background Particles
	0x0800 : Disable Background Lights (?)
	0x0400 : Disable Background Color Effects
	0x0010 : Enable ECB, stage geometry, and other visualizations
 	0x0008 : Enable camera facing orientation visualizations
	0x0004 : Disable Background and stage geometry display
	0x0002 : ?
	0x0001 : Disable Ground Textures (?)
[Punkline]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x80028dc8 ---- 981F039A -> Branch
 
stb r0, 0x039A (r31)
# original instruction

li r0, 0                # default base
flags: xori r0, r0, [[Camera Flags]]  
# modifiable bools at flags+2:
# + 0x8000 : hide players
# + 0x2000 : Disable Background
# + 0x1000 : Disable Background Particles
# + 0x0800 : Disable Background Lights (?)
# + 0x0400 : Disable Background Color Effects
# + 0x0010 : Enable ECB, stage geometry, and other visualizations
# + 0x0008 : Enable camera facing orientation visualizations
# + 0x0004 : Disable Background and stage geometry display
# + 0x0002 : ?
# + 0x0001 : Disable Ground Textures (?)
  
sth r0, 0x398(r31)
b 0


	-==-


Zero-G Mode
No gravity on characters.
[DRGN]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x80084dec ---- C03E005C -> FC20F890


	-==-


Disable HUD
[Achilles]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x802F6508 ---- 7C0802A6 -> 4E800020


	-==-


Disable Pause HUD
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8016cb04 ---- 4082003C -> 4800003c


	-==-


Remove Classic Mode VS Screen HUD
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 ---- 0x1835D8 ----- 4820A079 -> 60000000
-------------- 0x183790 ----- 4BFFF609 -> 60000000
-------------- 0x183694 ----- 48209FBD -> 60000000


	-==-


Debug Menu replaces Tournament Mode
- Selecting Tournament Melee in the main menu takes you to the Debug Menu instead
- Selecting Single Button Mode takes you to the Tournament Melee mode instead
(Concomitant Graphical Mod Here: http://smashboards.com/threads/326347/page-3#post-15738900)
[Magus, donny2112, SypherPhoenix]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code 
1.00 ------ 0x228F20 ---- 3800001B -> 38000006 ---- (li r0,27 -> li r0,6)
----------- 0x227F2C ---- 3800002C -> 3800001B ---- (li r0,44 -> li r0,27)

1.01 ------ 0x229A90 ---- 3800001B -> 38000006 ---- (li r0,27 -> li r0,6)
----------- 0x228A9C ---- 3800002C -> 3800001B ---- (li r0,44 -> li r0,27)

1.02 ------ 0x22A218 ---- 3800001B -> 38000006 ---- (li r0,27 -> li r0,6)
----------- 0x229224 ---- 3800002C -> 3800001B ---- (li r0,44 -> li r0,27)

PAL ------- 0x22C084 ---- 3800001E -> 38000006 ---- (li r0,30 -> li r0,6)
----------- 0x22B08C ---- 3800002F -> 3800001E ---- (li r0,74 -> li r0,30)


	-==-


16:9 Widescreen Support (Variation C)
Includes the fullscreen flashing effect fix.
This variation also fixes the position of magnifier bubbles and unstretches the HUD.
If using with Dolphin, you'll also need to change the setting, "Force 16:9" for the aspect ratio and make sure "Widescreen Hack" is OFF.
See the notes in the link below for adjustments based on your display device:
https://docs.google.com/document/d/1o_KEmsbKg4_qIm607FfewzIvChQYxayN4r1j3Uy6yX0/edit
<https://smashboards.com/threads/16-9-widescreen-w-options-for-tv-monitor-dolphin.397929/post-21611190>
[Dan Salvato, ShockSlayer (1.02), Brandondorf9999 (Old versions and PAL)]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
PAL ------- 0x1EA34 ---- 38600006 -> Branch

39C00001 38600006
60000000 48000000

----------- 0x366F8C ---- C03F0034 -> Branch

C03F0034 2C0E0001
41820024 3C004093 # <- ____XXXX
90010030 3C004040 # <- ____YYYY
90010034 C0010030
EC210032 C0010034
EC210024 39C00000
281E0000 48000000

1.01 ------ 0x1E69C ---- 38600006 -> Branch

39C00001 38600006
60000000 48000000

----------- 0x3663A8 ---- C03F0034 -> Branch

C03F0034 2C0E0001
41820024 3C004093 # <- ____XXXX
90010030 3C004040 # <- ____YYYY
90010034 C0010030
EC210032 C0010034
EC210024 39C00000
281E0000 48000000

1.02 ------ 0x1E69C ---- 38600006 -> Branch

39C00001 38600006
60000000 48000000

----------- 0x367088 ---- C03F0034 -> Branch

C03F0034 2C0E0001
41820024 3C004093 # <- ____XXXX
90010030 3C004040 # <- ____YYYY
90010034 C0010030
EC210032 C0010034
EC210024 39C00000
281E0000 48000000

1.00 ------ 0x1E61C ---- 38600006 -> Branch

39C00001 38600006
60000000 48000000

----------- 0x3651D4 ---- C03F0034 -> Branch

C03F0034 2C0E0001
41820024 3C004093 # <- ____XXXX
90010030 3C004040 # <- ____YYYY
90010034 C0010030
EC210032 C0010034
EC210024 39C00000
281E0000 48000000
