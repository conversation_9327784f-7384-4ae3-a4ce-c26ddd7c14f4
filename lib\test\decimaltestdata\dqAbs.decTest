------------------------------------------------------------------------
-- dqAbs.decTest -- decQuad absolute value, heeding sNaN              --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

dqabs001 abs '1'      -> '1'
dqabs002 abs '-1'     -> '1'
dqabs003 abs '1.00'   -> '1.00'
dqabs004 abs '-1.00'  -> '1.00'
dqabs005 abs '0'      -> '0'
dqabs006 abs '0.00'   -> '0.00'
dqabs007 abs '00.0'   -> '0.0'
dqabs008 abs '00.00'  -> '0.00'
dqabs009 abs '00'     -> '0'

dqabs010 abs '-2'     -> '2'
dqabs011 abs '2'      -> '2'
dqabs012 abs '-2.00'  -> '2.00'
dqabs013 abs '2.00'   -> '2.00'
dqabs014 abs '-0'     -> '0'
dqabs015 abs '-0.00'  -> '0.00'
dqabs016 abs '-00.0'  -> '0.0'
dqabs017 abs '-00.00' -> '0.00'
dqabs018 abs '-00'    -> '0'

dqabs020 abs '-2000000' -> '2000000'
dqabs021 abs '2000000'  -> '2000000'

dqabs030 abs '+0.1'            -> '0.1'
dqabs031 abs '-0.1'            -> '0.1'
dqabs032 abs '+0.01'           -> '0.01'
dqabs033 abs '-0.01'           -> '0.01'
dqabs034 abs '+0.001'          -> '0.001'
dqabs035 abs '-0.001'          -> '0.001'
dqabs036 abs '+0.000001'       -> '0.000001'
dqabs037 abs '-0.000001'       -> '0.000001'
dqabs038 abs '+0.000000000001' -> '1E-12'
dqabs039 abs '-0.000000000001' -> '1E-12'

-- examples from decArith
dqabs040 abs '2.1'     ->  '2.1'
dqabs041 abs '-100'    ->  '100'
dqabs042 abs '101.5'   ->  '101.5'
dqabs043 abs '-101.5'  ->  '101.5'

-- more fixed, potential LHS swaps/overlays if done by subtract 0
dqabs060 abs '-56267E-10'  -> '0.0000056267'
dqabs061 abs '-56267E-5'   -> '0.56267'
dqabs062 abs '-56267E-2'   -> '562.67'
dqabs063 abs '-56267E-1'   -> '5626.7'
dqabs065 abs '-56267E-0'   -> '56267'

-- subnormals and underflow

-- long operand tests
dqabs321 abs 1234567890123456  -> 1234567890123456
dqabs322 abs 12345678000  -> 12345678000
dqabs323 abs 1234567800   -> 1234567800
dqabs324 abs 1234567890   -> 1234567890
dqabs325 abs 1234567891   -> 1234567891
dqabs326 abs 12345678901  -> 12345678901
dqabs327 abs 1234567896   -> 1234567896

-- zeros
dqabs111 abs          0   -> 0
dqabs112 abs         -0   -> 0
dqabs113 abs       0E+6   -> 0E+6
dqabs114 abs      -0E+6   -> 0E+6
dqabs115 abs     0.0000   -> 0.0000
dqabs116 abs    -0.0000   -> 0.0000
dqabs117 abs      0E-141  -> 0E-141
dqabs118 abs     -0E-141  -> 0E-141

-- full coefficients, alternating bits
dqabs121 abs   2682682682682682682682682682682682    ->  2682682682682682682682682682682682
dqabs122 abs  -2682682682682682682682682682682682    ->  2682682682682682682682682682682682
dqabs123 abs   1341341341341341341341341341341341    ->  1341341341341341341341341341341341
dqabs124 abs  -1341341341341341341341341341341341    ->  1341341341341341341341341341341341

-- Nmax, Nmin, Ntiny
dqabs131 abs  9.999999999999999999999999999999999E+6144   ->  9.999999999999999999999999999999999E+6144
dqabs132 abs  1E-6143                                     ->  1E-6143
dqabs133 abs  1.000000000000000000000000000000000E-6143   ->  1.000000000000000000000000000000000E-6143
dqabs134 abs  1E-6176                                     ->  1E-6176 Subnormal

dqabs135 abs  -1E-6176                                    ->  1E-6176 Subnormal
dqabs136 abs  -1.000000000000000000000000000000000E-6143  ->  1.000000000000000000000000000000000E-6143
dqabs137 abs  -1E-6143                                    ->  1E-6143
dqabs138 abs  -9.999999999999999999999999999999999E+6144  ->  9.999999999999999999999999999999999E+6144

-- specials
dqabs520 abs 'Inf'    -> 'Infinity'
dqabs521 abs '-Inf'   -> 'Infinity'
dqabs522 abs   NaN    ->  NaN
dqabs523 abs  sNaN    ->  NaN   Invalid_operation
dqabs524 abs   NaN22  ->  NaN22
dqabs525 abs  sNaN33  ->  NaN33 Invalid_operation
dqabs526 abs  -NaN22  -> -NaN22
dqabs527 abs -sNaN33  -> -NaN33 Invalid_operation

-- Null tests
dqabs900 abs  # -> NaN Invalid_operation

