------------------------------------------------------------------------
-- dqInvert.decTest -- digitwise logical INVERT for decQuads          --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check (truth table)
dqinv001 invert             0 -> 1111111111111111111111111111111111
dqinv002 invert             1 -> 1111111111111111111111111111111110
dqinv003 invert            10 -> 1111111111111111111111111111111101
dqinv004 invert     111111111 -> 1111111111111111111111111000000000
dqinv005 invert     000000000 -> 1111111111111111111111111111111111
-- and at msd and msd-1
dqinv007 invert 0000000000000000000000000000000000 ->   1111111111111111111111111111111111
dqinv008 invert 1000000000000000000000000000000000 ->    111111111111111111111111111111111
dqinv009 invert 0000000000000000000000000000000000 ->   1111111111111111111111111111111111
dqinv010 invert 0100000000000000000000000000000000 ->   1011111111111111111111111111111111
dqinv011 invert 0111111111111111111111111111111111 ->   1000000000000000000000000000000000
dqinv012 invert 1111111111111111111111111111111111 ->                  0
dqinv013 invert 0011111111111111111111111111111111 ->   1100000000000000000000000000000000
dqinv014 invert 0111111111111111111111111111111111 ->   1000000000000000000000000000000000

-- Various lengths
dqinv600 invert 0111111111111111111011111111111111 ->   1000000000000000000100000000000000
dqinv601 invert 0011111111111111110101111111111111 ->   1100000000000000001010000000000000
dqinv602 invert 0101111111111111101110111111111111 ->   1010000000000000010001000000000000
dqinv603 invert 0110111111111111011111011111111111 ->   1001000000000000100000100000000000
dqinv604 invert 0111011111111110111111101111111111 ->   1000100000000001000000010000000000
dqinv605 invert 0111101111111101111111110111111111 ->   1000010000000010000000001000000000
dqinv606 invert 0111110111111011111111111011111111 ->   1000001000000100000000000100000000
dqinv607 invert 0111111011110111111111111101111111 ->   1000000100001000000000000010000000
dqinv608 invert 0111111101101111111111111110111111 ->   1000000010010000000000000001000000
dqinv609 invert 0111111110011111111111111111011111 ->   1000000001100000000000000000100000
dqinv610 invert 0111111110011111111111111111101111 ->   1000000001100000000000000000010000
dqinv611 invert 0111111101101111111111111111110111 ->   1000000010010000000000000000001000
dqinv612 invert 0111111011110111111111111111111011 ->   1000000100001000000000000000000100
dqinv613 invert 0111110111111011111111111111111101 ->   1000001000000100000000000000000010
dqinv614 invert 0111101111111101111111111111111110 ->   1000010000000010000000000000000001
dqinv615 invert 0111011111111110111111111111111111 ->   1000100000000001000000000000000000
dqinv616 invert 0110111111111111011111111111111110 ->   1001000000000000100000000000000001
dqinv617 invert 0101111111111111101111111111111101 ->   1010000000000000010000000000000010
dqinv618 invert 0011111111111111110111111111111011 ->   1100000000000000001000000000000100
dqinv619 invert 0101111111111111111011111111110111 ->   1010000000000000000100000000001000
dqinv620 invert 0110111111111111111101111111101111 ->   1001000000000000000010000000010000
dqinv621 invert 0111011111111111111110111111011111 ->   1000100000000000000001000000100000
dqinv622 invert 0111101111111111111111011110111111 ->   1000010000000000000000100001000000
dqinv623 invert 0111110111111111111111101101111111 ->   1000001000000000000000010010000000
dqinv624 invert 0111111011111111111111110011111111 ->   1000000100000000000000001100000000
dqinv625 invert 0111111101111111111111110011111111 ->   1000000010000000000000001100000000
dqinv626 invert 0111111110111111111111101101111111 ->   1000000001000000000000010010000000
dqinv627 invert 0111111111011111111111011110111111 ->   1000000000100000000000100001000000
dqinv628 invert 0111111111101111111110111111011111 ->   1000000000010000000001000000100000
dqinv629 invert 0111111111110111111101111111101111 ->   1000000000001000000010000000010000
dqinv630 invert 0111111111111011111011111111110111 ->   1000000000000100000100000000001000
dqinv631 invert 0111111111111101110111111111111011 ->   1000000000000010001000000000000100
dqinv632 invert 0111111111111110101111111111111101 ->   1000000000000001010000000000000010
dqinv633 invert 0111111111111111011111111111111110 ->   1000000000000000100000000000000001

dqinv021 invert 111111111     -> 1111111111111111111111111000000000
dqinv022 invert 111111111111  -> 1111111111111111111111000000000000
dqinv023 invert  11111111     -> 1111111111111111111111111100000000
dqinv025 invert   1111111     -> 1111111111111111111111111110000000
dqinv026 invert    111111     -> 1111111111111111111111111111000000
dqinv027 invert     11111     -> 1111111111111111111111111111100000
dqinv028 invert      1111     -> 1111111111111111111111111111110000
dqinv029 invert       111     -> 1111111111111111111111111111111000
dqinv031 invert        11     -> 1111111111111111111111111111111100
dqinv032 invert         1     -> 1111111111111111111111111111111110
dqinv033 invert 111111111111  -> 1111111111111111111111000000000000
dqinv034 invert 11111111111   -> 1111111111111111111111100000000000
dqinv035 invert 1111111111    -> 1111111111111111111111110000000000
dqinv036 invert 111111111     -> 1111111111111111111111111000000000

dqinv040 invert 011111111   -> 1111111111111111111111111100000000
dqinv041 invert 101111111   -> 1111111111111111111111111010000000
dqinv042 invert 110111111   -> 1111111111111111111111111001000000
dqinv043 invert 111011111   -> 1111111111111111111111111000100000
dqinv044 invert 111101111   -> 1111111111111111111111111000010000
dqinv045 invert 111110111   -> 1111111111111111111111111000001000
dqinv046 invert 111111011   -> 1111111111111111111111111000000100
dqinv047 invert 111111101   -> 1111111111111111111111111000000010
dqinv048 invert 111111110   -> 1111111111111111111111111000000001
dqinv049 invert 011111011   -> 1111111111111111111111111100000100
dqinv050 invert 101111101   -> 1111111111111111111111111010000010
dqinv051 invert 110111110   -> 1111111111111111111111111001000001
dqinv052 invert 111011101   -> 1111111111111111111111111000100010
dqinv053 invert 111101011   -> 1111111111111111111111111000010100
dqinv054 invert 111110111   -> 1111111111111111111111111000001000
dqinv055 invert 111101011   -> 1111111111111111111111111000010100
dqinv056 invert 111011101   -> 1111111111111111111111111000100010
dqinv057 invert 110111110   -> 1111111111111111111111111001000001
dqinv058 invert 101111101   -> 1111111111111111111111111010000010
dqinv059 invert 011111011   -> 1111111111111111111111111100000100

dqinv080 invert 1000000011111111   -> 1111111111111111110111111100000000
dqinv081 invert 0100000101111111   -> 1111111111111111111011111010000000
dqinv082 invert 0010000110111111   -> 1111111111111111111101111001000000
dqinv083 invert 0001000111011111   -> 1111111111111111111110111000100000
dqinv084 invert 0000100111101111   -> 1111111111111111111111011000010000
dqinv085 invert 0000010111110111   -> 1111111111111111111111101000001000
dqinv086 invert 0000001111111011   -> 1111111111111111111111110000000100
dqinv087 invert 0000010111111101   -> 1111111111111111111111101000000010
dqinv088 invert 0000100111111110   -> 1111111111111111111111011000000001
dqinv089 invert 0001000011111011   -> 1111111111111111111110111100000100
dqinv090 invert 0010000101111101   -> 1111111111111111111101111010000010
dqinv091 invert 0100000110111110   -> 1111111111111111111011111001000001
dqinv092 invert 1000000111011101   -> 1111111111111111110111111000100010
dqinv093 invert 0100000111101011   -> 1111111111111111111011111000010100
dqinv094 invert 0010000111110111   -> 1111111111111111111101111000001000
dqinv095 invert 0001000111101011   -> 1111111111111111111110111000010100
dqinv096 invert 0000100111011101   -> 1111111111111111111111011000100010
dqinv097 invert 0000010110111110   -> 1111111111111111111111101001000001
dqinv098 invert 0000001101111101   -> 1111111111111111111111110010000010
dqinv099 invert 0000010011111011   -> 1111111111111111111111101100000100

-- and more thorough MSD/LSD tests [8 and 9 mght be encoded differently...]
dqinv151 invert 1111111111111111111111111111111110 ->                                   1
dqinv152 invert 1111111111111111110000000000000000 ->                    1111111111111111
dqinv153 invert 1000000000000000001111111111111111 ->   111111111111111110000000000000000
dqinv154 invert 1111111111111111111000000000000000 ->                     111111111111111
dqinv155 invert 0100000000000000000111111111111111 ->  1011111111111111111000000000000000
dqinv156 invert 1011111111111111110100000000000000 ->   100000000000000001011111111111111
dqinv157 invert 1101111111111111110111111111111111 ->    10000000000000001000000000000000
dqinv158 invert 1110111111111111110011111111111111 ->     1000000000000001100000000000000

-- non-0/1 should not be accepted, nor should signs
dqinv220 invert 111111112   ->  NaN Invalid_operation
dqinv221 invert 333333333   ->  NaN Invalid_operation
dqinv222 invert 555555555   ->  NaN Invalid_operation
dqinv223 invert 777777777   ->  NaN Invalid_operation
dqinv224 invert 999999999   ->  NaN Invalid_operation
dqinv225 invert 222222222   ->  NaN Invalid_operation
dqinv226 invert 444444444   ->  NaN Invalid_operation
dqinv227 invert 666666666   ->  NaN Invalid_operation
dqinv228 invert 888888888   ->  NaN Invalid_operation
dqinv229 invert 999999999   ->  NaN Invalid_operation
dqinv230 invert 999999999   ->  NaN Invalid_operation
dqinv231 invert 999999999   ->  NaN Invalid_operation
dqinv232 invert 999999999   ->  NaN Invalid_operation
-- a few randoms
dqinv240 invert  567468689  ->  NaN Invalid_operation
dqinv241 invert  567367689  ->  NaN Invalid_operation
dqinv242 invert -631917772  ->  NaN Invalid_operation
dqinv243 invert -756253257  ->  NaN Invalid_operation
dqinv244 invert  835590149  ->  NaN Invalid_operation
-- test MSD
dqinv250 invert  2000000111000111000111000000000000 ->  NaN Invalid_operation
dqinv251 invert  3000000111000111000111000000000000 ->  NaN Invalid_operation
dqinv252 invert  4000000111000111000111000000000000 ->  NaN Invalid_operation
dqinv253 invert  5000000111000111000111000000000000 ->  NaN Invalid_operation
dqinv254 invert  6000000111000111000111000000000000 ->  NaN Invalid_operation
dqinv255 invert  7000000111000111000111000000000000 ->  NaN Invalid_operation
dqinv256 invert  8000000111000111000111000000000000 ->  NaN Invalid_operation
dqinv257 invert  9000000111000111000111000000000000 ->  NaN Invalid_operation
-- test MSD-1
dqinv270 invert  0200000111000111000111001000000000 ->  NaN Invalid_operation
dqinv271 invert  0300000111000111000111000100000000 ->  NaN Invalid_operation
dqinv272 invert  0400000111000111000111000010000000 ->  NaN Invalid_operation
dqinv273 invert  0500000111000111000111000001000000 ->  NaN Invalid_operation
dqinv274 invert  1600000111000111000111000000100000 ->  NaN Invalid_operation
dqinv275 invert  1700000111000111000111000000010000 ->  NaN Invalid_operation
dqinv276 invert  1800000111000111000111000000001000 ->  NaN Invalid_operation
dqinv277 invert  1900000111000111000111000000000100 ->  NaN Invalid_operation
-- test LSD
dqinv280 invert  0010000111000111000111000000000002 ->  NaN Invalid_operation
dqinv281 invert  0001000111000111000111000000000003 ->  NaN Invalid_operation
dqinv282 invert  0000000111000111000111100000000004 ->  NaN Invalid_operation
dqinv283 invert  0000000111000111000111010000000005 ->  NaN Invalid_operation
dqinv284 invert  1000000111000111000111001000000006 ->  NaN Invalid_operation
dqinv285 invert  1000000111000111000111000100000007 ->  NaN Invalid_operation
dqinv286 invert  1000000111000111000111000010000008 ->  NaN Invalid_operation
dqinv287 invert  1000000111000111000111000001000009 ->  NaN Invalid_operation
-- test Middie
dqinv288 invert  0010000111000111000111000020000000 ->  NaN Invalid_operation
dqinv289 invert  0001000111000111000111000030000001 ->  NaN Invalid_operation
dqinv290 invert  0000000111000111000111100040000010 ->  NaN Invalid_operation
dqinv291 invert  0000000111000111000111010050000100 ->  NaN Invalid_operation
dqinv292 invert  1000000111000111000111001060001000 ->  NaN Invalid_operation
dqinv293 invert  1000000111000111000111000170010000 ->  NaN Invalid_operation
dqinv294 invert  1000000111000111000111000080100000 ->  NaN Invalid_operation
dqinv295 invert  1000000111000111000111000091000000 ->  NaN Invalid_operation
-- signs
dqinv296 invert -1000000111000111000111000001000000  ->  NaN Invalid_operation
dqinv299 invert  1000000111000111000111000001000000  ->  111111000111000111000111110111111

-- Nmax, Nmin, Ntiny-like
dqinv341 invert  9.99999999E+2998  -> NaN Invalid_operation
dqinv342 invert  1E-2998           -> NaN Invalid_operation
dqinv343 invert  1.00000000E-2998  -> NaN Invalid_operation
dqinv344 invert  1E-2078           -> NaN Invalid_operation
dqinv345 invert  -1E-2078          -> NaN Invalid_operation
dqinv346 invert  -1.00000000E-2998 -> NaN Invalid_operation
dqinv347 invert  -1E-2998          -> NaN Invalid_operation
dqinv348 invert  -9.99999999E+2998 -> NaN Invalid_operation

-- A few other non-integers
dqinv361 invert  1.0               -> NaN Invalid_operation
dqinv362 invert  1E+1              -> NaN Invalid_operation
dqinv363 invert  0.0               -> NaN Invalid_operation
dqinv364 invert  0E+1              -> NaN Invalid_operation
dqinv365 invert  9.9               -> NaN Invalid_operation
dqinv366 invert  9E+1              -> NaN Invalid_operation

-- All Specials are in error
dqinv788 invert -Inf     -> NaN  Invalid_operation
dqinv794 invert  Inf     -> NaN  Invalid_operation
dqinv821 invert  NaN     -> NaN  Invalid_operation
dqinv841 invert  sNaN    -> NaN  Invalid_operation
-- propagating NaNs
dqinv861 invert  NaN1    -> NaN Invalid_operation
dqinv862 invert +NaN2    -> NaN Invalid_operation
dqinv863 invert  NaN3    -> NaN Invalid_operation
dqinv864 invert  NaN4    -> NaN Invalid_operation
dqinv865 invert  NaN5    -> NaN Invalid_operation
dqinv871 invert  sNaN11  -> NaN Invalid_operation
dqinv872 invert  sNaN12  -> NaN Invalid_operation
dqinv873 invert  sNaN13  -> NaN Invalid_operation
dqinv874 invert  sNaN14  -> NaN Invalid_operation
dqinv875 invert  sNaN15  -> NaN Invalid_operation
dqinv876 invert  NaN16   -> NaN Invalid_operation
dqinv881 invert +NaN25   -> NaN Invalid_operation
dqinv882 invert -NaN26   -> NaN Invalid_operation
dqinv883 invert -sNaN27  -> NaN Invalid_operation
