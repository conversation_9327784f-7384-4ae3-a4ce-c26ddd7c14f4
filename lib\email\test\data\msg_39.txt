MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="----- =_aaaaaaaaaa0"

------- =_aaaaaaaaaa0
Content-Type: multipart/mixed; boundary="----- =_aaaaaaaaaa1"
Content-ID: <<EMAIL>>

------- =_aaaaaaaaaa1
Content-Type: multipart/alternative; boundary="----- =_aaaaaaaaaa1"
Content-ID: <<EMAIL>>

------- =_aaaaaaaaaa1
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch1
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa1
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch2
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa1--

------- =_aaaaaaaaaa1
Content-Type: multipart/alternative; boundary="----- =_aaaaaaaaaa1"
Content-ID: <<EMAIL>>

------- =_aaaaaaaaaa1
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch3
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa1
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch4
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa1--

------- =_aaaaaaaaaa1
Content-Type: multipart/alternative; boundary="----- =_aaaaaaaaaa1"
Content-ID: <<EMAIL>>

------- =_aaaaaaaaaa1
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch5
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa1
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch6
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa1--

------- =_aaaaaaaaaa1--

------- =_aaaaaaaaaa0
Content-Type: text/plain; charset="us-ascii"
Content-ID: <<EMAIL>>

--
It's never too late to have a happy childhood.

------- =_aaaaaaaaaa0--
