{"category": "Core Codes", "codes": [{"name": "Arena Memory Allocation (Static ArenaHi Malloc)", "authors": ["Punkline"], "description": ["Creates a permanent allocation from the top of the arena of a custom size."], "configurations": {"Reservation Size": {"type": "uint32", "value": 0, "default": 0}, "Reservation Location": {"type": "uint32", "value": 0, "default": 0}}, "build": [{"type": "inject", "address": "80375324", "sourceFile": "Malloc.asm"}, {"type": "inject", "address": "803753E4", "sourceFile": "LoadCodes.asm"}]}]}