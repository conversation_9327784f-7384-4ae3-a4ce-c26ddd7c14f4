------------------------------------------------------------------------
-- dqEncode.decTest -- decimal sixteen-byte format testcases          --
-- Copyright (c) IBM Corporation, 2000, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
-- [Previously called decimal128.decTest]
version: 2.59

-- This set of tests is for the sixteen-byte concrete representation.
-- Its characteristics are:
--
--   1 bit  sign
--   5 bits combination field
--  12 bits exponent continuation
-- 110 bits coefficient continuation
--
-- Total exponent length 14 bits
-- Total coefficient length 114 bits (34 digits)
--
-- Elimit = 12287 (maximum encoded exponent)
-- Emax   =  6144 (largest exponent value)
-- Emin   = -6143 (smallest exponent value)
-- bias   =  6176 (subtracted from encoded exponent) = -Etiny

-- The testcases here have only exactly representable data on the
-- 'left-hand-side'; rounding from strings is tested in 'base'
-- testcase groups.

extended:    1
clamp:       1
precision:   34
rounding:    half_up
maxExponent: 6144
minExponent: -6143

-- General testcases
-- (mostly derived from the Strawman 4 document and examples)
decq001 apply   #A20780000000000000000000000003D0 -> -7.50
decq002 apply   -7.50             -> #A20780000000000000000000000003D0
-- derivative canonical plain strings
decq003 apply   #A20840000000000000000000000003D0 -> -7.50E+3
decq004 apply   -7.50E+3          -> #A20840000000000000000000000003D0
decq005 apply   #A20800000000000000000000000003D0 -> -750
decq006 apply   -750              -> #A20800000000000000000000000003D0
decq007 apply   #A207c0000000000000000000000003D0 -> -75.0
decq008 apply   -75.0             -> #A207c0000000000000000000000003D0
decq009 apply   #A20740000000000000000000000003D0 -> -0.750
decq010 apply   -0.750            -> #A20740000000000000000000000003D0
decq011 apply   #A20700000000000000000000000003D0 -> -0.0750
decq012 apply   -0.0750           -> #A20700000000000000000000000003D0
decq013 apply   #A20680000000000000000000000003D0 -> -0.000750
decq014 apply   -0.000750         -> #A20680000000000000000000000003D0
decq015 apply   #A20600000000000000000000000003D0 -> -0.00000750
decq016 apply   -0.00000750       -> #A20600000000000000000000000003D0
decq017 apply   #A205c0000000000000000000000003D0 -> -7.50E-7
decq018 apply   -7.50E-7          -> #A205c0000000000000000000000003D0

-- Normality
decq020 apply   1234567890123456789012345678901234   -> #2608134b9c1e28e56f3c127177823534
decq021 apply  -1234567890123456789012345678901234   -> #a608134b9c1e28e56f3c127177823534
decq022 apply   1111111111111111111111111111111111   -> #26080912449124491244912449124491

-- Nmax and similar
decq031 apply   9.999999999999999999999999999999999E+6144  -> #77ffcff3fcff3fcff3fcff3fcff3fcff
decq032 apply   #77ffcff3fcff3fcff3fcff3fcff3fcff -> 9.999999999999999999999999999999999E+6144
decq033 apply   1.234567890123456789012345678901234E+6144 -> #47ffd34b9c1e28e56f3c127177823534
decq034 apply   #47ffd34b9c1e28e56f3c127177823534 -> 1.234567890123456789012345678901234E+6144
-- fold-downs (more below)
decq035 apply   1.23E+6144    -> #47ffd300000000000000000000000000 Clamped
decq036 apply   #47ffd300000000000000000000000000       -> 1.230000000000000000000000000000000E+6144
decq037 apply   1E+6144       -> #47ffc000000000000000000000000000 Clamped
decq038 apply   #47ffc000000000000000000000000000       -> 1.000000000000000000000000000000000E+6144

decq051 apply   12345                   -> #220800000000000000000000000049c5
decq052 apply   #220800000000000000000000000049c5       -> 12345
decq053 apply   1234                    -> #22080000000000000000000000000534
decq054 apply   #22080000000000000000000000000534       -> 1234
decq055 apply   123                     -> #220800000000000000000000000000a3
decq056 apply   #220800000000000000000000000000a3       -> 123
decq057 apply   12                      -> #22080000000000000000000000000012
decq058 apply   #22080000000000000000000000000012       -> 12
decq059 apply   1                       -> #22080000000000000000000000000001
decq060 apply   #22080000000000000000000000000001       -> 1
decq061 apply   1.23                    -> #220780000000000000000000000000a3
decq062 apply   #220780000000000000000000000000a3       -> 1.23
decq063 apply   123.45                  -> #220780000000000000000000000049c5
decq064 apply   #220780000000000000000000000049c5       -> 123.45

-- Nmin and below
decq071 apply   1E-6143                                    -> #00084000000000000000000000000001
decq072 apply   #00084000000000000000000000000001          -> 1E-6143
decq073 apply   1.000000000000000000000000000000000E-6143  -> #04000000000000000000000000000000
decq074 apply   #04000000000000000000000000000000          -> 1.000000000000000000000000000000000E-6143
decq075 apply   1.000000000000000000000000000000001E-6143  -> #04000000000000000000000000000001
decq076 apply   #04000000000000000000000000000001          -> 1.000000000000000000000000000000001E-6143

decq077 apply   0.100000000000000000000000000000000E-6143  -> #00000800000000000000000000000000      Subnormal
decq078 apply   #00000800000000000000000000000000          -> 1.00000000000000000000000000000000E-6144  Subnormal
decq079 apply   0.000000000000000000000000000000010E-6143  -> #00000000000000000000000000000010      Subnormal
decq080 apply   #00000000000000000000000000000010          -> 1.0E-6175              Subnormal
decq081 apply   0.00000000000000000000000000000001E-6143   -> #00004000000000000000000000000001      Subnormal
decq082 apply   #00004000000000000000000000000001          -> 1E-6175                Subnormal
decq083 apply   0.000000000000000000000000000000001E-6143  -> #00000000000000000000000000000001      Subnormal
decq084 apply   #00000000000000000000000000000001          -> 1E-6176                 Subnormal

-- underflows cannot be tested for simple copies, check edge cases
decq090 apply   1e-6176                  -> #00000000000000000000000000000001  Subnormal
decq100 apply   999999999999999999999999999999999e-6176 -> #00000ff3fcff3fcff3fcff3fcff3fcff  Subnormal

-- same again, negatives
-- Nmax and similar
decq122 apply  -9.999999999999999999999999999999999E+6144  -> #f7ffcff3fcff3fcff3fcff3fcff3fcff
decq123 apply   #f7ffcff3fcff3fcff3fcff3fcff3fcff -> -9.999999999999999999999999999999999E+6144
decq124 apply  -1.234567890123456789012345678901234E+6144 -> #c7ffd34b9c1e28e56f3c127177823534
decq125 apply   #c7ffd34b9c1e28e56f3c127177823534 -> -1.234567890123456789012345678901234E+6144
-- fold-downs (more below)
decq130 apply  -1.23E+6144    -> #c7ffd300000000000000000000000000 Clamped
decq131 apply   #c7ffd300000000000000000000000000       -> -1.230000000000000000000000000000000E+6144
decq132 apply  -1E+6144       -> #c7ffc000000000000000000000000000 Clamped
decq133 apply   #c7ffc000000000000000000000000000       -> -1.000000000000000000000000000000000E+6144

decq151 apply  -12345                   -> #a20800000000000000000000000049c5
decq152 apply   #a20800000000000000000000000049c5       -> -12345
decq153 apply  -1234                    -> #a2080000000000000000000000000534
decq154 apply   #a2080000000000000000000000000534       -> -1234
decq155 apply  -123                     -> #a20800000000000000000000000000a3
decq156 apply   #a20800000000000000000000000000a3       -> -123
decq157 apply  -12                      -> #a2080000000000000000000000000012
decq158 apply   #a2080000000000000000000000000012       -> -12
decq159 apply  -1                       -> #a2080000000000000000000000000001
decq160 apply   #a2080000000000000000000000000001       -> -1
decq161 apply  -1.23                    -> #a20780000000000000000000000000a3
decq162 apply   #a20780000000000000000000000000a3       -> -1.23
decq163 apply  -123.45                  -> #a20780000000000000000000000049c5
decq164 apply   #a20780000000000000000000000049c5       -> -123.45

-- Nmin and below
decq171 apply  -1E-6143                                    -> #80084000000000000000000000000001
decq172 apply   #80084000000000000000000000000001          -> -1E-6143
decq173 apply  -1.000000000000000000000000000000000E-6143  -> #84000000000000000000000000000000
decq174 apply   #84000000000000000000000000000000          -> -1.000000000000000000000000000000000E-6143
decq175 apply  -1.000000000000000000000000000000001E-6143  -> #84000000000000000000000000000001
decq176 apply   #84000000000000000000000000000001          -> -1.000000000000000000000000000000001E-6143

decq177 apply  -0.100000000000000000000000000000000E-6143  -> #80000800000000000000000000000000      Subnormal
decq178 apply   #80000800000000000000000000000000          -> -1.00000000000000000000000000000000E-6144  Subnormal
decq179 apply  -0.000000000000000000000000000000010E-6143  -> #80000000000000000000000000000010      Subnormal
decq180 apply   #80000000000000000000000000000010          -> -1.0E-6175              Subnormal
decq181 apply  -0.00000000000000000000000000000001E-6143   -> #80004000000000000000000000000001      Subnormal
decq182 apply   #80004000000000000000000000000001          -> -1E-6175                Subnormal
decq183 apply  -0.000000000000000000000000000000001E-6143  -> #80000000000000000000000000000001      Subnormal
decq184 apply   #80000000000000000000000000000001          -> -1E-6176                 Subnormal

-- underflow edge cases
decq190 apply   -1e-6176                  -> #80000000000000000000000000000001  Subnormal
decq200 apply   -999999999999999999999999999999999e-6176 -> #80000ff3fcff3fcff3fcff3fcff3fcff  Subnormal

-- zeros
decq400 apply   0E-8000                 -> #00000000000000000000000000000000  Clamped
decq401 apply   0E-6177                 -> #00000000000000000000000000000000  Clamped
decq402 apply   0E-6176                 -> #00000000000000000000000000000000
decq403 apply   #00000000000000000000000000000000       -> 0E-6176
decq404 apply   0.000000000000000000000000000000000E-6143  -> #00000000000000000000000000000000
decq405 apply   #00000000000000000000000000000000       -> 0E-6176
decq406 apply   0E-2                    -> #22078000000000000000000000000000
decq407 apply   #22078000000000000000000000000000       -> 0.00
decq408 apply   0                       -> #22080000000000000000000000000000
decq409 apply   #22080000000000000000000000000000       -> 0
decq410 apply   0E+3                    -> #2208c000000000000000000000000000
decq411 apply   #2208c000000000000000000000000000       -> 0E+3
decq412 apply   0E+6111                 -> #43ffc000000000000000000000000000
decq413 apply   #43ffc000000000000000000000000000       -> 0E+6111
-- clamped zeros...
decq414 apply   0E+6112                 -> #43ffc000000000000000000000000000  Clamped
decq415 apply   #43ffc000000000000000000000000000       -> 0E+6111
decq416 apply   0E+6144                 -> #43ffc000000000000000000000000000  Clamped
decq417 apply   #43ffc000000000000000000000000000       -> 0E+6111
decq418 apply   0E+8000                 -> #43ffc000000000000000000000000000  Clamped
decq419 apply   #43ffc000000000000000000000000000       -> 0E+6111

-- negative zeros
decq420 apply  -0E-8000                 -> #80000000000000000000000000000000  Clamped
decq421 apply  -0E-6177                 -> #80000000000000000000000000000000  Clamped
decq422 apply  -0E-6176                 -> #80000000000000000000000000000000
decq423 apply   #80000000000000000000000000000000       -> -0E-6176
decq424 apply  -0.000000000000000000000000000000000E-6143  -> #80000000000000000000000000000000
decq425 apply   #80000000000000000000000000000000       -> -0E-6176
decq426 apply  -0E-2                    -> #a2078000000000000000000000000000
decq427 apply   #a2078000000000000000000000000000       -> -0.00
decq428 apply  -0                       -> #a2080000000000000000000000000000
decq429 apply   #a2080000000000000000000000000000       -> -0
decq430 apply  -0E+3                    -> #a208c000000000000000000000000000
decq431 apply   #a208c000000000000000000000000000       -> -0E+3
decq432 apply  -0E+6111                 -> #c3ffc000000000000000000000000000
decq433 apply   #c3ffc000000000000000000000000000       -> -0E+6111
-- clamped zeros...
decq434 apply  -0E+6112                 -> #c3ffc000000000000000000000000000  Clamped
decq435 apply   #c3ffc000000000000000000000000000       -> -0E+6111
decq436 apply  -0E+6144                 -> #c3ffc000000000000000000000000000  Clamped
decq437 apply   #c3ffc000000000000000000000000000       -> -0E+6111
decq438 apply  -0E+8000                 -> #c3ffc000000000000000000000000000  Clamped
decq439 apply   #c3ffc000000000000000000000000000       -> -0E+6111

-- exponent lengths
decq440 apply   #22080000000000000000000000000007       -> 7
decq441 apply   7 -> #22080000000000000000000000000007
decq442 apply   #220a4000000000000000000000000007       -> 7E+9
decq443 apply   7E+9 -> #220a4000000000000000000000000007
decq444 apply   #2220c000000000000000000000000007       -> 7E+99
decq445 apply   7E+99 -> #2220c000000000000000000000000007
decq446 apply   #2301c000000000000000000000000007       -> 7E+999
decq447 apply   7E+999 -> #2301c000000000000000000000000007
decq448 apply   #43e3c000000000000000000000000007       -> 7E+5999
decq449 apply   7E+5999 -> #43e3c000000000000000000000000007

-- Specials
decq500 apply   Infinity                          -> #78000000000000000000000000000000
decq501 apply   #78787878787878787878787878787878 -> #78000000000000000000000000000000
decq502 apply   #78000000000000000000000000000000 -> Infinity
decq503 apply   #79797979797979797979797979797979 -> #78000000000000000000000000000000
decq504 apply   #79000000000000000000000000000000 -> Infinity
decq505 apply   #7a7a7a7a7a7a7a7a7a7a7a7a7a7a7a7a -> #78000000000000000000000000000000
decq506 apply   #7a000000000000000000000000000000 -> Infinity
decq507 apply   #7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b -> #78000000000000000000000000000000
decq508 apply   #7b000000000000000000000000000000 -> Infinity

decq509 apply   NaN                               -> #7c000000000000000000000000000000
decq510 apply   #7c7c7c7c7c7c7c7c7c7c7c7c7c7c7c7c -> #7c003c7c7c7c7c7c7c7c7c7c7c7c7c7c
decq511 apply   #7c000000000000000000000000000000 -> NaN
decq512 apply   #7d7d7d7d7d7d7d7d7d7d7d7d7d7d7d7d -> #7c003d7d7d7d7d7d7d7d7d7d7d7d7d7d
decq513 apply   #7d000000000000000000000000000000 -> NaN
decq514 apply   #7e7e7e7e7e7e7e7e7e7e7e7e7e7e7e7e -> #7e003e7e7c7e7e7e7e7c7e7e7e7e7c7e
decq515 apply   #7e000000000000000000000000000000 -> sNaN
decq516 apply   #7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f -> #7e003f7f7c7f7f7f7f7c7f7f7f7f7c7f
decq517 apply   #7f000000000000000000000000000000 -> sNaN
decq518 apply   #7fffffffffffffffffffffffffffffff -> sNaN999999999999999999999999999999999
decq519 apply   #7fffffffffffffffffffffffffffffff -> #7e000ff3fcff3fcff3fcff3fcff3fcff

decq520 apply   -Infinity                         -> #f8000000000000000000000000000000
decq521 apply   #f8787878787878787878787878787878 -> #f8000000000000000000000000000000
decq522 apply   #f8000000000000000000000000000000 -> -Infinity
decq523 apply   #f9797979797979797979797979797979 -> #f8000000000000000000000000000000
decq524 apply   #f9000000000000000000000000000000 -> -Infinity
decq525 apply   #fa7a7a7a7a7a7a7a7a7a7a7a7a7a7a7a -> #f8000000000000000000000000000000
decq526 apply   #fa000000000000000000000000000000 -> -Infinity
decq527 apply   #fb7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b -> #f8000000000000000000000000000000
decq528 apply   #fb000000000000000000000000000000 -> -Infinity

decq529 apply   -NaN                              -> #fc000000000000000000000000000000
decq530 apply   #fc7c7c7c7c7c7c7c7c7c7c7c7c7c7c7c -> #fc003c7c7c7c7c7c7c7c7c7c7c7c7c7c
decq531 apply   #fc000000000000000000000000000000 -> -NaN
decq532 apply   #fd7d7d7d7d7d7d7d7d7d7d7d7d7d7d7d -> #fc003d7d7d7d7d7d7d7d7d7d7d7d7d7d
decq533 apply   #fd000000000000000000000000000000 -> -NaN
decq534 apply   #fe7e7e7e7e7e7e7e7e7e7e7e7e7e7e7e -> #fe003e7e7c7e7e7e7e7c7e7e7e7e7c7e
decq535 apply   #fe000000000000000000000000000000 -> -sNaN
decq536 apply   #ff7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f -> #fe003f7f7c7f7f7f7f7c7f7f7f7f7c7f
decq537 apply   #ff000000000000000000000000000000 -> -sNaN
decq538 apply   #ffffffffffffffffffffffffffffffff -> -sNaN999999999999999999999999999999999
decq539 apply   #ffffffffffffffffffffffffffffffff -> #fe000ff3fcff3fcff3fcff3fcff3fcff

decq540 apply   NaN               -> #7c000000000000000000000000000000
decq541 apply   NaN0              -> #7c000000000000000000000000000000
decq542 apply   NaN1              -> #7c000000000000000000000000000001
decq543 apply   NaN12             -> #7c000000000000000000000000000012
decq544 apply   NaN79             -> #7c000000000000000000000000000079
decq545 apply   NaN12345          -> #7c0000000000000000000000000049c5
decq546 apply   NaN123456         -> #7c000000000000000000000000028e56
decq547 apply   NaN799799         -> #7c0000000000000000000000000f7fdf
decq548 apply   NaN799799799799799799799799799799799  -> #7c003dff7fdff7fdff7fdff7fdff7fdf
decq549 apply   NaN999999999999999999999999999999999  -> #7c000ff3fcff3fcff3fcff3fcff3fcff
decq550 apply     9999999999999999999999999999999999  -> #6e080ff3fcff3fcff3fcff3fcff3fcff

-- fold-down full sequence
decq601 apply   1E+6144                 -> #47ffc000000000000000000000000000 Clamped
decq602 apply   #47ffc000000000000000000000000000       -> 1.000000000000000000000000000000000E+6144
decq603 apply   1E+6143                 -> #43ffc800000000000000000000000000 Clamped
decq604 apply   #43ffc800000000000000000000000000       -> 1.00000000000000000000000000000000E+6143
decq605 apply   1E+6142                 -> #43ffc100000000000000000000000000 Clamped
decq606 apply   #43ffc100000000000000000000000000       -> 1.0000000000000000000000000000000E+6142
decq607 apply   1E+6141                 -> #43ffc010000000000000000000000000 Clamped
decq608 apply   #43ffc010000000000000000000000000       -> 1.000000000000000000000000000000E+6141
decq609 apply   1E+6140                 -> #43ffc002000000000000000000000000 Clamped
decq610 apply   #43ffc002000000000000000000000000       -> 1.00000000000000000000000000000E+6140
decq611 apply   1E+6139                 -> #43ffc000400000000000000000000000 Clamped
decq612 apply   #43ffc000400000000000000000000000       -> 1.0000000000000000000000000000E+6139
decq613 apply   1E+6138                 -> #43ffc000040000000000000000000000 Clamped
decq614 apply   #43ffc000040000000000000000000000       -> 1.000000000000000000000000000E+6138
decq615 apply   1E+6137                 -> #43ffc000008000000000000000000000 Clamped
decq616 apply   #43ffc000008000000000000000000000       -> 1.00000000000000000000000000E+6137
decq617 apply   1E+6136                 -> #43ffc000001000000000000000000000 Clamped
decq618 apply   #43ffc000001000000000000000000000       -> 1.0000000000000000000000000E+6136
decq619 apply   1E+6135                 -> #43ffc000000100000000000000000000 Clamped
decq620 apply   #43ffc000000100000000000000000000       -> 1.000000000000000000000000E+6135
decq621 apply   1E+6134                 -> #43ffc000000020000000000000000000 Clamped
decq622 apply   #43ffc000000020000000000000000000       -> 1.00000000000000000000000E+6134
decq623 apply   1E+6133                 -> #43ffc000000004000000000000000000 Clamped
decq624 apply   #43ffc000000004000000000000000000       -> 1.0000000000000000000000E+6133
decq625 apply   1E+6132                 -> #43ffc000000000400000000000000000 Clamped
decq626 apply   #43ffc000000000400000000000000000       -> 1.000000000000000000000E+6132
decq627 apply   1E+6131                 -> #43ffc000000000080000000000000000 Clamped
decq628 apply   #43ffc000000000080000000000000000       -> 1.00000000000000000000E+6131
decq629 apply   1E+6130                 -> #43ffc000000000010000000000000000 Clamped
decq630 apply   #43ffc000000000010000000000000000       -> 1.0000000000000000000E+6130
decq631 apply   1E+6129                 -> #43ffc000000000001000000000000000 Clamped
decq632 apply   #43ffc000000000001000000000000000       -> 1.000000000000000000E+6129
decq633 apply   1E+6128                 -> #43ffc000000000000200000000000000 Clamped
decq634 apply   #43ffc000000000000200000000000000       -> 1.00000000000000000E+6128
decq635 apply   1E+6127                 -> #43ffc000000000000040000000000000 Clamped
decq636 apply   #43ffc000000000000040000000000000       -> 1.0000000000000000E+6127
decq637 apply   1E+6126                 -> #43ffc000000000000004000000000000 Clamped
decq638 apply   #43ffc000000000000004000000000000       -> 1.000000000000000E+6126
decq639 apply   1E+6125                 -> #43ffc000000000000000800000000000 Clamped
decq640 apply   #43ffc000000000000000800000000000       -> 1.00000000000000E+6125
decq641 apply   1E+6124                 -> #43ffc000000000000000100000000000 Clamped
decq642 apply   #43ffc000000000000000100000000000       -> 1.0000000000000E+6124
decq643 apply   1E+6123                 -> #43ffc000000000000000010000000000 Clamped
decq644 apply   #43ffc000000000000000010000000000       -> 1.000000000000E+6123
decq645 apply   1E+6122                 -> #43ffc000000000000000002000000000 Clamped
decq646 apply   #43ffc000000000000000002000000000       -> 1.00000000000E+6122
decq647 apply   1E+6121                 -> #43ffc000000000000000000400000000 Clamped
decq648 apply   #43ffc000000000000000000400000000       -> 1.0000000000E+6121
decq649 apply   1E+6120                 -> #43ffc000000000000000000040000000 Clamped
decq650 apply   #43ffc000000000000000000040000000       -> 1.000000000E+6120
decq651 apply   1E+6119                 -> #43ffc000000000000000000008000000 Clamped
decq652 apply   #43ffc000000000000000000008000000       -> 1.00000000E+6119
decq653 apply   1E+6118                 -> #43ffc000000000000000000001000000 Clamped
decq654 apply   #43ffc000000000000000000001000000       -> 1.0000000E+6118
decq655 apply   1E+6117                 -> #43ffc000000000000000000000100000 Clamped
decq656 apply   #43ffc000000000000000000000100000       -> 1.000000E+6117
decq657 apply   1E+6116                 -> #43ffc000000000000000000000020000 Clamped
decq658 apply   #43ffc000000000000000000000020000       -> 1.00000E+6116
decq659 apply   1E+6115                 -> #43ffc000000000000000000000004000 Clamped
decq660 apply   #43ffc000000000000000000000004000       -> 1.0000E+6115
decq661 apply   1E+6114                 -> #43ffc000000000000000000000000400 Clamped
decq662 apply   #43ffc000000000000000000000000400       -> 1.000E+6114
decq663 apply   1E+6113                 -> #43ffc000000000000000000000000080 Clamped
decq664 apply   #43ffc000000000000000000000000080       -> 1.00E+6113
decq665 apply   1E+6112                 -> #43ffc000000000000000000000000010 Clamped
decq666 apply   #43ffc000000000000000000000000010       -> 1.0E+6112
decq667 apply   1E+6111                 -> #43ffc000000000000000000000000001
decq668 apply   #43ffc000000000000000000000000001       -> 1E+6111
decq669 apply   1E+6110                 -> #43ff8000000000000000000000000001
decq670 apply   #43ff8000000000000000000000000001       -> 1E+6110

-- Selected DPD codes
decq700 apply   #22080000000000000000000000000000       -> 0
decq701 apply   #22080000000000000000000000000009       -> 9
decq702 apply   #22080000000000000000000000000010       -> 10
decq703 apply   #22080000000000000000000000000019       -> 19
decq704 apply   #22080000000000000000000000000020       -> 20
decq705 apply   #22080000000000000000000000000029       -> 29
decq706 apply   #22080000000000000000000000000030       -> 30
decq707 apply   #22080000000000000000000000000039       -> 39
decq708 apply   #22080000000000000000000000000040       -> 40
decq709 apply   #22080000000000000000000000000049       -> 49
decq710 apply   #22080000000000000000000000000050       -> 50
decq711 apply   #22080000000000000000000000000059       -> 59
decq712 apply   #22080000000000000000000000000060       -> 60
decq713 apply   #22080000000000000000000000000069       -> 69
decq714 apply   #22080000000000000000000000000070       -> 70
decq715 apply   #22080000000000000000000000000071       -> 71
decq716 apply   #22080000000000000000000000000072       -> 72
decq717 apply   #22080000000000000000000000000073       -> 73
decq718 apply   #22080000000000000000000000000074       -> 74
decq719 apply   #22080000000000000000000000000075       -> 75
decq720 apply   #22080000000000000000000000000076       -> 76
decq721 apply   #22080000000000000000000000000077       -> 77
decq722 apply   #22080000000000000000000000000078       -> 78
decq723 apply   #22080000000000000000000000000079       -> 79

decq730 apply   #2208000000000000000000000000029e       -> 994
decq731 apply   #2208000000000000000000000000029f       -> 995
decq732 apply   #220800000000000000000000000002a0       -> 520
decq733 apply   #220800000000000000000000000002a1       -> 521

-- DPD: one of each of the huffman groups
decq740 apply   #220800000000000000000000000003f7       -> 777
decq741 apply   #220800000000000000000000000003f8       -> 778
decq742 apply   #220800000000000000000000000003eb       -> 787
decq743 apply   #2208000000000000000000000000037d       -> 877
decq744 apply   #2208000000000000000000000000039f       -> 997
decq745 apply   #220800000000000000000000000003bf       -> 979
decq746 apply   #220800000000000000000000000003df       -> 799
decq747 apply   #2208000000000000000000000000006e       -> 888


-- DPD all-highs cases (includes the 24 redundant codes)
decq750 apply   #2208000000000000000000000000006e       -> 888
decq751 apply   #2208000000000000000000000000016e       -> 888
decq752 apply   #2208000000000000000000000000026e       -> 888
decq753 apply   #2208000000000000000000000000036e       -> 888
decq754 apply   #2208000000000000000000000000006f       -> 889
decq755 apply   #2208000000000000000000000000016f       -> 889
decq756 apply   #2208000000000000000000000000026f       -> 889
decq757 apply   #2208000000000000000000000000036f       -> 889

decq760 apply   #2208000000000000000000000000007e       -> 898
decq761 apply   #2208000000000000000000000000017e       -> 898
decq762 apply   #2208000000000000000000000000027e       -> 898
decq763 apply   #2208000000000000000000000000037e       -> 898
decq764 apply   #2208000000000000000000000000007f       -> 899
decq765 apply   #2208000000000000000000000000017f       -> 899
decq766 apply   #2208000000000000000000000000027f       -> 899
decq767 apply   #2208000000000000000000000000037f       -> 899

decq770 apply   #220800000000000000000000000000ee       -> 988
decq771 apply   #220800000000000000000000000001ee       -> 988
decq772 apply   #220800000000000000000000000002ee       -> 988
decq773 apply   #220800000000000000000000000003ee       -> 988
decq774 apply   #220800000000000000000000000000ef       -> 989
decq775 apply   #220800000000000000000000000001ef       -> 989
decq776 apply   #220800000000000000000000000002ef       -> 989
decq777 apply   #220800000000000000000000000003ef       -> 989

decq780 apply   #220800000000000000000000000000fe       -> 998
decq781 apply   #220800000000000000000000000001fe       -> 998
decq782 apply   #220800000000000000000000000002fe       -> 998
decq783 apply   #220800000000000000000000000003fe       -> 998
decq784 apply   #220800000000000000000000000000ff       -> 999
decq785 apply   #220800000000000000000000000001ff       -> 999
decq786 apply   #220800000000000000000000000002ff       -> 999
decq787 apply   #220800000000000000000000000003ff       -> 999

-- Miscellaneous (testers' queries, etc.)

decq790 apply   #2208000000000000000000000000c000       -> 30000
decq791 apply   #22080000000000000000000000007800       -> 890000
decq792 apply   30000 -> #2208000000000000000000000000c000
decq793 apply   890000 -> #22080000000000000000000000007800

-- values around [u]int32 edges (zeros done earlier)
decq800 apply -2147483646  -> #a208000000000000000000008c78af46
decq801 apply -2147483647  -> #a208000000000000000000008c78af47
decq802 apply -2147483648  -> #a208000000000000000000008c78af48
decq803 apply -2147483649  -> #a208000000000000000000008c78af49
decq804 apply  2147483646  -> #2208000000000000000000008c78af46
decq805 apply  2147483647  -> #2208000000000000000000008c78af47
decq806 apply  2147483648  -> #2208000000000000000000008c78af48
decq807 apply  2147483649  -> #2208000000000000000000008c78af49
decq808 apply  4294967294  -> #22080000000000000000000115afb55a
decq809 apply  4294967295  -> #22080000000000000000000115afb55b
decq810 apply  4294967296  -> #22080000000000000000000115afb57a
decq811 apply  4294967297  -> #22080000000000000000000115afb57b

decq820 apply  #a208000000000000000000008c78af46 -> -2147483646
decq821 apply  #a208000000000000000000008c78af47 -> -2147483647
decq822 apply  #a208000000000000000000008c78af48 -> -2147483648
decq823 apply  #a208000000000000000000008c78af49 -> -2147483649
decq824 apply  #2208000000000000000000008c78af46 ->  2147483646
decq825 apply  #2208000000000000000000008c78af47 ->  2147483647
decq826 apply  #2208000000000000000000008c78af48 ->  2147483648
decq827 apply  #2208000000000000000000008c78af49 ->  2147483649
decq828 apply  #22080000000000000000000115afb55a ->  4294967294
decq829 apply  #22080000000000000000000115afb55b ->  4294967295
decq830 apply  #22080000000000000000000115afb57a ->  4294967296
decq831 apply  #22080000000000000000000115afb57b ->  4294967297

-- VG testcase
decq840 apply    #2080000000000000F294000000172636 -> 8.81125000000001349436E-1548
decq841 apply    #20800000000000008000000000000000 -> 8.000000000000000000E-1550
decq842 apply    #1EF98490000000010F6E4E0000000000 -> 7.049000000000010795488000000000000E-3097
decq843 multiply #20800000000000008000000000000000 #2080000000000000F294000000172636 -> #1EF98490000000010F6E4E0000000000 Rounded

