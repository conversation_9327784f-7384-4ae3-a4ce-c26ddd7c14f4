------------------------------------------------------------------------
-- xor.decTest -- digitwise logical XOR                               --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 999
minExponent: -999

-- Sanity check (truth table)
xorx001 xor             0    0 ->    0
xorx002 xor             0    1 ->    1
xorx003 xor             1    0 ->    1
xorx004 xor             1    1 ->    0
xorx005 xor          1100 1010 ->  110
xorx006 xor          1111   10 -> 1101
-- and at msd and msd-1
xorx010 xor 000000000 000000000 ->           0
xorx011 xor 000000000 100000000 ->   100000000
xorx012 xor 100000000 000000000 ->   100000000
xorx013 xor 100000000 100000000 ->           0
xorx014 xor 000000000 000000000 ->           0
xorx015 xor 000000000 010000000 ->    10000000
xorx016 xor 010000000 000000000 ->    10000000
xorx017 xor 010000000 010000000 ->           0

-- Various lengths
--        123456789     123456789      123456789
xorx021 xor 111111111     111111111  ->  0
xorx022 xor 111111111111  111111111  ->  0
xorx023 xor  11111111      11111111  ->  0
xorx025 xor   1111111       1111111  ->  0
xorx026 xor    111111        111111  ->  0
xorx027 xor     11111         11111  ->  0
xorx028 xor      1111          1111  ->  0
xorx029 xor       111           111  ->  0
xorx031 xor        11            11  ->  0
xorx032 xor         1             1  ->  0
xorx033 xor 111111111111 1111111111  ->  0
xorx034 xor 11111111111 11111111111  ->  0
xorx035 xor 1111111111 111111111111  ->  0
xorx036 xor 111111111 1111111111111  ->  0

xorx040 xor 111111111  111111111111  ->  0
xorx041 xor  11111111  111111111111  ->  100000000
xorx042 xor  11111111     111111111  ->  100000000
xorx043 xor   1111111     100000010  ->  101111101
xorx044 xor    111111     100000100  ->  100111011
xorx045 xor     11111     100001000  ->  100010111
xorx046 xor      1111     100010000  ->  100011111
xorx047 xor       111     100100000  ->  100100111
xorx048 xor        11     101000000  ->  101000011
xorx049 xor         1     110000000  ->  110000001

xorx050 xor 1111111111  1  ->  111111110
xorx051 xor  111111111  1  ->  111111110
xorx052 xor   11111111  1  ->  11111110
xorx053 xor    1111111  1  ->  1111110
xorx054 xor     111111  1  ->  111110
xorx055 xor      11111  1  ->  11110
xorx056 xor       1111  1  ->  1110
xorx057 xor        111  1  ->  110
xorx058 xor         11  1  ->  10
xorx059 xor          1  1  ->  0

xorx060 xor 1111111111  0  ->  111111111
xorx061 xor  111111111  0  ->  111111111
xorx062 xor   11111111  0  ->  11111111
xorx063 xor    1111111  0  ->  1111111
xorx064 xor     111111  0  ->  111111
xorx065 xor      11111  0  ->  11111
xorx066 xor       1111  0  ->  1111
xorx067 xor        111  0  ->  111
xorx068 xor         11  0  ->  11
xorx069 xor          1  0  ->  1

xorx070 xor 1  1111111111  ->  111111110
xorx071 xor 1   111111111  ->  111111110
xorx072 xor 1    11111111  ->  11111110
xorx073 xor 1     1111111  ->  1111110
xorx074 xor 1      111111  ->  111110
xorx075 xor 1       11111  ->  11110
xorx076 xor 1        1111  ->  1110
xorx077 xor 1         111  ->  110
xorx078 xor 1          11  ->  10
xorx079 xor 1           1  ->  0

xorx080 xor 0  1111111111  ->  111111111
xorx081 xor 0   111111111  ->  111111111
xorx082 xor 0    11111111  ->  11111111
xorx083 xor 0     1111111  ->  1111111
xorx084 xor 0      111111  ->  111111
xorx085 xor 0       11111  ->  11111
xorx086 xor 0        1111  ->  1111
xorx087 xor 0         111  ->  111
xorx088 xor 0          11  ->  11
xorx089 xor 0           1  ->  1

xorx090 xor 011111111  111101111  ->  100010000
xorx091 xor 101111111  111101111  ->   10010000
xorx092 xor 110111111  111101111  ->    1010000
xorx093 xor 111011111  111101111  ->     110000
xorx094 xor 111101111  111101111  ->          0
xorx095 xor 111110111  111101111  ->      11000
xorx096 xor 111111011  111101111  ->      10100
xorx097 xor 111111101  111101111  ->      10010
xorx098 xor 111111110  111101111  ->      10001

xorx100 xor 111101111  011111111  ->  100010000
xorx101 xor 111101111  101111111  ->   10010000
xorx102 xor 111101111  110111111  ->    1010000
xorx103 xor 111101111  111011111  ->     110000
xorx104 xor 111101111  111101111  ->          0
xorx105 xor 111101111  111110111  ->      11000
xorx106 xor 111101111  111111011  ->      10100
xorx107 xor 111101111  111111101  ->      10010
xorx108 xor 111101111  111111110  ->      10001

-- non-0/1 should not be accepted, nor should signs
xorx220 xor 111111112  111111111  ->  NaN Invalid_operation
xorx221 xor 333333333  333333333  ->  NaN Invalid_operation
xorx222 xor 555555555  555555555  ->  NaN Invalid_operation
xorx223 xor 777777777  777777777  ->  NaN Invalid_operation
xorx224 xor 999999999  999999999  ->  NaN Invalid_operation
xorx225 xor 222222222  999999999  ->  NaN Invalid_operation
xorx226 xor 444444444  999999999  ->  NaN Invalid_operation
xorx227 xor 666666666  999999999  ->  NaN Invalid_operation
xorx228 xor 888888888  999999999  ->  NaN Invalid_operation
xorx229 xor 999999999  222222222  ->  NaN Invalid_operation
xorx230 xor 999999999  444444444  ->  NaN Invalid_operation
xorx231 xor 999999999  666666666  ->  NaN Invalid_operation
xorx232 xor 999999999  888888888  ->  NaN Invalid_operation
-- a few randoms
xorx240 xor  567468689 -934981942 ->  NaN Invalid_operation
xorx241 xor  567367689  934981942 ->  NaN Invalid_operation
xorx242 xor -631917772 -706014634 ->  NaN Invalid_operation
xorx243 xor -756253257  138579234 ->  NaN Invalid_operation
xorx244 xor  835590149  567435400 ->  NaN Invalid_operation
-- test MSD
xorx250 xor  200000000 100000000 ->  NaN Invalid_operation
xorx251 xor  700000000 100000000 ->  NaN Invalid_operation
xorx252 xor  800000000 100000000 ->  NaN Invalid_operation
xorx253 xor  900000000 100000000 ->  NaN Invalid_operation
xorx254 xor  200000000 000000000 ->  NaN Invalid_operation
xorx255 xor  700000000 000000000 ->  NaN Invalid_operation
xorx256 xor  800000000 000000000 ->  NaN Invalid_operation
xorx257 xor  900000000 000000000 ->  NaN Invalid_operation
xorx258 xor  100000000 200000000 ->  NaN Invalid_operation
xorx259 xor  100000000 700000000 ->  NaN Invalid_operation
xorx260 xor  100000000 800000000 ->  NaN Invalid_operation
xorx261 xor  100000000 900000000 ->  NaN Invalid_operation
xorx262 xor  000000000 200000000 ->  NaN Invalid_operation
xorx263 xor  000000000 700000000 ->  NaN Invalid_operation
xorx264 xor  000000000 800000000 ->  NaN Invalid_operation
xorx265 xor  000000000 900000000 ->  NaN Invalid_operation
-- test MSD-1
xorx270 xor  020000000 100000000 ->  NaN Invalid_operation
xorx271 xor  070100000 100000000 ->  NaN Invalid_operation
xorx272 xor  080010000 100000001 ->  NaN Invalid_operation
xorx273 xor  090001000 100000010 ->  NaN Invalid_operation
xorx274 xor  100000100 020010100 ->  NaN Invalid_operation
xorx275 xor  100000000 070001000 ->  NaN Invalid_operation
xorx276 xor  100000010 080010100 ->  NaN Invalid_operation
xorx277 xor  100000000 090000010 ->  NaN Invalid_operation
-- test LSD
xorx280 xor  001000002 100000000 ->  NaN Invalid_operation
xorx281 xor  000000007 100000000 ->  NaN Invalid_operation
xorx282 xor  000000008 100000000 ->  NaN Invalid_operation
xorx283 xor  000000009 100000000 ->  NaN Invalid_operation
xorx284 xor  100000000 000100002 ->  NaN Invalid_operation
xorx285 xor  100100000 001000007 ->  NaN Invalid_operation
xorx286 xor  100010000 010000008 ->  NaN Invalid_operation
xorx287 xor  100001000 100000009 ->  NaN Invalid_operation
-- test Middie
xorx288 xor  001020000 100000000 ->  NaN Invalid_operation
xorx289 xor  000070001 100000000 ->  NaN Invalid_operation
xorx290 xor  000080000 100010000 ->  NaN Invalid_operation
xorx291 xor  000090000 100001000 ->  NaN Invalid_operation
xorx292 xor  100000010 000020100 ->  NaN Invalid_operation
xorx293 xor  100100000 000070010 ->  NaN Invalid_operation
xorx294 xor  100010100 000080001 ->  NaN Invalid_operation
xorx295 xor  100001000 000090000 ->  NaN Invalid_operation
-- signs
xorx296 xor -100001000 -000000000 ->  NaN Invalid_operation
xorx297 xor -100001000  000010000 ->  NaN Invalid_operation
xorx298 xor  100001000 -000000000 ->  NaN Invalid_operation
xorx299 xor  100001000  000011000 ->  100010000

-- Nmax, Nmin, Ntiny
xorx331 xor  2   9.99999999E+999     -> NaN Invalid_operation
xorx332 xor  3   1E-999              -> NaN Invalid_operation
xorx333 xor  4   1.00000000E-999     -> NaN Invalid_operation
xorx334 xor  5   1E-1007             -> NaN Invalid_operation
xorx335 xor  6   -1E-1007            -> NaN Invalid_operation
xorx336 xor  7   -1.00000000E-999    -> NaN Invalid_operation
xorx337 xor  8   -1E-999             -> NaN Invalid_operation
xorx338 xor  9   -9.99999999E+999    -> NaN Invalid_operation
xorx341 xor  9.99999999E+999     -18 -> NaN Invalid_operation
xorx342 xor  1E-999               01 -> NaN Invalid_operation
xorx343 xor  1.00000000E-999     -18 -> NaN Invalid_operation
xorx344 xor  1E-1007              18 -> NaN Invalid_operation
xorx345 xor  -1E-1007            -10 -> NaN Invalid_operation
xorx346 xor  -1.00000000E-999     18 -> NaN Invalid_operation
xorx347 xor  -1E-999              10 -> NaN Invalid_operation
xorx348 xor  -9.99999999E+999    -18 -> NaN Invalid_operation

-- A few other non-integers
xorx361 xor  1.0                  1  -> NaN Invalid_operation
xorx362 xor  1E+1                 1  -> NaN Invalid_operation
xorx363 xor  0.0                  1  -> NaN Invalid_operation
xorx364 xor  0E+1                 1  -> NaN Invalid_operation
xorx365 xor  9.9                  1  -> NaN Invalid_operation
xorx366 xor  9E+1                 1  -> NaN Invalid_operation
xorx371 xor  0 1.0                   -> NaN Invalid_operation
xorx372 xor  0 1E+1                  -> NaN Invalid_operation
xorx373 xor  0 0.0                   -> NaN Invalid_operation
xorx374 xor  0 0E+1                  -> NaN Invalid_operation
xorx375 xor  0 9.9                   -> NaN Invalid_operation
xorx376 xor  0 9E+1                  -> NaN Invalid_operation

-- All Specials are in error
xorx780 xor -Inf  -Inf   -> NaN Invalid_operation
xorx781 xor -Inf  -1000  -> NaN Invalid_operation
xorx782 xor -Inf  -1     -> NaN Invalid_operation
xorx783 xor -Inf  -0     -> NaN Invalid_operation
xorx784 xor -Inf   0     -> NaN Invalid_operation
xorx785 xor -Inf   1     -> NaN Invalid_operation
xorx786 xor -Inf   1000  -> NaN Invalid_operation
xorx787 xor -1000 -Inf   -> NaN Invalid_operation
xorx788 xor -Inf  -Inf   -> NaN Invalid_operation
xorx789 xor -1    -Inf   -> NaN Invalid_operation
xorx790 xor -0    -Inf   -> NaN Invalid_operation
xorx791 xor  0    -Inf   -> NaN Invalid_operation
xorx792 xor  1    -Inf   -> NaN Invalid_operation
xorx793 xor  1000 -Inf   -> NaN Invalid_operation
xorx794 xor  Inf  -Inf   -> NaN Invalid_operation

xorx800 xor  Inf  -Inf   -> NaN Invalid_operation
xorx801 xor  Inf  -1000  -> NaN Invalid_operation
xorx802 xor  Inf  -1     -> NaN Invalid_operation
xorx803 xor  Inf  -0     -> NaN Invalid_operation
xorx804 xor  Inf   0     -> NaN Invalid_operation
xorx805 xor  Inf   1     -> NaN Invalid_operation
xorx806 xor  Inf   1000  -> NaN Invalid_operation
xorx807 xor  Inf   Inf   -> NaN Invalid_operation
xorx808 xor -1000  Inf   -> NaN Invalid_operation
xorx809 xor -Inf   Inf   -> NaN Invalid_operation
xorx810 xor -1     Inf   -> NaN Invalid_operation
xorx811 xor -0     Inf   -> NaN Invalid_operation
xorx812 xor  0     Inf   -> NaN Invalid_operation
xorx813 xor  1     Inf   -> NaN Invalid_operation
xorx814 xor  1000  Inf   -> NaN Invalid_operation
xorx815 xor  Inf   Inf   -> NaN Invalid_operation

xorx821 xor  NaN -Inf    -> NaN Invalid_operation
xorx822 xor  NaN -1000   -> NaN Invalid_operation
xorx823 xor  NaN -1      -> NaN Invalid_operation
xorx824 xor  NaN -0      -> NaN Invalid_operation
xorx825 xor  NaN  0      -> NaN Invalid_operation
xorx826 xor  NaN  1      -> NaN Invalid_operation
xorx827 xor  NaN  1000   -> NaN Invalid_operation
xorx828 xor  NaN  Inf    -> NaN Invalid_operation
xorx829 xor  NaN  NaN    -> NaN Invalid_operation
xorx830 xor -Inf  NaN    -> NaN Invalid_operation
xorx831 xor -1000 NaN    -> NaN Invalid_operation
xorx832 xor -1    NaN    -> NaN Invalid_operation
xorx833 xor -0    NaN    -> NaN Invalid_operation
xorx834 xor  0    NaN    -> NaN Invalid_operation
xorx835 xor  1    NaN    -> NaN Invalid_operation
xorx836 xor  1000 NaN    -> NaN Invalid_operation
xorx837 xor  Inf  NaN    -> NaN Invalid_operation

xorx841 xor  sNaN -Inf   ->  NaN  Invalid_operation
xorx842 xor  sNaN -1000  ->  NaN  Invalid_operation
xorx843 xor  sNaN -1     ->  NaN  Invalid_operation
xorx844 xor  sNaN -0     ->  NaN  Invalid_operation
xorx845 xor  sNaN  0     ->  NaN  Invalid_operation
xorx846 xor  sNaN  1     ->  NaN  Invalid_operation
xorx847 xor  sNaN  1000  ->  NaN  Invalid_operation
xorx848 xor  sNaN  NaN   ->  NaN  Invalid_operation
xorx849 xor  sNaN sNaN   ->  NaN  Invalid_operation
xorx850 xor  NaN  sNaN   ->  NaN  Invalid_operation
xorx851 xor -Inf  sNaN   ->  NaN  Invalid_operation
xorx852 xor -1000 sNaN   ->  NaN  Invalid_operation
xorx853 xor -1    sNaN   ->  NaN  Invalid_operation
xorx854 xor -0    sNaN   ->  NaN  Invalid_operation
xorx855 xor  0    sNaN   ->  NaN  Invalid_operation
xorx856 xor  1    sNaN   ->  NaN  Invalid_operation
xorx857 xor  1000 sNaN   ->  NaN  Invalid_operation
xorx858 xor  Inf  sNaN   ->  NaN  Invalid_operation
xorx859 xor  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
xorx861 xor  NaN1   -Inf    -> NaN Invalid_operation
xorx862 xor +NaN2   -1000   -> NaN Invalid_operation
xorx863 xor  NaN3    1000   -> NaN Invalid_operation
xorx864 xor  NaN4    Inf    -> NaN Invalid_operation
xorx865 xor  NaN5   +NaN6   -> NaN Invalid_operation
xorx866 xor -Inf     NaN7   -> NaN Invalid_operation
xorx867 xor -1000    NaN8   -> NaN Invalid_operation
xorx868 xor  1000    NaN9   -> NaN Invalid_operation
xorx869 xor  Inf    +NaN10  -> NaN Invalid_operation
xorx871 xor  sNaN11  -Inf   -> NaN Invalid_operation
xorx872 xor  sNaN12  -1000  -> NaN Invalid_operation
xorx873 xor  sNaN13   1000  -> NaN Invalid_operation
xorx874 xor  sNaN14   NaN17 -> NaN Invalid_operation
xorx875 xor  sNaN15  sNaN18 -> NaN Invalid_operation
xorx876 xor  NaN16   sNaN19 -> NaN Invalid_operation
xorx877 xor -Inf    +sNaN20 -> NaN Invalid_operation
xorx878 xor -1000    sNaN21 -> NaN Invalid_operation
xorx879 xor  1000    sNaN22 -> NaN Invalid_operation
xorx880 xor  Inf     sNaN23 -> NaN Invalid_operation
xorx881 xor +NaN25  +sNaN24 -> NaN Invalid_operation
xorx882 xor -NaN26    NaN28 -> NaN Invalid_operation
xorx883 xor -sNaN27  sNaN29 -> NaN Invalid_operation
xorx884 xor  1000    -NaN30 -> NaN Invalid_operation
xorx885 xor  1000   -sNaN31 -> NaN Invalid_operation
