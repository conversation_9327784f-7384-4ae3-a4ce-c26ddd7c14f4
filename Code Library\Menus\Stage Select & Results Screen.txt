Stage Striking (Whites-out Struck Stages)
Works like it does in PM.

X = ban currently selected stage
Y = ban all stages that aren't allowed in random, and unban all that are allowed
Z = unban all

Banned stages can't be selected anymore, however they can still be selected when someone chooses "Random". You may edit the code to have blacked-out struck stages instead.
[Sham Rock]
Revision ---- <PERSON><PERSON> Offset ---- Hex to Replace ---------- ASM Code -
PAL 1.00 ---- 0x8025AEF4 ---- C022CA40 -> Branch

39E00001 3E008045
6210BF04 1E2F000C
7E31802E 5630014B
4182000C 3A200001
48000020 56300109
4182000C 3A200002
48000010 563002D7
418200DC 3A200003
3EA0803F 62B51550
3EC0804C 62D67FC6
3A800000 3A600000
3A400000 2C110001
40820014 8A560000
2C12001C 418100A8
48000050 2C110003
40820010 3A80003F
3A600002 4800003C
1E12001C 7E10AA14
8AF0000A 3E008044
6210D190 82100000
7E10BC30 561007FF
41820010 3A600002
3A80003F 4800000C
3A600000 3A800000
1E12001C 7E10A82E
2C120016 41800008
82100010 82100018
82100004 82100008
8210001C 82100008
9A900000 1E12001C # <- Replace this line with "9A900024 1E12001C" for 
3A100008 7E70A9AE # the black version of the code.
3A00001E 9A160000
2C110001 41820010
3A520001 2C12001D
41A0FF4C 39EF0001
2C0F0005 41A0FEE8
C022CA40 48000000

NTSC 1.00 --- 0x8025910C ---- C022C9E8 -> Branch

39E00001 3E008046
62109134 1E2F000C
7E31802E 5630014B
4182000C 3A200001
48000020 56300109
4182000C 3A200002
48000010 563002D7
418200DC 3A200003
3EA0803E 62B5E840
3EC0804D 62D64B2E
3A800000 3A600000
3A400000 2C110001
40820014 8A560000
2C12001C 418100A8
48000050 2C110003
40820010 3A80003F
3A600002 4800003C
1E12001C 7E10AA14
8AF0000A 3E008045
6210A3C0 82100000
7E10BC30 561007FF
41820010 3A600002
3A80003F 4800000C
3A600000 3A800000
1E12001C 7E10A82E
2C120016 41800008
82100010 82100018
82100004 82100008
8210001C 82100008
9A900000 1E12001C # <- Replace this line with "9A900024 1E12001C" for 
3A100008 7E70A9AE # the black version of the code.
3A00001E 9A160000
2C110001 41820010
3A520001 2C12001D
41A0FF4C 39EF0001
2C0F0005 41A0FEE8
C022C9E8 48000000

NTSC 1.02 --- 0x8025A3BC ---- C022C9E8 -> Branch

39E00001 3E008046
6210B0FC 1E2F000C
7E31802E 5630014B
4182000C 3A200001
48000020 56300109
4182000C 3A200002
48000010 563002D7
418200DC 3A200003
3EA0803F 62B506D0
3EC0804D 62D66CAE
3A800000 3A600000
3A400000 2C110001
40820014 8A560000
2C12001C 418100A8
48000050 2C110003
40820010 3A80003F
3A600002 4800003C
1E12001C 7E10AA14
8AF0000A 3E008045
6210C388 82100000
7E10BC30 561007FF
41820010 3A600002
3A80003F 4800000C
3A600000 3A800000
1E12001C 7E10A82E
2C120016 41800008
82100010 82100018
82100004 82100008
8210001C 82100008
9A900000 1E12001C # <- Replace this line with "9A900024 1E12001C" for 
3A100008 7E70A9AE # the black version of the code.
3A00001E 9A160000
2C110001 41820010
3A520001 2C12001D
41A0FF4C 39EF0001
2C0F0005 41A0FEE8
C022C9E8 48000000


	-==-


Stage Striking (Transparent Struck Stages), Method 1
Works like it does in PM.

X = ban currently selected stage
Y = ban all stages that aren't allowed in random, and unban all that are allowed
Z = unban all

Banned stages can't be selected anymore, however they can still be selected when someone chooses "Random".
<https://smashboards.com/threads/stage-striking-invisible-struck-stages.422589/post-20401006>
[Sham Rock, Achilles,]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8025A3BC ---- C022C9E8 -> Branch

39E00001 3E008046
6210B0FC 1E2F000C
7E31802E 5630014B
4182000C 3A200001
48000020 56300109
4182000C 3A200002
48000010 563002D7
418200E0 3A200003
3EA0803F 62B506D0
3EC0804D 62D66CAE
3A800000 3A600000
3A400000 2C110001
40820014 8A560000
2C12001C 418100AC
48000050 2C110003
40820010 3A80003F
3A600002 4800003C
1E12001C 7E10AA14
8AF0000A 3E008045
6210C388 82100000
7E10BC30 561007FF
41820010 3A600002
3A80003F 4800000C
3A600000 3A800000
1E12001C 7E10A82E
3AE00000 2C130002
4082001C 3EE00008
62F70008 2C120016
4180000C 3EE02000
62F70008 92F00014
1E12001C 3A100008
7E70A9AE 3A00001E
9A160000 2C110001
41820010 3A520001
2C12001D 41A0FF60
39EF0001 2C0F0005
41A0FEE4 C022C9E8
60000000 00000000


	-==-


Stage Striking (Transparent Struck Stages), Method 2
X = ban currently selected stage
Y = ban all stages that aren't allowed in random, and unban all that are allowed
Z = unban all

This version also auto-strikes all stages that aren't enabled in random select.
<https://smashboards.com/threads/stage-striking-invisible-struck-stages.422589/post-20402298>
[Dan Salvato]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x80259C40 ---- 28000000 -> Branch

39600000 3D408045
614AC388 38600000
3C80803F 608406D0
28000013 4082000C
39600001 48000010
28000000 408200C0
48000034 2C03001D
408000B4 2C0B0002
4182004C 1CA3001C
7CA52214 88C5000A
80AA0000 7CA53430
54A507FF 40820088
4800002C 806DB600
5460056B 4082001C
546006F7 40820008
48000074 39600002
38600000 4BFFFFB0
886DB60E 2C03001D
4080005C 1CA3001C
7CA52214 38C00000
2C0B0002 40820008
38C00002 98C50008
80A50000 2C030016
41800008 80A50010
3CC04400 2C0B0002
40820008 38C00000
90C50038 38C0001E
98CDB60E 2C0B0000
4182000C 38630001
4BFFFF4C 28000000
60000000 00000000


	-==-


'RANDOM' is Default Highlighted on Stage Select Screen
Th cursor is set as if you had already dragged it over top of the "Random" selection.
[Jorgasms]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0x2575F0 ---- 38E0001E -> 38E0001D
1.01 ------ 0x256E54 ---- 38E0001E -> 38E0001D
1.00 ------ 0x2562E4 ---- 38E0001E -> 38E0001D
PAL ------- 0x25810C ---- 38E0001E -> 38E0001D


	-==-


Hold L+R+Y For Salty Runbacks
- Holding L+R+Y at the end of a match immediately starts a new match on that stage.
[Dan Salvato]

Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0x1A0D40 ---- 981F0003 -> Branch

39C00000 3DE08046
61EFB108 820F0000
56110253 41820018
56110295 41820010
56110109 41820008
38000002 39CE0001
2C0E0004 4080000C
39EF0008 4BFFFFD0
981F0003 4819FC00


	-==-


Hold A+B For Salty Runbacks
- Holding A+B at the end of a match immediately starts a new match on that stage.
[Dan Salvato and Sham Rock]

Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.00 ------ 0x19FFf8 ---- 981F0003 -> Branch

39C00000 3DE08046
61EF9140 820F0000
5611018D 41820010
561101CF 41820008
38000002 39CE0001
2C0E0004 4080000C
39EF000C 4BFFFFD8
981F0003 4819EEC0

1.02 ------ 0x1A0D40 ---- 981F0003 -> Branch

39C00000 3DE08046
61EFB108 820F0000
5611018D 41820010
561101CF 41820008
38000002 39CE0001
2C0E0004 4080000C
39EF000C 4BFFFFD8
981F0003 4819FC08

PAL ------ 0x1A18C4 ---- 981F0003 -> Branch

39C00000 3DE08045
61EFBF10 820F0000
5611018D 41820010
561101CF 41820008
38000002 39CE0001
2C0E0004 4080000C
39EF000C 4BFFFFD8
981F0003 481A078C


	-==-


L Cancel % in Results Screen (revised 2/11/15)
- Shows the player's L-cancel success rate in the end of game stats page.
[Wooggle]
Version -- DOL Offset ------ Hex to Replace -------
1.02 ------ 0x3D79C ----- 90030C68 -> 60000000
----------- 0x8A1E0 ----- 3880FFFF -> Branch

8A830678 3DC08000
61CE45D4 1EB40008
7DCEAA14 81EE0000
39EF0001 91EE0000
3A0EFFFC 82300000
7E717BD6 3E408045
62523DA4 1EB40E90
7E52AA14 92720000
3880FFFF 4808A2F8

----------- 0x8A284 ----- EC010024 -> Branch

8A830678 3DC08000
61CE45D4 1EB40008
7DCEAA14 81EE0000
3A0EFFFC 82300000
3A310064 92300000
7E717BD6 3E408045
62523DA4 1EB40E90
7E52AA14 92720000
EC010024 4808A354

----------- 0x174E04 ---- 80030008 -> Branch

3DC08000 61CE45D0
39E00000 91EE0000
91EE0004 91EE0008
91EE000C 91EE0010
91EE0014 91EE0018
91EE001C 3DC080C6
61CE1DB8 3DE01620
61EF151A 91EE0000
3DE0200C 61EF2024
91EE0004 3DE02031
61EF2026 91EE0008
3DE02028 61EF202F
91EE000C 3DE01A21
61EF0300 91EE0010
39E00000 91EE0014
91EE0018 91EE001C
80030008 48174E08


	-==-


Always Skip the Result Screen, and Stock Count = Placement
- Game skips the result screen after a match, and changes the stars for the amount of stocks taken to the placement of the player in the last match.
If you quit out of a match it will show who had the stock-lead at the moment.
The only time it doesn't skip it is after the sudden death if a timed match ends in a draw.
Warning: when using this code you will NOT unlock anything you would normally after a match (new stages/characters etc), only use it together with save files where you have already unlocked everything.
[Sham Rock]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
PAL ------- 0x1A18C0 ---- 3803FFFF -> Branch

3803FFFF 2C000004
40820060 2C0B0020
41820058 3A400000
3E808046 6294AB60
3EA0804C 62B579EF
8E1400A8 8A340001
2C10004E 4080000C
9E350001 4800000C
3A310001 9E350001
3A520001 2C120004
41A0FFD8 3A000000
3A200000 3A800000
3AA00000 38000000
60000000 481A0858

1.02 ------ 0x1a0d3c ---- 3803ffff -> Branch

3803FFFF 2C000004
40820060 2C0B0020
41820058 3A400000
3E808047 62949D58
3EA0804D 62B5672F
8E1400A8 8A340001
2C10004E 4080000C
9E350001 4800000C
3A310001 9E350001
3A520001 2C120004
41A0FFD8 3A000000
3A200000 3A800000
3AA00000 38000000
60000000 4819FCD4

1.00 ------ 0x19FFf4 ---- 3803FFFF -> Branch

3803FFFF 2C000004
40820060 2C0B0020
41820058 3A400000
3E808047 62947D90
3EA0804D 62B545AF
8E1400A8 8A340001
2C10004E 4080000C
9E350001 4800000C
3A310001 9E350001
3A520001 2C120004
41A0FFD8 3A000000
3A200000 3A800000
3AA00000 38000000
60000000 4819EF8C


	-==-


Skip Results Screen, and KO Stars = Games Won
- Game skips the result screen after a match, and changes the stars for the amount of stocks taken to the number of games won.
- Ragequitting DOES NOT earn anyone stars.

Another version of this code can be found here:
https://smashboards.com/threads/skip-results-screen-new-code.423874/#post-20547079
[Sham Rock, Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0x1A0D3C ---- 3803FFFF -> Branch

3803FFFF 2C0B0020
41820078 2C000004
40820070 3E208047
6231A1EC 7C088800
4082005C 3E00804D
3A9065A7 6210672F
3E208043 62312087
3A400000 3A520001
3A940001 3A100001
1E720008 7E738A14
8A730000 2C130021
4182001C 8AB40000
2C150000 40820010
8AB00000 3AB50001
9AB00000 2C120004
41A0FFC4 38000000
60000000 48000000


	-==-


Always Skip Results Screen, and KO Stars Function Normally
- Game skips the result screen after a match.
[achilles]
Version -- DOL Offset ------ Hex to Replace -------

1.02 ------ 0x1A0D3C ---- 3803FFFF -> Branch

3803FFFF 2C0B0020
41820064 2C000004
4082005C 3AC00000
3E608045 6273226F
3E40804D 6252672F
8E320001 8E930E84
7E31A214 8E930004
7E31A214 8E930004
7E31A214 8E930004
7E31A214 2C1100FF
41800008 3A2000FF
9A320000 3AD60001
2C160004 41A0FFC4
38000000 4819FCD4


	-==-


Always Skip the Results Screen (no change to KO stars)
Stock counter doesn't show up with this version.
[Sham Rock]
Version -- DOL Offset ------ Hex to Replace -------
1.02 ----- 0x801A415C ---- 3803FFFF -> Branch

3803FFFF 2C000004
40820008 38000000
60000000 00000000

1.00 ----- 0x801A3414 ---- 3803FFFF -> Branch

3803FFFF 2C000004
40820008 38000000
60000000 00000000

PAL ------ 0x801a4ce0 ---- 3803FFFF -> Branch

3803FFFF 2C000004
40820008 38000000
60000000 00000000


	-==-


Hold Start to Skip Results (2 Seconds)
[Achilles]
1.02
C21795B8 00000006
38ADB4EF 38800004
7C8903A6 8C850001
2C040078 4180000C
3C808048 90A49D64
4200FFEC 901F0008
60000000 00000000


	-==-


No Delay on Start at SSS
<https://smashboards.com/threads/no-delay-on-pressing-start-at-css-and-sss.448419/>
[UnclePunch]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0x2575F8 ---- 38000014 -> 38000001