------------------------------------------------------------------------
-- ddMaxMag.decTest -- decDouble maxnummag                            --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- we assume that base comparison is tested in compare.decTest, so
-- these mainly cover special cases and rounding
precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- sanity checks
ddmxg001 maxmag  -2  -2  -> -2
ddmxg002 maxmag  -2  -1  -> -2
ddmxg003 maxmag  -2   0  -> -2
ddmxg004 maxmag  -2   1  -> -2
ddmxg005 maxmag  -2   2  ->  2
ddmxg006 maxmag  -1  -2  -> -2
ddmxg007 maxmag  -1  -1  -> -1
ddmxg008 maxmag  -1   0  -> -1
ddmxg009 maxmag  -1   1  ->  1
ddmxg010 maxmag  -1   2  ->  2
ddmxg011 maxmag   0  -2  -> -2
ddmxg012 maxmag   0  -1  -> -1
ddmxg013 maxmag   0   0  ->  0
ddmxg014 maxmag   0   1  ->  1
ddmxg015 maxmag   0   2  ->  2
ddmxg016 maxmag   1  -2  -> -2
ddmxg017 maxmag   1  -1  ->  1
ddmxg018 maxmag   1   0  ->  1
ddmxg019 maxmag   1   1  ->  1
ddmxg020 maxmag   1   2  ->  2
ddmxg021 maxmag   2  -2  ->  2
ddmxg022 maxmag   2  -1  ->  2
ddmxg023 maxmag   2   0  ->  2
ddmxg025 maxmag   2   1  ->  2
ddmxg026 maxmag   2   2  ->  2

-- extended zeros
ddmxg030 maxmag   0     0   ->  0
ddmxg031 maxmag   0    -0   ->  0
ddmxg032 maxmag   0    -0.0 ->  0
ddmxg033 maxmag   0     0.0 ->  0
ddmxg034 maxmag  -0     0   ->  0    -- note: -0 = 0, but 0 chosen
ddmxg035 maxmag  -0    -0   -> -0
ddmxg036 maxmag  -0    -0.0 -> -0.0
ddmxg037 maxmag  -0     0.0 ->  0.0
ddmxg038 maxmag   0.0   0   ->  0
ddmxg039 maxmag   0.0  -0   ->  0.0
ddmxg040 maxmag   0.0  -0.0 ->  0.0
ddmxg041 maxmag   0.0   0.0 ->  0.0
ddmxg042 maxmag  -0.0   0   ->  0
ddmxg043 maxmag  -0.0  -0   -> -0.0
ddmxg044 maxmag  -0.0  -0.0 -> -0.0
ddmxg045 maxmag  -0.0   0.0 ->  0.0

ddmxg050 maxmag  -0E1   0E1 ->  0E+1
ddmxg051 maxmag  -0E2   0E2 ->  0E+2
ddmxg052 maxmag  -0E2   0E1 ->  0E+1
ddmxg053 maxmag  -0E1   0E2 ->  0E+2
ddmxg054 maxmag   0E1  -0E1 ->  0E+1
ddmxg055 maxmag   0E2  -0E2 ->  0E+2
ddmxg056 maxmag   0E2  -0E1 ->  0E+2
ddmxg057 maxmag   0E1  -0E2 ->  0E+1

ddmxg058 maxmag   0E1   0E1 ->  0E+1
ddmxg059 maxmag   0E2   0E2 ->  0E+2
ddmxg060 maxmag   0E2   0E1 ->  0E+2
ddmxg061 maxmag   0E1   0E2 ->  0E+2
ddmxg062 maxmag  -0E1  -0E1 -> -0E+1
ddmxg063 maxmag  -0E2  -0E2 -> -0E+2
ddmxg064 maxmag  -0E2  -0E1 -> -0E+1
ddmxg065 maxmag  -0E1  -0E2 -> -0E+1

-- Specials
ddmxg090 maxmag  Inf  -Inf   ->  Infinity
ddmxg091 maxmag  Inf  -1000  ->  Infinity
ddmxg092 maxmag  Inf  -1     ->  Infinity
ddmxg093 maxmag  Inf  -0     ->  Infinity
ddmxg094 maxmag  Inf   0     ->  Infinity
ddmxg095 maxmag  Inf   1     ->  Infinity
ddmxg096 maxmag  Inf   1000  ->  Infinity
ddmxg097 maxmag  Inf   Inf   ->  Infinity
ddmxg098 maxmag -1000  Inf   ->  Infinity
ddmxg099 maxmag -Inf   Inf   ->  Infinity
ddmxg100 maxmag -1     Inf   ->  Infinity
ddmxg101 maxmag -0     Inf   ->  Infinity
ddmxg102 maxmag  0     Inf   ->  Infinity
ddmxg103 maxmag  1     Inf   ->  Infinity
ddmxg104 maxmag  1000  Inf   ->  Infinity
ddmxg105 maxmag  Inf   Inf   ->  Infinity

ddmxg120 maxmag -Inf  -Inf   -> -Infinity
ddmxg121 maxmag -Inf  -1000  -> -Infinity
ddmxg122 maxmag -Inf  -1     -> -Infinity
ddmxg123 maxmag -Inf  -0     -> -Infinity
ddmxg124 maxmag -Inf   0     -> -Infinity
ddmxg125 maxmag -Inf   1     -> -Infinity
ddmxg126 maxmag -Inf   1000  -> -Infinity
ddmxg127 maxmag -Inf   Inf   ->  Infinity
ddmxg128 maxmag -Inf  -Inf   ->  -Infinity
ddmxg129 maxmag -1000 -Inf   -> -Infinity
ddmxg130 maxmag -1    -Inf   -> -Infinity
ddmxg131 maxmag -0    -Inf   -> -Infinity
ddmxg132 maxmag  0    -Inf   -> -Infinity
ddmxg133 maxmag  1    -Inf   -> -Infinity
ddmxg134 maxmag  1000 -Inf   -> -Infinity
ddmxg135 maxmag  Inf  -Inf   ->  Infinity

-- 2004.08.02 754r chooses number over NaN in mixed cases
ddmxg141 maxmag  NaN -Inf    -> -Infinity
ddmxg142 maxmag  NaN -1000   -> -1000
ddmxg143 maxmag  NaN -1      -> -1
ddmxg144 maxmag  NaN -0      -> -0
ddmxg145 maxmag  NaN  0      ->  0
ddmxg146 maxmag  NaN  1      ->  1
ddmxg147 maxmag  NaN  1000   ->  1000
ddmxg148 maxmag  NaN  Inf    ->  Infinity
ddmxg149 maxmag  NaN  NaN    ->  NaN
ddmxg150 maxmag -Inf  NaN    -> -Infinity
ddmxg151 maxmag -1000 NaN    -> -1000
ddmxg152 maxmag -1    NaN    -> -1
ddmxg153 maxmag -0    NaN    -> -0
ddmxg154 maxmag  0    NaN    ->  0
ddmxg155 maxmag  1    NaN    ->  1
ddmxg156 maxmag  1000 NaN    ->  1000
ddmxg157 maxmag  Inf  NaN    ->  Infinity

ddmxg161 maxmag  sNaN -Inf   ->  NaN  Invalid_operation
ddmxg162 maxmag  sNaN -1000  ->  NaN  Invalid_operation
ddmxg163 maxmag  sNaN -1     ->  NaN  Invalid_operation
ddmxg164 maxmag  sNaN -0     ->  NaN  Invalid_operation
ddmxg165 maxmag  sNaN  0     ->  NaN  Invalid_operation
ddmxg166 maxmag  sNaN  1     ->  NaN  Invalid_operation
ddmxg167 maxmag  sNaN  1000  ->  NaN  Invalid_operation
ddmxg168 maxmag  sNaN  NaN   ->  NaN  Invalid_operation
ddmxg169 maxmag  sNaN sNaN   ->  NaN  Invalid_operation
ddmxg170 maxmag  NaN  sNaN   ->  NaN  Invalid_operation
ddmxg171 maxmag -Inf  sNaN   ->  NaN  Invalid_operation
ddmxg172 maxmag -1000 sNaN   ->  NaN  Invalid_operation
ddmxg173 maxmag -1    sNaN   ->  NaN  Invalid_operation
ddmxg174 maxmag -0    sNaN   ->  NaN  Invalid_operation
ddmxg175 maxmag  0    sNaN   ->  NaN  Invalid_operation
ddmxg176 maxmag  1    sNaN   ->  NaN  Invalid_operation
ddmxg177 maxmag  1000 sNaN   ->  NaN  Invalid_operation
ddmxg178 maxmag  Inf  sNaN   ->  NaN  Invalid_operation
ddmxg179 maxmag  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
ddmxg181 maxmag  NaN9  -Inf   -> -Infinity
ddmxg182 maxmag  NaN8     9   ->  9
ddmxg183 maxmag -NaN7   Inf   ->  Infinity

ddmxg184 maxmag -NaN1   NaN11 -> -NaN1
ddmxg185 maxmag  NaN2   NaN12 ->  NaN2
ddmxg186 maxmag -NaN13 -NaN7  -> -NaN13
ddmxg187 maxmag  NaN14 -NaN5  ->  NaN14

ddmxg188 maxmag -Inf    NaN4  -> -Infinity
ddmxg189 maxmag -9     -NaN3  -> -9
ddmxg190 maxmag  Inf    NaN2  ->  Infinity

ddmxg191 maxmag  sNaN99 -Inf    ->  NaN99 Invalid_operation
ddmxg192 maxmag  sNaN98 -1      ->  NaN98 Invalid_operation
ddmxg193 maxmag -sNaN97  NaN    -> -NaN97 Invalid_operation
ddmxg194 maxmag  sNaN96 sNaN94  ->  NaN96 Invalid_operation
ddmxg195 maxmag  NaN95  sNaN93  ->  NaN93 Invalid_operation
ddmxg196 maxmag -Inf    sNaN92  ->  NaN92 Invalid_operation
ddmxg197 maxmag  0      sNaN91  ->  NaN91 Invalid_operation
ddmxg198 maxmag  Inf   -sNaN90  -> -NaN90 Invalid_operation
ddmxg199 maxmag  NaN    sNaN89  ->  NaN89 Invalid_operation

-- old rounding checks
ddmxg221 maxmag 12345678000 1  -> 12345678000
ddmxg222 maxmag 1 12345678000  -> 12345678000
ddmxg223 maxmag 1234567800  1  -> 1234567800
ddmxg224 maxmag 1 1234567800   -> 1234567800
ddmxg225 maxmag 1234567890  1  -> 1234567890
ddmxg226 maxmag 1 1234567890   -> 1234567890
ddmxg227 maxmag 1234567891  1  -> 1234567891
ddmxg228 maxmag 1 1234567891   -> 1234567891
ddmxg229 maxmag 12345678901 1  -> 12345678901
ddmxg230 maxmag 1 12345678901  -> 12345678901
ddmxg231 maxmag 1234567896  1  -> 1234567896
ddmxg232 maxmag 1 1234567896   -> 1234567896
ddmxg233 maxmag -1234567891  1 -> -1234567891
ddmxg234 maxmag 1 -1234567891  -> -1234567891
ddmxg235 maxmag -12345678901 1 -> -12345678901
ddmxg236 maxmag 1 -12345678901 -> -12345678901
ddmxg237 maxmag -1234567896  1 -> -1234567896
ddmxg238 maxmag 1 -1234567896  -> -1234567896

-- from examples
ddmxg280 maxmag '3'   '2'  ->  '3'
ddmxg281 maxmag '-10' '3'  ->  '-10'
ddmxg282 maxmag '1.0' '1'  ->  '1'
ddmxg283 maxmag '1' '1.0'  ->  '1'
ddmxg284 maxmag '7' 'NaN'  ->  '7'

-- expanded list from min/max 754r purple prose
-- [explicit tests for exponent ordering]
ddmxg401 maxmag  Inf    1.1     ->  Infinity
ddmxg402 maxmag  1.1    1       ->  1.1
ddmxg403 maxmag  1      1.0     ->  1
ddmxg404 maxmag  1.0    0.1     ->  1.0
ddmxg405 maxmag  0.1    0.10    ->  0.1
ddmxg406 maxmag  0.10   0.100   ->  0.10
ddmxg407 maxmag  0.10   0       ->  0.10
ddmxg408 maxmag  0      0.0     ->  0
ddmxg409 maxmag  0.0   -0       ->  0.0
ddmxg410 maxmag  0.0   -0.0     ->  0.0
ddmxg411 maxmag  0.00  -0.0     ->  0.00
ddmxg412 maxmag  0.0   -0.00    ->  0.0
ddmxg413 maxmag  0     -0.0     ->  0
ddmxg414 maxmag  0     -0       ->  0
ddmxg415 maxmag -0.0   -0       -> -0.0
ddmxg416 maxmag -0     -0.100   -> -0.100
ddmxg417 maxmag -0.100 -0.10    -> -0.100
ddmxg418 maxmag -0.10  -0.1     -> -0.10
ddmxg419 maxmag -0.1   -1.0     -> -1.0
ddmxg420 maxmag -1.0   -1       -> -1.0
ddmxg421 maxmag -1     -1.1     -> -1.1
ddmxg423 maxmag -1.1   -Inf     -> -Infinity
-- same with operands reversed
ddmxg431 maxmag  1.1    Inf     ->  Infinity
ddmxg432 maxmag  1      1.1     ->  1.1
ddmxg433 maxmag  1.0    1       ->  1
ddmxg434 maxmag  0.1    1.0     ->  1.0
ddmxg435 maxmag  0.10   0.1     ->  0.1
ddmxg436 maxmag  0.100  0.10    ->  0.10
ddmxg437 maxmag  0      0.10    ->  0.10
ddmxg438 maxmag  0.0    0       ->  0
ddmxg439 maxmag -0      0.0     ->  0.0
ddmxg440 maxmag -0.0    0.0     ->  0.0
ddmxg441 maxmag -0.0    0.00    ->  0.00
ddmxg442 maxmag -0.00   0.0     ->  0.0
ddmxg443 maxmag -0.0    0       ->  0
ddmxg444 maxmag -0      0       ->  0
ddmxg445 maxmag -0     -0.0     -> -0.0
ddmxg446 maxmag -0.100 -0       -> -0.100
ddmxg447 maxmag -0.10  -0.100   -> -0.100
ddmxg448 maxmag -0.1   -0.10    -> -0.10
ddmxg449 maxmag -1.0   -0.1     -> -1.0
ddmxg450 maxmag -1     -1.0     -> -1.0
ddmxg451 maxmag -1.1   -1       -> -1.1
ddmxg453 maxmag -Inf   -1.1     -> -Infinity
-- largies
ddmxg460 maxmag  1000   1E+3    ->  1E+3
ddmxg461 maxmag  1E+3   1000    ->  1E+3
ddmxg462 maxmag  1000  -1E+3    ->  1000
ddmxg463 maxmag  1E+3  -1000    ->  1E+3
ddmxg464 maxmag -1000   1E+3    ->  1E+3
ddmxg465 maxmag -1E+3   1000    ->  1000
ddmxg466 maxmag -1000  -1E+3    -> -1000
ddmxg467 maxmag -1E+3  -1000    -> -1000

-- subnormals
ddmxg510 maxmag  1.00E-383       0  ->   1.00E-383
ddmxg511 maxmag  0.1E-383        0  ->   1E-384    Subnormal
ddmxg512 maxmag  0.10E-383       0  ->   1.0E-384  Subnormal
ddmxg513 maxmag  0.100E-383      0  ->   1.00E-384 Subnormal
ddmxg514 maxmag  0.01E-383       0  ->   1E-385    Subnormal
ddmxg515 maxmag  0.999E-383      0  ->   9.99E-384 Subnormal
ddmxg516 maxmag  0.099E-383      0  ->   9.9E-385  Subnormal
ddmxg517 maxmag  0.009E-383      0  ->   9E-386    Subnormal
ddmxg518 maxmag  0.001E-383      0  ->   1E-386    Subnormal
ddmxg519 maxmag  0.0009E-383     0  ->   9E-387    Subnormal
ddmxg520 maxmag  0.0001E-383     0  ->   1E-387    Subnormal

ddmxg530 maxmag -1.00E-383       0  ->  -1.00E-383
ddmxg531 maxmag -0.1E-383        0  ->  -1E-384    Subnormal
ddmxg532 maxmag -0.10E-383       0  ->  -1.0E-384  Subnormal
ddmxg533 maxmag -0.100E-383      0  ->  -1.00E-384 Subnormal
ddmxg534 maxmag -0.01E-383       0  ->  -1E-385    Subnormal
ddmxg535 maxmag -0.999E-383      0  ->  -9.99E-384 Subnormal
ddmxg536 maxmag -0.099E-383      0  ->  -9.9E-385  Subnormal
ddmxg537 maxmag -0.009E-383      0  ->  -9E-386    Subnormal
ddmxg538 maxmag -0.001E-383      0  ->  -1E-386    Subnormal
ddmxg539 maxmag -0.0009E-383     0  ->  -9E-387    Subnormal
ddmxg540 maxmag -0.0001E-383     0  ->  -1E-387    Subnormal

-- Null tests
ddmxg900 maxmag 10  #  -> NaN Invalid_operation
ddmxg901 maxmag  # 10  -> NaN Invalid_operation

