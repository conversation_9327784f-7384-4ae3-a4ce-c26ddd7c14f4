v1.00 Hitlag Behavior
- Prevents moves that deal less than 1% from having hitlag and being A/S/DIable. 
- Allows many moves, such as <PERSON><PERSON>'s Up-B and <PERSON><PERSON><PERSON>'s <PERSON>smash, to work properly.
(Notes on this: http://smashboards.com/threads/melee-gecko-codes-guide-and-discussion.327311/page-31#post-18650879)
[Magus]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x800771E0 --- 41820010 -> 60000000
1.02 ----- 0x800772A4 --- 41820010 -> 60000000
1.02 ----- 0x800781D4 --- 41820010 -> 60000000


	-==-


Every Character Can Wall Jump
- Note: Peach can wall jump out of up-B infinitely (any character with a wall jump and the parasol item can do this, even in vanilla Melee)
[Y.S.]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0x07E2A0 ---- 540007FF -> 60000001 ---- ()
----------- 0x07E2A4 ---- 41820254 -> 981F2224 ---- ()


	-==-


Normal C-Stick Functionality in 1P Mode
-C-Stick functions in 1P modes like it does in VS mode
[Zauron]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
PAL ------- 0x168A30 ---- 48000008 -> 60000000
1.01 ------ 0x167D6C ---- 48000008 -> 60000000
1.02 ------ 0x168060 ---- 48000008 -> 60000000
1.00 ------ 0x167744 ---- 48000008 -> 60000000


	-==-


Taunt Canceling
- Taunting during dash carries momentum
- Taunting can be interrupted by teeter animation
[Dan Salvato]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.00 ------ 0x0C6CF4 ---- 40820034 -> 40820058 ---- ()
----------- 0x0DB604 ---- 4BFA5439 -> 4BFA55B5 ---- ()

1.01 ------ 0x0C6E38 ---- 40820034 -> 40820058 ---- ()
----------- 0x0DB7DC ---- 4BFA5349 -> 4BFA54C5 ---- ()

1.02 ------ 0x0C70AC ---- 40820034 -> 40820058 ---- ()
----------- 0x0DBA50 ---- 4BFA5295 -> 4BFA5411 ---- ()

PAL ------- 0x0C7850 ---- 40820034 -> 40820058 ---- ()
----------- 0x0DC204 ---- 4BFA515D -> 4BFA52D9 ---- ()


	-==-


No Rapid Jabs - All Applicable Characters
[achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0xD36CC ------ 480000A1 -> 60000000


	-==-


Aerials are Automatically L-Cancelled
[Dan Salvato]
Version -- DOL Offset ------ Hex to Replace
1.02 ------ 0x68200 ---- 41820010 -> 60000000


	-==-


Disable Tap Jump
Affects all players
[Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x800CBBC0 --- 7C0802A6 -> Branch

89FD06BE 2C0F0004
40800008 4E800020
7C0802A6 48000000

1.02 ----- 0x800CB4E0 --- 7C0802A6 -> Branch

2C040001 40820008
4E800020 7C0802A6
60000000 48000000


	-==-


Disable Airdodges 
[Punkline] 
NTSC 1.02 --- 80099a7c ---- 48000021 -> 60000000 
------------- 80099a80 ---- 38600001 -> 38600000


	-==-


Rolling Results in Immediate Death
[Dan Salvato, standardtoaster]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x80099244 --- 48000065 -> 4803A985
1.00 ----- 0x80098F98 --- 48000065 -> 4803A879


	-==-


Ledge Invincibility Attrition
This basically make ledge invincibility behave similarly to shields, in that after grabbing the ledge a few times in quick succession, subsequent grabs will reduces ledge invincibility. The ledge invincibility recovers over time, like shields, and is reset when you die, just like shields. (I figure this is a better solution that the one PM has, not only because I feel it feels more natural, but also because of things like the haxdash that can circumvent it. - _glook)
<http://smashboards.com/threads/melee-gecko-codes-guide-and-discussion.327311/page-35#post-19218896> #
[_glook]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8009A8AC ---- 8084049C -> Branch

7C671B78 8063002C
8084049C 80A31068
7CA62B78 38A50020
2C050140 40A10008
38A00140 90A31068
7CC631D6 54C6A33E
7C062000 41A0000C
38800000 48000008
7C862050 7CE33B78
60000000 48000000

------------- 0x8006A414 ---- 807F1990 -> Branch

807F198C 809F1988
7C632378 2C030000
40820018 807F1068
2C030000 41A2000C
3863FFFF 907F1068
807F1990 48000000


	-==-


Wall Bracing
Its main purpose is to get rid of wall infinites.
If you are on the ground and you're moving into a wall (either by purposely walking/running into it or, more likely, if you're being shined into the wall), if you get hit by a non-launching attack, you can press the analog stick left or right to brace against the wall, which will make your feet leave the ground. The direction you press doesn't matter, and this was purposefully done to help with Smash DI (which is actually just a justification for my laziness. - _glook).
<https://smashboards.com/threads/melee-gecko-codes-guide-and-discussion.327311/post-19254768>
<https://youtu.be/pFbPOTBDKgY>
[_glook]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8008DFAC ---- 2C1C0003 -> Branch

2C1C0003 41820030
887D0724 70630004
2C030004 40A20020
807D0620 2C030000
4082000C 38600001
48000008 38600000
2C030000 48000000


	-==-


Tap Jump from Grounded Jump means Death
[flieskiller]
1.02
040cb504 60000000
C20CB514 00000007
82062344 2C100001
38C00000 40820018
3E00800D 62103BC8
7E0803A6 4E800021
48000014 3E008006
621093AC 7E0803A6
4E800021 00000000


	-==-


Disable Star KOs
Deaths off of the top of the screen happen as they do on the side/bottom blastzones.
[Dan Salvato]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x800d3258 --- 41820010 -> 60000000


	-==-


20XX Neutral Spawn for Singles and Doubles
More info here:
https://smashboards.com/threads/forced-and-fixed-neutral-spawn-hack-singles-and-doubles.364075/
[Achilles, Jorgasms]
1.02
C2263058 00000030
39E00000 3A000000
3E408048 625307FD
91F206D8 91F206DC
8A3207C8 625206D7
2C110001 4182004C
39EF0001 8E930024
2C140003 4182000C
9A130004 3A100001
2C0F0004 41A0FFE4
48000130 39E00000
3A0000FF 3E408048
62520801 39EF0001
9E120024 2C0F0004
41A0FFF4 4800010C
3E208048 623106DC
39EF0001 3A520001
8E930024 89D30008
2C140003 41A2FFC0
2C0E0000 40820010
3A000000 8A910000
48000024 2C0E0001
40820010 3A000001
8E910001 48000010
3A000002 8E910002
48000004 3A940001
2C140003 40A0FF80
9A910000 9A120000
2C0F0004 41A0FF94
39E00000 3E208048
623106DB 3AA00000
39EF0001 8E910001
2C140001 40800008
48000010 3AB50001
2C150003 40A0FF40
2C0F0003 41A0FFDC
39E00000 3A310127
39C000FF 3A8000FF
39EF0001 8E110024
2C0E00FF 40820010
7E128378 39C00000
4800002C 7C109000
4082000C 39C00003
4800001C 2C1400FF
40820010 39C00001
3A800000 48000008
39C00002 99D1FFFC
2C0F0004 41A0FFB4
38840001 00000000
C21C0A48 0000001A
3DE0801B 61EFFFA8
7C0F6000 418200BC
3DC08048 A1CE0686
3DE08049 61EFED70
81EF0000 2C0E001F
4082001C 3E00C242
3E204242 3E404230
3A600000 960F0598
4800002C 2C0E001C
40820040 3E00C23A
62106666 3E20423D
62318E70 3E404214
3A600000 960F0854
48000004 924F0004
962F0040 924F0004
962F0040 926F0004
960F0040 926F0004
2C0E0020 40820014
3A000041 9E0F0650
3A0000C1 9A0F0040
2C0E0008 40820024
3E004270 3E20C270
3A400000 39EF4748
960F4748 924F0004
962F0040 924F0004
7D8803A6 00000000


	-==-


!

Tournament Style Overtime
Ties by percent in regulation and makes sudden death mode 3 minutes.
Video demo here: https://youtu.be/uIDf3GBwotM
[_glook]
1.02
C21A415C 00000048
3803FFFF 2C000003
40820230 2C0B0020
41820228 3821FFEC
3C80FFFF 6084FFFF
90810000 90810004
90810008 9081000C
90810010 3A800000
3AA00000 3E208045
62313080 3E408047
62529E00 3E00803F
62100E06 3C808048
608407C8 88840000
2C040001 4182004C
80910008 2C040003
41820028 88920001
5484801E A0B10060
7C842B78 56A5103A
7CA50A14 90850004
7EB409AE 3A940001
3A310E90 3A5200A8
3AB50001 2C150004
4082FFC0 4800005C
80910008 2C040003
41820038 88D00000
54C5103A 7CA50A14
80850004 2C040000
40800014 7CD409AE
3A940001 88920001
5484801E A0D10060
7C843214 90850004
3A100024 3A310E90
3A5200A8 3AB50001
2C150004 4082FFAC
3AA00000 7E41AA14
8A520000 2C1200FF
4182005C 7EB4AB78
2C140000 4182003C
7E21A214 8A31FFFF
5646103A 5625103A
7CC60A14 7CA50A14
80C60004 80A50004
7C062800 40A00014
7E01A214 9A300000
3A94FFFF 4BFFFFC4
7E81A214 9A540000
3AB50001 2C150004
4082FF9C 38600005
38000004 3A400000
3E20FFFF 6231FFFF
3A000000 7CC18214
88C60000 2C0600FF
41820088 54C5103A
7CA50A14 80A50004
7C112800 40A20018
2C120000 40A20018
38600004 38000003
4800000C 7CB12B78
7E128378 3E808048
629407C8 8A940000
2C140000 41A20020
3CA08047 60A59DC8
1CC6000C 7CC62A14
9A460000 9A460001
4800001C 3CA08047
60A59E00 1CC600A8
7CC62A14 9A460001
9A460002 3A100001
2C100004 4082FF70
38210014 3A000000
3A200000 3A400000
3A800000 3AA00000
60000000 00000000
C216DD38 00000008
3CA08046 60A5DB6E
88650000 2C030000
41A20024 38600004
3CA08047 60A59D35
98650000 38600032
3CA08046 60A5DB68
98650000 38BF24C0
881E0000 00000000
C216DDCC 00000004
3C608046 6063DB6E
88630000 2C030000
41A20008 380000B4
901F0028 00000000
C2067E78 00000005
3C608046 6063DB6E
88630000 2C030000
41A20010 38600000
907B1830 48000008
D01B1830 00000000
C20343B4 00000004
3C608046 6063DB6E
88630000 2C030000
41A20008 3BC00000
7FDD032E 00000000
041C0EB4 48000094
04165dc4 38040000
04165e0c 38040000
04165e54 38040000

v1.00
C21A3414 00000048
3803FFFF 2C000003
40820230 2C0B0020
41820228 3821FFEC
3C80FFFF 6084FFFF
90810000 90810004
90810008 9081000C
90810010 3A800000
3AA00000 3E208045
623110C0 3E408047
62527E38 3E00803E
6210EF76 3C808047
6084E800 88840000
2C040001 4182004C
80910008 2C040003
41820028 88920001
5484801E A0B10060
7C842B78 56A5103A
7CA50A14 90850004
7EB409AE 3A940001
3A310E90 3A5200A8
3AB50001 2C150004
4082FFC0 4800005C
80910008 2C040003
41820038 88D00000
54C5103A 7CA50A14
80850004 2C040000
40800014 7CD409AE
3A940001 88920001
5484801E A0D10060
7C843214 90850004
3A100024 3A310E90
3A5200A8 3AB50001
2C150004 4082FFAC
3AA00000 7E41AA14
8A520000 2C1200FF
4182005C 7EB4AB78
2C140000 4182003C
7E21A214 8A31FFFF
5646103A 5625103A
7CC60A14 7CA50A14
80C60004 80A50004
7C062800 40A00014
7E01A214 9A300000
3A94FFFF 4BFFFFC4
7E81A214 9A540000
3AB50001 2C150004
4082FF9C 38600005
38000004 3A400000
3E20FFFF 6231FFFF
3A000000 7CC18214
88C60000 2C0600FF
41820088 54C5103A
7CA50A14 80A50004
7C112800 40A20018
2C120000 40A20018
38600004 38000003
4800000C 7CB12B78
7E128378 3E808047
6294E800 8A940000
2C140000 41A20020
3CA08047 60A57E00
1CC6000C 7CC62A14
9A460000 9A460001
4800001C 3CA08047
60A57E38 1CC600A8
7CC62A14 9A460001
9A460002 3A100001
2C100004 4082FF70
38210014 3A000000
3A200000 3A400000
3A800000 3AA00000
60000000 00000000
C216D330 00000008
3CA08046 60A5BBA6
88650000 2C030000
41A20024 38600004
3CA08047 60A57D6D
98650000 38600032
3CA08046 60A5BBA0
98650000 38BF24C0
881E0000 00000000
C216D3C4 00000004
3C608046 6063BBA6
88630000 2C030000
41A20008 380000B4
901F0028 00000000
C2067D54 00000005
3C608046 6063BBA6
88630000 2C030000
41A20010 38600000
907B1830 48000008
D01B1830 00000000
C2034334 00000006
3C608046 6063BBA6
88630000 2C030000
41A20014 38600001
3BDD0000 987E008E
3BC00000 7FDD032E
60000000 00000000
041BFEF8 48000094
041655BC 38040000
04165604 38040000
0416564C 38040000


	-==-


L+R+A+Start Resets Game While Pause Is Off
If you use 20XX, it will just boot right back to the CSS. Downside is that you lose character/name selection and stars.
[Sickolas]
1.02
C2050D68 0000000E
3E808045 6294BF18
82B40000 56B305EF
40A20058 3AA00000
3E808046 6294B108
82740000 567201CF
4182001C 567200C7
41820014 56720253
4182000C 56720295
40820018 3AB50001
2C150004 4080001C
3A94000C 4BFFFFCC
3D80801A 618C4510
7D8903A6 4E800421
7C0802A6 00000000


	-==-


Grab Infinite Removal v3
This removes grab infinites by making any attack knock opponents out of a grab when the grip has run out. This keeps everything else about grabs the same except for the ability to extend grabs into infinity.

This revision of the code ignores the following action states from grabber:
0x0D9 - "CatchAttack" for all characters
0x163 - Up+B catch for Captain Falcon and Ganondorf
<https://smashboards.com/threads/grab-infinite-removal-code.412693/>
[Glook, Punkline]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -

NTSC 1.02 --- 0x8008EE48 ---- 2c000000 -> Branch
807B0010 2C0300D9 4182004C 2C030163 40A20018 807B0004 2C030002 2C830019 
4C423382 41820030 887D2226 70630020 40820024 C01D1A4C 38600000 9061FFF0 
C021FFF0 FC000840 4C401382 40820008 38000000 2C000000 00000000

PAL 1.00 ---- 0x8008F500 ---- 2c000000 -> Branch
807B0010 2C0300D9 4182004C 2C030163 40A20018 807B0004 2C030002 2C830019 
4C423382 41820030 887D2226 70630020 40820024 C01D1A4C 38600000 9061FFF0 
C021FFF0 FC000840 4C401382 40820008 38000000 2C000000 00000000

NTSC 1.01 --- 0x8008EC88 ---- 2c000000 -> Branch
807B0010 2C0300D9 4182004C 2C030163 40A20018 807B0004 2C030002 2C830019 
4C423382 41820030 887D2226 70630020 40820024 C01D1A4C 38600000 9061FFF0 
C021FFF0 FC000840 4C401382 40820008 38000000 2C000000 00000000

NTSC 1.00 --- 0x8008EBA0 ---- 2c000000 -> Branch
807B0010 2C0300D9 4182004C 2C030163 40A20018 807B0004 2C030002 2C830019 
4C423382 41820030 887D2226 70630020 40820024 C01D1A4C 38600000 9061FFF0 
C021FFF0 FC000840 4C401382 40820008 38000000 2C000000 00000000


	-==-


Throw Bug Fix v2
Fixes the hitstun differences between ports on throws, to make it so they all have the full amount of hitstun, i.e. the first frame of hitstun is never skipped.
<https://smashboards.com/threads/throw-hitstun-bug-fix.444506/post-21603633>
[Achilles]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8008E250 ---- 38C00000 -> Branch

38C00000 8118002C
80E818C4 8928000C
7C093800 41800050
80E80010 2C0700EF
41800044 2C0700F3
4181003C 3CE03F80
90E20000 C0820000
C0A82340 FC84282A
D0882340 3CE08006
60E793AC 7CE803A6
4E800021 3CE08008
60E7E260 7CE803A6
4E800020 00000000


	-==-


Freeze glitch fix
<https://smashboards.com/threads/bug-fixes.471624/post-22930938>
[tauKhan]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x801239A8 ---- 901F1A5C -> 60000000


	-==-


Invisible ceiling glitch fix
<https://smashboards.com/threads/bug-fixes.471624/post-22930938>
[tauKhan]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8006BB14 ---- D01E0004 -> D01D0004


	-==-


Detection Bubbles Continue Hurtbox Collision Check
Phenomenon that affects Falcon and Ganon side-b ignoring projectiles
<https://smashboards.com/threads/bug-fixes.471624/post-22931202>
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x800796E0 ---- 3A400001 -> 60000000


	-==-


Update TopN Location In ECB Calculation
This updates character model locations before checks are made for environment collisions and other positional calculations. This can prevent weird looking stage clipping in some cases, especially for grabs.
<https://smashboards.com/threads/bug-fixes.471624/post-23430430>
[tauKhan]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8004250C ---- 3BC40000 -> Branch

7C7F1B78 7C9E2378
807F0000 80830028
8063002C 800300B0
90040038 800300B4
9004003C 800300B8
90040040 7C832378
3D808037 618C32E8
7D8903A6 4E800421
7FE3FB78 00000000


	-==-


Fastfall Whenever
<https://smashboards.com/threads/misc-character-codes.446554/post-21701562>
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8007D54C ---- 4080005C -> 60000000


	-==-


Disable Wobbling
Disable Ice Climbers' grab infinite. Opponent breaks out after being hit by Nana 3 times.
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x800DA9DC ---- C02296E8 -> Branch

38600000 987C2350
3860FFFF B07C2352
7F43D378 00000000

------------- 0x8008F090 ---- 801B0010 -> Branch

807B0010 2C0300DF
418000D4 2C0300E4
418100CC 807B1A58
2C030000 418200C0
8063002C 88832222
5484077B 418200B0
8863000C 38800001
3D808003 618C418C
7D8903A6 4E800421
2C030000 41820090
809B1868 7C032000
41820034 A0A40000
2C050006 40820078
80A4002C 80850518
7C032000 40820068
A0650DA8 A09B2352
7C032000 41820058
48000018 80A3002C
A0652088 A09B2352
7C032000 41820040
B07B2352 887B2350
38630001 987B2350
2C030003 41800028
807B1A58 3D80800D
618CA698 7D8903A6
4E800421 3D808008
618CF0C8 7D8903A6
4E800420 801B0010
60000000 00000000