------------------------------------------------------------------------
-- copyAbs.decTest -- quiet copy and set sign to zero                 --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 999
minExponent: -999

-- Sanity check
cpax001 copyabs       +7.50  -> 7.50

-- Infinities
cpax011 copyabs  Infinity    -> Infinity
cpax012 copyabs  -Infinity   -> Infinity

-- NaNs, 0 payload
cpax021 copyabs         NaN  -> NaN
cpax022 copyabs        -NaN  -> NaN
cpax023 copyabs        sNaN  -> sNaN
cpax024 copyabs       -sNaN  -> sNaN

-- NaNs, non-0 payload
cpax031 copyabs       NaN10  -> NaN10
cpax032 copyabs      -NaN15  -> NaN15
cpax033 copyabs      sNaN15  -> sNaN15
cpax034 copyabs     -sNaN10  -> sNaN10
cpax035 copyabs       NaN7   -> NaN7
cpax036 copyabs      -NaN7   -> NaN7
cpax037 copyabs      sNaN101 -> sNaN101
cpax038 copyabs     -sNaN101 -> sNaN101

-- finites
cpax101 copyabs          7   -> 7
cpax102 copyabs         -7   -> 7
cpax103 copyabs         75   -> 75
cpax104 copyabs        -75   -> 75
cpax105 copyabs       7.10   -> 7.10
cpax106 copyabs      -7.10   -> 7.10
cpax107 copyabs       7.500  -> 7.500
cpax108 copyabs      -7.500  -> 7.500

-- zeros
cpax111 copyabs          0   -> 0
cpax112 copyabs         -0   -> 0
cpax113 copyabs       0E+6   -> 0E+6
cpax114 copyabs      -0E+6   -> 0E+6
cpax115 copyabs     0.0000   -> 0.0000
cpax116 copyabs    -0.0000   -> 0.0000
cpax117 copyabs      0E-141  -> 0E-141
cpax118 copyabs     -0E-141  -> 0E-141

-- full coefficients, alternating bits
cpax121 copyabs   268268268        -> 268268268
cpax122 copyabs  -268268268        -> 268268268
cpax123 copyabs   134134134        -> 134134134
cpax124 copyabs  -134134134        -> 134134134

-- Nmax, Nmin, Ntiny
cpax131 copyabs  9.99999999E+999   -> 9.99999999E+999
cpax132 copyabs  1E-999            -> 1E-999
cpax133 copyabs  1.00000000E-999   -> 1.00000000E-999
cpax134 copyabs  1E-1007           -> 1E-1007

cpax135 copyabs  -1E-1007          -> 1E-1007
cpax136 copyabs  -1.00000000E-999  -> 1.00000000E-999
cpax137 copyabs  -1E-999           -> 1E-999
cpax199 copyabs  -9.99999999E+999  -> 9.99999999E+999
