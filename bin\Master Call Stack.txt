

	On boot:

8000522c	__start
80005340	__init_registers
8000541C	__init_hardware
8000535c	__init_data
80335EC0	DBInit
80342FC8	OSInit
8034C410		__OSGetSystemTime
			OSSetArenaLo
			OSSetArenaHi
			OSThreadInit
			OSInitAudioSystem
			OSInitMemoryProtection
800051EC	__check_pad3
8034CABC	__init_user
8015feb4	main
80342FC8		OSInit
			VIInit
			DVDInit
			PADInit
			CARDInit
			OSInitAlarm
			zz_0225374_
8015fda4		checkFor develop.ini		(nop 8015fdc8 to boot in debug mode?)
			OSGetConsoleSimulatedMemSize
			OSGetArenaHi
			OSGetArenaLo
			HSD_SetInitParameter	(called a few times)
			ErrorHandler_Init
			HSD_AllocateXFB
			HSD_AllocateFIFO
			GXInit
			HSD_GXSetFifoObj
			HSD_InitComponent
803767b8			HSD_VIInit
80375258			HSD_GXInit
			GXSetMisc
			Audio_Initialize
			HSD_VISetUserPostRetraceCallback
			HSD_VISetUserGXDrawDoneCallback
			HSD_VISetBlack
			Heap_InitalizePreloadFileSlotStructs/CreateLinkedList
			Heap_InitPersistentHeaps
			Preload_InitializePreloadTableOnBoot
8001C5BC		MemoryCard_Init
			[multiple unknown functions]		(includes checks for USB or other peripherals?)
			Text_InitSisLookup
			InitializeStaticMemRegions (Match,Nametag,Memcard)
				Zero_AreaLength
				[checks for usa.ini]		(change 8015fbd4 to hard branch to spoof usa.ini not present)
				Language_SetLanguageSetting
				LanguageSwitch_SetLanguage
8015f600			NameTag_InitializeNameTagArea
					Zero_AreaLength
					Load_TyDatai.usd
					Memcard_InitializeUnk
					Trophies_CheckAmountOfThisTrophyObtained
					Stage_CheckStageUnlockStatus
				loadImportantAudioFiles_main.ssm and others
					OSGetSoundMode_Prefuncton
					DSP_Process
					LanguageSwitch_LoadCurrent_CompareUS
					SFX_StopAll
					SSM_FreeAllLiveSSMData
803889B8				loadCharAudioFile (r3=string, r4=bankID, r5=callback function, r6=typedef)		(needs rename; actually loading main.ssm)
					Audio_AsyncronousLoad
					Audio_UnloadSmash2.sem
					Audio_LoadSmash2.sem
					SSM_FreeAllLiveSSMData
********				[load nr_name.ssm]
********				[load pokemon.ssm]
800289a0				[load end.ssm]
				Deflicker_ApplyOnBoot
8016000c    [back in main]
********		[check if USB is connected?]
			[various USB functions]
			LooseBallAnims::Destroy
			[series of OSReports]
800154bc		Heap_StoreARAMBoundsToPersistentHeapTable
			GetTimeAndDateOnStack
			[a few unknown functions]
801a4510		Scene_Main
801a43a0			Scene_ProcessMajor 	(r27=MajorSceneID, r30=MajorSceneTable, r31=SceneController)
801a4014				Scene_ProcessMinor 	(r25=MinorSceneData, r26=MinorSceneFuncList, r27=MinorSceneTable, r28=MajorSceneTable)
						Heap_UpdatePersistentHeapsOnSceneChange
						Scene_MinorIDToMinorSceneFunctionTable
801a4bd4					ScenePrep_Common
							GObjList_Initialize
							Develop_InitCamera
							loadFile_LbRb.dat

8016e934					SceneLoad_InGame
8016e730		-	-	-	-	StartMelee
802254b8							DevelopMode_DbCo.dat_Load&More
								SetGameEngineRunSpeed(r3=?,r4=speed)
								CameraInfo_Init
								InitalizeCamera
								InitializeMatchInfo+ThinkStruct
									Playerblock_Initialize		(called multiple times, for each player)
								Load_LbRf.dat
								loadFile_EfData.dat			(called multiple times)
								loadFile_PdPm.dat
801c0378							Stage_SetupSomething
802251E8	-	-	-	-	-	-	Stages_LoadStagePrefunction
801c0754								Stages_LoadStage_InitAndLoad
										Stages_LoadStage_LoadFile+CopyDATPointers
											Stages_LoadStage_LoadFile
											Archive_GetSymbolOffset (r3 = start of dat, r4 = string to find)
801c28cc									Music_CheckStagePlaylist
801c5878									checkToLoadTrophyTextureDAT?
								CountPlayersInMatch
80266f70	-	-	-	-	-	-	checkToLoadItems		(called at 8016e864)
									MatchInfo_ItemFrequencyLoad
8027870c								fileLoad_ItCo
										Language_CheckIfEnglish
80017040									loadFile_GameEngineFiles_Prefunction	(prepares loading files collected above)
8001819c										Preload_LoadGameEngineFiles	(loads LbRb, LbRf, EfCoData, EfMnData, 
															 PdPm, [stage], TyDatai, ItCo, PlCo, [charFiles])
												File_AppendExtension
										[8001711C: Pl__.dat file loaded and initialized. pointer to file start in r6]
								InitializeItemStructSkeleton
								Items_InitItemSpawnThinkStruct
								Audio_LoadStageAndCharAudio (andSetDSPEchoLevel)
								StageInitialization_Prefunction
								CameraIno_GetDefaultFOV
								CameraInfo_FOVStore
8016e2bc							PlayerBlock_LoadPlayers / MatchStartRead/WriteValues
80036da4								CreatePlayerBlockAllocationSkeleton?_Pre_Prefunction
										CreatePlayerBlockAllocationSkeleton?_Prefunction
											CreatePlayerBlockAllocationSkeleton?
												UnloadCharacterDATFiles
												FileLoad_PlCo.dat
									Player_InitializeSpawnPositions?
										Playerblock_LoadSlotType
										SpawnPoint_GetXYZFromIDInput
									Playerblock_LoadSlotType
									SpawnPoint_GetXYZFromIDInput
800327dc		-	-	-	-	-	-	PlayerBlock_StoreInitialX/YCoords
									PlayerBlock_LoadControllerIndex
80031AD0								SetupPlayerSlot
80068e98									AllocateAndInitPlayer
											GObj_Create (r3=GObjType,r4=Subclass,r5=BehaviorBitflags)
											GObj_AddGXLink (r3 =GObj,r4=Function,r5=gx_link,r6=gx_pri)
											HSD_ObjAlloc		<- Init character data struct
											HSD_ObjAlloc
											GObj_AddUserData (r3=entity,r4=UserDataKind,r5=destructor,r6=dataspace)
8008572c										fileLoad_PlXX.dat	(r3=int char ID)
80017040											loadFile_GameEngineFiles_Prefunction	(r4=filenamePointer)
8001819c												Preload_LoadGameEngineFiles
80017ebc													Preload_AllocateAndLoadFile (DATfile_StageFileLoad_CharFileLoad?)
															Archive_InitOnLoad
											
80225298							Stage_RunOnLoadFunction_Prefunction
801c5800							Pause_UnpauseStoreCameraType
8002f3ac							Camera_CorrectPosition
802F390C							InitHUD
								StartMelee_LoadMusic

		(back to SceneLoad_InGame <-)		HUD_Timer_Initialize
							HUD_Create
								HUD_SpacingAdjust
								HUD_PlayerCreate

		[breakpoint 8016e94c for line just after StartMelee call]

		[at 801a40d8 sceneMinor may blrl to SceneLoad_InGame (will do so at first hit). And then returns to 801a40e8]


801a4d34					updateFunction			(back to Scene_ProcessMinor)
							HSD_PadFlushQueue
							DisplayMemcardSaveBanner
							USBScreenshot_InitMCC?
		[801a4d98 = start of updateFunction loop]
							RunDiscCheck		(checks for memory card for saving?)
								DiscError_DisplayMessageMain
								MemoryCard_CheckToSaveData
							HSD_PadGetResetSwitch
						-	HSD_PerfSetStartTime
80019900						ButtonPresses_CopyInputsToStruct
							[other button functions]
8016d800						SceneThink_InGame
								SceneThink_VSMode
									VSMode_EndGame
									MenuController_ChangeScreenMinor
									[1P Mode advenuture checks]
									DevelopMode_FrameAdvanceCheck
									VSMode_CheckForGameEnd
8016ca68								Pause_IngameCheckPauseOnOff
									Timer_EveryFrame_CheckToPlayDefeatedSFX
								GAME!_Think
8037e23c					-	HSD_PerfSetCPUTime
							OSCheckActiveThreads
							GXInvalidateVtxCache
							GXInvalidateTexAll
							HSD_StartRender
								GXSetPixelFmt
								GXSetFieldMode
80390fc0						CameraTaskScheduler
								HUD_OffscreenThink
									CameraType_CompareTo_0x6
									HSD_CObjGetOrtho
									HSD_CObjSetCurrent
								DrawCamera+ECBDevelopBoxes
									Camera_AdjustNearAndFar
									Shadows_UpdateShadowPosition
803676f8								HSD_CObjEraseScreen
									HSD_LObjDeleteCurrentAll
									TextureDisplay_Stage_SetToDisplay
800311ec		-	-	-	-	-	-	RenderPlayerModels
									RenderItemModels
									HSD_FogSet
									StageBGFlagToggles_Unk
									CameraInfo_ECB/CollLinks_Draw
									Blastzone/CameraLimits_Draw
									DrawItemSpawnDevelopBoxes
									HSD_CObjEndCurrent
								DrawNameTagEachFrame
								CObjThink_Common
								HUDRender
								CObjThink_Common
								CObjThink_Common
								
							HSD_PerfSetDrawTime
							HSD_VICopyXFBASync
							DevelopMode_CheckForUSBScreenshot
							HSD_PerfSetTotalTime
							HSD_PerfInitStat
8022892c						DevelopMode_CheckForUSBScreenshot









