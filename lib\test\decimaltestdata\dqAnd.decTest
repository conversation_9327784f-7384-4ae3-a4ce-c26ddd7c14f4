------------------------------------------------------------------------
-- dqAnd.decTest -- digitwise logical AND for decQuads                --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check (truth table)
dqand001 and             0    0 ->    0
dqand002 and             0    1 ->    0
dqand003 and             1    0 ->    0
dqand004 and             1    1 ->    1
dqand005 and          1100 1010 -> 1000
-- and at msd and msd-1
--           1234567890123456789012345678901234
dqand006 and 0000000000000000000000000000000000 0000000000000000000000000000000000 ->                  0
dqand007 and 0000000000000000000000000000000000 1000000000000000000000000000000000 ->                  0
dqand008 and 1000000000000000000000000000000000 0000000000000000000000000000000000 ->                  0
dqand009 and 1000000000000000000000000000000000 1000000000000000000000000000000000 ->   1000000000000000000000000000000000
dqand010 and 0000000000000000000000000000000000 0000000000000000000000000000000000 ->                  0
dqand011 and 0000000000000000000000000000000000 0100000000000000000000000000000000 ->                  0
dqand012 and 0100000000000000000000000000000000 0000000000000000000000000000000000 ->                  0
dqand013 and 0100000000000000000000000000000000 0100000000000000000000000000000000 ->    100000000000000000000000000000000

-- Various lengths
--           1234567890123456789012345678901234

dqand601 and 0111111111111111111111111111111111 1111111111111111111111111111111111  ->  111111111111111111111111111111111
dqand602 and 1011111111111111111111111111111111 1111111111111111111111111111111111  -> 1011111111111111111111111111111111
dqand603 and 1101111111111111111111111111111111 1111111111111111111111111111111111  -> 1101111111111111111111111111111111
dqand604 and 1110111111111111111111111111111111 1111111111111111111111111111111111  -> 1110111111111111111111111111111111
dqand605 and 1111011111111111111111111111111111 1111111111111111111111111111111111  -> 1111011111111111111111111111111111
dqand606 and 1111101111111111111111111111111111 1111111111111111111111111111111111  -> 1111101111111111111111111111111111
dqand607 and 1111110111111111111111111111111111 1111111111111111111111111111111111  -> 1111110111111111111111111111111111
dqand608 and 1111111011111111111111111111111111 1111111111111111111111111111111111  -> 1111111011111111111111111111111111
dqand609 and 1111111101111111111111111111111111 1111111111111111111111111111111111  -> 1111111101111111111111111111111111
dqand610 and 1111111110111111111111111111111111 1111111111111111111111111111111111  -> 1111111110111111111111111111111111
dqand611 and 1111111111011111111111111111111111 1111111111111111111111111111111111  -> 1111111111011111111111111111111111
dqand612 and 1111111111101111111111111111111111 1111111111111111111111111111111111  -> 1111111111101111111111111111111111
dqand613 and 1111111111110111111111111111111111 1111111111111111111111111111111111  -> 1111111111110111111111111111111111
dqand614 and 1111111111111011111111111111111111 1111111111111111111111111111111111  -> 1111111111111011111111111111111111
dqand615 and 1111111111111101111111111111111111 1111111111111111111111111111111111  -> 1111111111111101111111111111111111
dqand616 and 1111111111111110111111111111111111 1111111111111111111111111111111111  -> 1111111111111110111111111111111111
dqand617 and 1111111111111111011111111111111111 1111111111111111111111111111111111  -> 1111111111111111011111111111111111
dqand618 and 1111111111111111101111111111111111 1111111111111111111111111111111111  -> 1111111111111111101111111111111111
dqand619 and 1111111111111111110111111111111111 1111111111111111111111111111111111  -> 1111111111111111110111111111111111
dqand620 and 1111111111111111111011111111111111 1111111111111111111111111111111111  -> 1111111111111111111011111111111111
dqand621 and 1111111111111111111101111111111111 1111111111111111111111111111111111  -> 1111111111111111111101111111111111
dqand622 and 1111111111111111111110111111111111 1111111111111111111111111111111111  -> 1111111111111111111110111111111111
dqand623 and 1111111111111111111111011111111111 1111111111111111111111111111111111  -> 1111111111111111111111011111111111
dqand624 and 1111111111111111111111101111111111 1111111111111111111111111111111111  -> 1111111111111111111111101111111111
dqand625 and 1111111111111111111111110111111111 1111111111111111111111111111111111  -> 1111111111111111111111110111111111
dqand626 and 1111111111111111111111111011111111 1111111111111111111111111111111111  -> 1111111111111111111111111011111111
dqand627 and 1111111111111111111111111101111111 1111111111111111111111111111111111  -> 1111111111111111111111111101111111
dqand628 and 1111111111111111111111111110111111 1111111111111111111111111111111111  -> 1111111111111111111111111110111111
dqand629 and 1111111111111111111111111111011111 1111111111111111111111111111111111  -> 1111111111111111111111111111011111
dqand630 and 1111111111111111111111111111101111 1111111111111111111111111111111111  -> 1111111111111111111111111111101111
dqand631 and 1111111111111111111111111111110111 1111111111111111111111111111111111  -> 1111111111111111111111111111110111
dqand632 and 1111111111111111111111111111111011 1111111111111111111111111111111111  -> 1111111111111111111111111111111011
dqand633 and 1111111111111111111111111111111101 1111111111111111111111111111111111  -> 1111111111111111111111111111111101
dqand634 and 1111111111111111111111111111111110 1111111111111111111111111111111111  -> 1111111111111111111111111111111110

dqand641 and 1111111111111111111111111111111111 0111111111111111111111111111111111  ->  111111111111111111111111111111111
dqand642 and 1111111111111111111111111111111111 1011111111111111111111111111111111  -> 1011111111111111111111111111111111
dqand643 and 1111111111111111111111111111111111 1101111111111111111111111111111111  -> 1101111111111111111111111111111111
dqand644 and 1111111111111111111111111111111111 1110111111111111111111111111111111  -> 1110111111111111111111111111111111
dqand645 and 1111111111111111111111111111111111 1111011111111111111111111111111111  -> 1111011111111111111111111111111111
dqand646 and 1111111111111111111111111111111111 1111101111111111111111111111111111  -> 1111101111111111111111111111111111
dqand647 and 1111111111111111111111111111111111 1111110111111111111111111111111111  -> 1111110111111111111111111111111111
dqand648 and 1111111111111111111111111111111111 1111111011111111111111111111111111  -> 1111111011111111111111111111111111
dqand649 and 1111111111111111111111111111111111 1111111101111111111111111111111111  -> 1111111101111111111111111111111111
dqand650 and 1111111111111111111111111111111111 1111111110111111111111111111111111  -> 1111111110111111111111111111111111
dqand651 and 1111111111111111111111111111111111 1111111111011111111111111111111111  -> 1111111111011111111111111111111111
dqand652 and 1111111111111111111111111111111111 1111111111101111111111111111111111  -> 1111111111101111111111111111111111
dqand653 and 1111111111111111111111111111111111 1111111111110111111111111111111111  -> 1111111111110111111111111111111111
dqand654 and 1111111111111111111111111111111111 1111111111111011111111111111111111  -> 1111111111111011111111111111111111
dqand655 and 1111111111111111111111111111111111 1111111111111101111111111111111111  -> 1111111111111101111111111111111111
dqand656 and 1111111111111111111111111111111111 1111111111111110111111111111111111  -> 1111111111111110111111111111111111
dqand657 and 1111111111111111111111111111111111 1111111111111111011111111111111111  -> 1111111111111111011111111111111111
dqand658 and 1111111111111111111111111111111111 1111111111111111101111111111111111  -> 1111111111111111101111111111111111
dqand659 and 1111111111111111111111111111111111 1111111111111111110111111111111111  -> 1111111111111111110111111111111111
dqand660 and 1111111111111111111111111111111111 1111111111111111111011111111111111  -> 1111111111111111111011111111111111
dqand661 and 1111111111111111111111111111111111 1111111111111111111101111111111111  -> 1111111111111111111101111111111111
dqand662 and 1111111111111111111111111111111111 1111111111111111111110111111111111  -> 1111111111111111111110111111111111
dqand663 and 1111111111111111111111111111111111 1111111111111111111111011111111111  -> 1111111111111111111111011111111111
dqand664 and 1111111111111111111111111111111111 1111111111111111111111101111111111  -> 1111111111111111111111101111111111
dqand665 and 1111111111111111111111111111111111 1111111111111111111111110111111111  -> 1111111111111111111111110111111111
dqand666 and 1111111111111111111111111111111111 1111111111111111111111111011111111  -> 1111111111111111111111111011111111
dqand667 and 1111111111111111111111111111111111 1111111111111111111111111101111111  -> 1111111111111111111111111101111111
dqand668 and 1111111111111111111111111111111111 1111111111111111111111111110111111  -> 1111111111111111111111111110111111
dqand669 and 1111111111111111111111111111111111 1111111111111111111111111111011111  -> 1111111111111111111111111111011111
dqand670 and 1111111111111111111111111111111111 1111111111111111111111111111101111  -> 1111111111111111111111111111101111
dqand671 and 1111111111111111111111111111111111 1111111111111111111111111111110111  -> 1111111111111111111111111111110111
dqand672 and 1111111111111111111111111111111111 1111111111111111111111111111111011  -> 1111111111111111111111111111111011
dqand673 and 1111111111111111111111111111111111 1111111111111111111111111111111101  -> 1111111111111111111111111111111101
dqand674 and 1111111111111111111111111111111111 1111111111111111111111111111111110  -> 1111111111111111111111111111111110
dqand675 and 0111111111111111111111111111111111 1111111111111111111111111111111110  ->  111111111111111111111111111111110
dqand676 and 1111111111111111111111111111111111 1111111111111111111111111111111110  -> 1111111111111111111111111111111110

dqand021 and 1111111111111111 1111111111111111  ->  1111111111111111
dqand024 and 1111111111111111  111111111111111  ->   111111111111111
dqand025 and 1111111111111111   11111111111111  ->    11111111111111
dqand026 and 1111111111111111    1111111111111  ->     1111111111111
dqand027 and 1111111111111111     111111111111  ->      111111111111
dqand028 and 1111111111111111      11111111111  ->       11111111111
dqand029 and 1111111111111111       1111111111  ->        1111111111
dqand030 and 1111111111111111        111111111  ->         111111111
dqand031 and 1111111111111111         11111111  ->          11111111
dqand032 and 1111111111111111          1111111  ->           1111111
dqand033 and 1111111111111111           111111  ->            111111
dqand034 and 1111111111111111            11111  ->             11111
dqand035 and 1111111111111111             1111  ->              1111
dqand036 and 1111111111111111              111  ->               111
dqand037 and 1111111111111111               11  ->                11
dqand038 and 1111111111111111                1  ->                 1
dqand039 and 1111111111111111                0  ->                 0

dqand040 and 1111111111111111    1111111111111111 ->  1111111111111111
dqand041 and  111111111111111    1111111111111111 ->   111111111111111
dqand042 and  111111111111111    1111111111111111 ->   111111111111111
dqand043 and   11111111111111    1111111111111111 ->    11111111111111
dqand044 and    1111111111111    1111111111111111 ->     1111111111111
dqand045 and     111111111111    1111111111111111 ->      111111111111
dqand046 and      11111111111    1111111111111111 ->       11111111111
dqand047 and       1111111111    1111111111111111 ->        1111111111
dqand048 and        111111111    1111111111111111 ->         111111111
dqand049 and         11111111    1111111111111111 ->          11111111
dqand050 and          1111111    1111111111111111 ->           1111111
dqand051 and           111111    1111111111111111 ->            111111
dqand052 and            11111    1111111111111111 ->             11111
dqand053 and             1111    1111111111111111 ->              1111
dqand054 and              111    1111111111111111 ->               111
dqand055 and               11    1111111111111111 ->                11
dqand056 and                1    1111111111111111 ->                 1
dqand057 and                0    1111111111111111 ->                 0

dqand150 and 1111111111  1  ->  1
dqand151 and  111111111  1  ->  1
dqand152 and   11111111  1  ->  1
dqand153 and    1111111  1  ->  1
dqand154 and     111111  1  ->  1
dqand155 and      11111  1  ->  1
dqand156 and       1111  1  ->  1
dqand157 and        111  1  ->  1
dqand158 and         11  1  ->  1
dqand159 and          1  1  ->  1

dqand160 and 1111111111  0  ->  0
dqand161 and  111111111  0  ->  0
dqand162 and   11111111  0  ->  0
dqand163 and    1111111  0  ->  0
dqand164 and     111111  0  ->  0
dqand165 and      11111  0  ->  0
dqand166 and       1111  0  ->  0
dqand167 and        111  0  ->  0
dqand168 and         11  0  ->  0
dqand169 and          1  0  ->  0

dqand170 and 1  1111111111  ->  1
dqand171 and 1   111111111  ->  1
dqand172 and 1    11111111  ->  1
dqand173 and 1     1111111  ->  1
dqand174 and 1      111111  ->  1
dqand175 and 1       11111  ->  1
dqand176 and 1        1111  ->  1
dqand177 and 1         111  ->  1
dqand178 and 1          11  ->  1
dqand179 and 1           1  ->  1

dqand180 and 0  1111111111  ->  0
dqand181 and 0   111111111  ->  0
dqand182 and 0    11111111  ->  0
dqand183 and 0     1111111  ->  0
dqand184 and 0      111111  ->  0
dqand185 and 0       11111  ->  0
dqand186 and 0        1111  ->  0
dqand187 and 0         111  ->  0
dqand188 and 0          11  ->  0
dqand189 and 0           1  ->  0

dqand090 and 011111111  111111111  ->   11111111
dqand091 and 101111111  111111111  ->  101111111
dqand092 and 110111111  111111111  ->  110111111
dqand093 and 111011111  111111111  ->  111011111
dqand094 and 111101111  111111111  ->  111101111
dqand095 and 111110111  111111111  ->  111110111
dqand096 and 111111011  111111111  ->  111111011
dqand097 and 111111101  111111111  ->  111111101
dqand098 and 111111110  111111111  ->  111111110

dqand100 and 111111111  011111111  ->   11111111
dqand101 and 111111111  101111111  ->  101111111
dqand102 and 111111111  110111111  ->  110111111
dqand103 and 111111111  111011111  ->  111011111
dqand104 and 111111111  111101111  ->  111101111
dqand105 and 111111111  111110111  ->  111110111
dqand106 and 111111111  111111011  ->  111111011
dqand107 and 111111111  111111101  ->  111111101
dqand108 and 111111111  111111110  ->  111111110

-- non-0/1 should not be accepted, nor should signs
dqand220 and 111111112  111111111  ->  NaN Invalid_operation
dqand221 and 333333333  333333333  ->  NaN Invalid_operation
dqand222 and 555555555  555555555  ->  NaN Invalid_operation
dqand223 and 777777777  777777777  ->  NaN Invalid_operation
dqand224 and 999999999  999999999  ->  NaN Invalid_operation
dqand225 and 222222222  999999999  ->  NaN Invalid_operation
dqand226 and 444444444  999999999  ->  NaN Invalid_operation
dqand227 and 666666666  999999999  ->  NaN Invalid_operation
dqand228 and 888888888  999999999  ->  NaN Invalid_operation
dqand229 and 999999999  222222222  ->  NaN Invalid_operation
dqand230 and 999999999  444444444  ->  NaN Invalid_operation
dqand231 and 999999999  666666666  ->  NaN Invalid_operation
dqand232 and 999999999  888888888  ->  NaN Invalid_operation
-- a few randoms
dqand240 and  567468689 -934981942 ->  NaN Invalid_operation
dqand241 and  567367689  934981942 ->  NaN Invalid_operation
dqand242 and -631917772 -706014634 ->  NaN Invalid_operation
dqand243 and -756253257  138579234 ->  NaN Invalid_operation
dqand244 and  835590149  567435400 ->  NaN Invalid_operation
-- test MSD
dqand250 and  2000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqand251 and  7000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqand252 and  8000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqand253 and  9000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqand254 and  2000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqand255 and  7000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqand256 and  8000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqand257 and  9000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqand258 and  1000000111000111000111000000000000 2000000111000111000111000000000000 ->  NaN Invalid_operation
dqand259 and  1000000111000111000111000000000000 7000000111000111000111000000000000 ->  NaN Invalid_operation
dqand260 and  1000000111000111000111000000000000 8000000111000111000111000000000000 ->  NaN Invalid_operation
dqand261 and  1000000111000111000111000000000000 9000000111000111000111000000000000 ->  NaN Invalid_operation
dqand262 and  0000000111000111000111000000000000 2000000111000111000111000000000000 ->  NaN Invalid_operation
dqand263 and  0000000111000111000111000000000000 7000000111000111000111000000000000 ->  NaN Invalid_operation
dqand264 and  0000000111000111000111000000000000 8000000111000111000111000000000000 ->  NaN Invalid_operation
dqand265 and  0000000111000111000111000000000000 9000000111000111000111000000000000 ->  NaN Invalid_operation
-- test MSD-1
dqand270 and  0200000111000111000111001000000000 1000000111000111000111100000000010 ->  NaN Invalid_operation
dqand271 and  0700000111000111000111000100000000 1000000111000111000111010000000100 ->  NaN Invalid_operation
dqand272 and  0800000111000111000111000010000000 1000000111000111000111001000001000 ->  NaN Invalid_operation
dqand273 and  0900000111000111000111000001000000 1000000111000111000111000100010000 ->  NaN Invalid_operation
dqand274 and  1000000111000111000111000000100000 0200000111000111000111000010100000 ->  NaN Invalid_operation
dqand275 and  1000000111000111000111000000010000 0700000111000111000111000001000000 ->  NaN Invalid_operation
dqand276 and  1000000111000111000111000000001000 0800000111000111000111000010100000 ->  NaN Invalid_operation
dqand277 and  1000000111000111000111000000000100 0900000111000111000111000000010000 ->  NaN Invalid_operation
-- test LSD
dqand280 and  0010000111000111000111000000000002 1000000111000111000111000100000001 ->  NaN Invalid_operation
dqand281 and  0001000111000111000111000000000007 1000000111000111000111001000000011 ->  NaN Invalid_operation
dqand282 and  0000000111000111000111100000000008 1000000111000111000111010000000001 ->  NaN Invalid_operation
dqand283 and  0000000111000111000111010000000009 1000000111000111000111100000000001 ->  NaN Invalid_operation
dqand284 and  1000000111000111000111001000000000 0001000111000111000111000000000002 ->  NaN Invalid_operation
dqand285 and  1000000111000111000111000100000000 0010000111000111000111000000000007 ->  NaN Invalid_operation
dqand286 and  1000000111000111000111000010000000 0100000111000111000111000000000008 ->  NaN Invalid_operation
dqand287 and  1000000111000111000111000001000000 1000000111000111000111000000000009 ->  NaN Invalid_operation
-- test Middie
dqand288 and  0010000111000111000111000020000000 1000000111000111000111001000000000 ->  NaN Invalid_operation
dqand289 and  0001000111000111000111000070000001 1000000111000111000111000100000000 ->  NaN Invalid_operation
dqand290 and  0000000111000111000111100080000010 1000000111000111000111000010000000 ->  NaN Invalid_operation
dqand291 and  0000000111000111000111010090000100 1000000111000111000111000001000000 ->  NaN Invalid_operation
dqand292 and  1000000111000111000111001000001000 0000000111000111000111000020100000 ->  NaN Invalid_operation
dqand293 and  1000000111000111000111000100010000 0000000111000111000111000070010000 ->  NaN Invalid_operation
dqand294 and  1000000111000111000111000010100000 0000000111000111000111000080001000 ->  NaN Invalid_operation
dqand295 and  1000000111000111000111000001000000 0000000111000111000111000090000100 ->  NaN Invalid_operation
-- signs
dqand296 and -1000000111000111000111000001000000 -0000001110001110001110010000000100 ->  NaN Invalid_operation
dqand297 and -1000000111000111000111000001000000  0000001110001110001110000010000100 ->  NaN Invalid_operation
dqand298 and  1000000111000111000111000001000000 -0000001110001110001110001000000100 ->  NaN Invalid_operation
dqand299 and  1000000111000111000111000001000000  0000001110001110001110000011000100 ->  110000110000110000001000000

-- Nmax, Nmin, Ntiny-like
dqand331 and  2   9.99999999E+999     -> NaN Invalid_operation
dqand332 and  3   1E-999              -> NaN Invalid_operation
dqand333 and  4   1.00000000E-999     -> NaN Invalid_operation
dqand334 and  5   1E-900              -> NaN Invalid_operation
dqand335 and  6   -1E-900             -> NaN Invalid_operation
dqand336 and  7   -1.00000000E-999    -> NaN Invalid_operation
dqand337 and  8   -1E-999             -> NaN Invalid_operation
dqand338 and  9   -9.99999999E+999    -> NaN Invalid_operation
dqand341 and  9.99999999E+999     -18 -> NaN Invalid_operation
dqand342 and  1E-999               01 -> NaN Invalid_operation
dqand343 and  1.00000000E-999     -18 -> NaN Invalid_operation
dqand344 and  1E-900               18 -> NaN Invalid_operation
dqand345 and  -1E-900             -10 -> NaN Invalid_operation
dqand346 and  -1.00000000E-999     18 -> NaN Invalid_operation
dqand347 and  -1E-999              10 -> NaN Invalid_operation
dqand348 and  -9.99999999E+999    -18 -> NaN Invalid_operation

-- A few other non-integers
dqand361 and  1.0                  1  -> NaN Invalid_operation
dqand362 and  1E+1                 1  -> NaN Invalid_operation
dqand363 and  0.0                  1  -> NaN Invalid_operation
dqand364 and  0E+1                 1  -> NaN Invalid_operation
dqand365 and  9.9                  1  -> NaN Invalid_operation
dqand366 and  9E+1                 1  -> NaN Invalid_operation
dqand371 and  0 1.0                   -> NaN Invalid_operation
dqand372 and  0 1E+1                  -> NaN Invalid_operation
dqand373 and  0 0.0                   -> NaN Invalid_operation
dqand374 and  0 0E+1                  -> NaN Invalid_operation
dqand375 and  0 9.9                   -> NaN Invalid_operation
dqand376 and  0 9E+1                  -> NaN Invalid_operation

-- All Specials are in error
dqand780 and -Inf  -Inf   -> NaN Invalid_operation
dqand781 and -Inf  -1000  -> NaN Invalid_operation
dqand782 and -Inf  -1     -> NaN Invalid_operation
dqand783 and -Inf  -0     -> NaN Invalid_operation
dqand784 and -Inf   0     -> NaN Invalid_operation
dqand785 and -Inf   1     -> NaN Invalid_operation
dqand786 and -Inf   1000  -> NaN Invalid_operation
dqand787 and -1000 -Inf   -> NaN Invalid_operation
dqand788 and -Inf  -Inf   -> NaN Invalid_operation
dqand789 and -1    -Inf   -> NaN Invalid_operation
dqand790 and -0    -Inf   -> NaN Invalid_operation
dqand791 and  0    -Inf   -> NaN Invalid_operation
dqand792 and  1    -Inf   -> NaN Invalid_operation
dqand793 and  1000 -Inf   -> NaN Invalid_operation
dqand794 and  Inf  -Inf   -> NaN Invalid_operation

dqand800 and  Inf  -Inf   -> NaN Invalid_operation
dqand801 and  Inf  -1000  -> NaN Invalid_operation
dqand802 and  Inf  -1     -> NaN Invalid_operation
dqand803 and  Inf  -0     -> NaN Invalid_operation
dqand804 and  Inf   0     -> NaN Invalid_operation
dqand805 and  Inf   1     -> NaN Invalid_operation
dqand806 and  Inf   1000  -> NaN Invalid_operation
dqand807 and  Inf   Inf   -> NaN Invalid_operation
dqand808 and -1000  Inf   -> NaN Invalid_operation
dqand809 and -Inf   Inf   -> NaN Invalid_operation
dqand810 and -1     Inf   -> NaN Invalid_operation
dqand811 and -0     Inf   -> NaN Invalid_operation
dqand812 and  0     Inf   -> NaN Invalid_operation
dqand813 and  1     Inf   -> NaN Invalid_operation
dqand814 and  1000  Inf   -> NaN Invalid_operation
dqand815 and  Inf   Inf   -> NaN Invalid_operation

dqand821 and  NaN -Inf    -> NaN Invalid_operation
dqand822 and  NaN -1000   -> NaN Invalid_operation
dqand823 and  NaN -1      -> NaN Invalid_operation
dqand824 and  NaN -0      -> NaN Invalid_operation
dqand825 and  NaN  0      -> NaN Invalid_operation
dqand826 and  NaN  1      -> NaN Invalid_operation
dqand827 and  NaN  1000   -> NaN Invalid_operation
dqand828 and  NaN  Inf    -> NaN Invalid_operation
dqand829 and  NaN  NaN    -> NaN Invalid_operation
dqand830 and -Inf  NaN    -> NaN Invalid_operation
dqand831 and -1000 NaN    -> NaN Invalid_operation
dqand832 and -1    NaN    -> NaN Invalid_operation
dqand833 and -0    NaN    -> NaN Invalid_operation
dqand834 and  0    NaN    -> NaN Invalid_operation
dqand835 and  1    NaN    -> NaN Invalid_operation
dqand836 and  1000 NaN    -> NaN Invalid_operation
dqand837 and  Inf  NaN    -> NaN Invalid_operation

dqand841 and  sNaN -Inf   ->  NaN  Invalid_operation
dqand842 and  sNaN -1000  ->  NaN  Invalid_operation
dqand843 and  sNaN -1     ->  NaN  Invalid_operation
dqand844 and  sNaN -0     ->  NaN  Invalid_operation
dqand845 and  sNaN  0     ->  NaN  Invalid_operation
dqand846 and  sNaN  1     ->  NaN  Invalid_operation
dqand847 and  sNaN  1000  ->  NaN  Invalid_operation
dqand848 and  sNaN  NaN   ->  NaN  Invalid_operation
dqand849 and  sNaN sNaN   ->  NaN  Invalid_operation
dqand850 and  NaN  sNaN   ->  NaN  Invalid_operation
dqand851 and -Inf  sNaN   ->  NaN  Invalid_operation
dqand852 and -1000 sNaN   ->  NaN  Invalid_operation
dqand853 and -1    sNaN   ->  NaN  Invalid_operation
dqand854 and -0    sNaN   ->  NaN  Invalid_operation
dqand855 and  0    sNaN   ->  NaN  Invalid_operation
dqand856 and  1    sNaN   ->  NaN  Invalid_operation
dqand857 and  1000 sNaN   ->  NaN  Invalid_operation
dqand858 and  Inf  sNaN   ->  NaN  Invalid_operation
dqand859 and  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dqand861 and  NaN1   -Inf    -> NaN Invalid_operation
dqand862 and +NaN2   -1000   -> NaN Invalid_operation
dqand863 and  NaN3    1000   -> NaN Invalid_operation
dqand864 and  NaN4    Inf    -> NaN Invalid_operation
dqand865 and  NaN5   +NaN6   -> NaN Invalid_operation
dqand866 and -Inf     NaN7   -> NaN Invalid_operation
dqand867 and -1000    NaN8   -> NaN Invalid_operation
dqand868 and  1000    NaN9   -> NaN Invalid_operation
dqand869 and  Inf    +NaN10  -> NaN Invalid_operation
dqand871 and  sNaN11  -Inf   -> NaN Invalid_operation
dqand872 and  sNaN12  -1000  -> NaN Invalid_operation
dqand873 and  sNaN13   1000  -> NaN Invalid_operation
dqand874 and  sNaN14   NaN17 -> NaN Invalid_operation
dqand875 and  sNaN15  sNaN18 -> NaN Invalid_operation
dqand876 and  NaN16   sNaN19 -> NaN Invalid_operation
dqand877 and -Inf    +sNaN20 -> NaN Invalid_operation
dqand878 and -1000    sNaN21 -> NaN Invalid_operation
dqand879 and  1000    sNaN22 -> NaN Invalid_operation
dqand880 and  Inf     sNaN23 -> NaN Invalid_operation
dqand881 and +NaN25  +sNaN24 -> NaN Invalid_operation
dqand882 and -NaN26    NaN28 -> NaN Invalid_operation
dqand883 and -sNaN27  sNaN29 -> NaN Invalid_operation
dqand884 and  1000    -NaN30 -> NaN Invalid_operation
dqand885 and  1000   -sNaN31 -> NaN Invalid_operation
