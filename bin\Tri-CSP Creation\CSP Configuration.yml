# Ordered Alphabetically
# Key = External Character ID

0x05: # Bowser
  threshold: 40
  centerImageXOffset: -34
  centerImageYOffset: 186
  centerImageScaling: .683
  sideImagesXOffset: -315
  sideImagesYOffset: -62
  sideImagesScaling: .48
  reverseSides: False
  actionState: 0x15b
  frame: 0x41200000
  camX: 0x4172526c
  camY: 0xc00727ed
  camZ: 0x426f382b

0x00: # C. Falcon
  threshold: 40
  centerImageXOffset: 0
  centerImageYOffset: 124
  centerImageScaling: .87
  sideImagesXOffset: -300
  sideImagesYOffset: -64
  sideImagesScaling: .46
  reverseSides: False
  actionState: 0x42
  frame: 0x41c00000
  camX: 0x42004ae7
  camY: 0x40c80aff
  camZ: 0x4197e801

0x01: # DK
  threshold: 40
  centerImageXOffset: -307
  centerImageYOffset: 169
  centerImageScaling: .62
  sideImagesXOffset: -328
  sideImagesYOffset: -134
  sideImagesScaling: .5
  reverseSides: False
  actionState: 0x1b
  frame: 0x40000000
  camX: 0x42726fbe
  camY: 0xc17a10f1
  camZ: 0x420dc295

0x16: # Dr. Mario
  threshold: 40
  centerImageXOffset: 23
  centerImageYOffset: 207
  centerImageScaling: .8
  sideImagesXOffset: -280
  sideImagesYOffset: -55
  sideImagesScaling: .47
  reverseSides: False
  actionState: 0x41
  frame: 0x41000000
  camX: 0x41058c79
  camY: 0x40a05ed7
  camZ: 0x41f53181

0x14: # Falco
  threshold: 40
  centerImageXOffset: 102
  centerImageYOffset: 166
  centerImageScaling: .85
  sideImagesXOffset: -270
  sideImagesYOffset: -40
  sideImagesScaling: .42
  reverseSides: False
  actionState: 0x15a
  frame: 0x40a00000
  camX: 0x41ab0f1b
  camY: 0x3ebe90dd
  camZ: 0x4215f34b

0x02: # Fox
  threshold: 40
  centerImageXOffset: 72
  centerImageYOffset: 164
  centerImageScaling: .87
  sideImagesXOffset: -270
  sideImagesYOffset: -40
  sideImagesScaling: .42
  reverseSides: False
  actionState: 0x15a
  frame: 0x40a00000
  camX: 0x4182015e
  camY: 0x3eb3cc4a
  camZ: 0x42152d9b

0x03: # Game and Watch
  threshold: 40
  centerImageXOffset: -266
  centerImageYOffset: 48
  centerImageScaling: .55
  sideImagesXOffset: -280
  sideImagesYOffset: -31
  sideImagesScaling: .42
  reverseSides: False
  actionState: 0x45
  frame: 0x41f00000
  camX: 0x00000000
  camY: 0xc09eee2b
  camZ: 0x423ef62b

0x19: # Ganon
  threshold: 40
  centerImageXOffset: -10
  centerImageYOffset: 167
  centerImageScaling: .90
  sideImagesXOffset: -450
  sideImagesYOffset: -89
  sideImagesScaling: .56
  reverseSides: False
  actionState: 0x108
  frame: 0x42480000
  camX: 0x41363c41
  camY: 0x41009923
  camZ: 0x4242322f

0x0E: # Ice Climbers
  threshold: 30
  centerImageXOffset: 0
  centerImageYOffset: 208
  centerImageScaling: .9
  sideImagesXOffset: -170
  sideImagesYOffset: 0
  sideImagesScaling: .3
  reverseSides: False
  grounded: True
  actionState: 0x158
  frame: 0x42880000
  camX: 0x41d68b54
  camY: 0x3e2d1618
  camZ: 0x41efe201

0x0F: # Jigglypuff
  threshold: 40
  centerImageXOffset: 47
  centerImageYOffset: 165
  centerImageScaling: .927
  sideImagesXOffset: -340
  sideImagesYOffset: -96
  sideImagesScaling: .523
  reverseSides: True
  actionState: 0x43
  frame: 0x41100000
  camX: 0x40596c6d
  camY: 0x3fd2f553
  camZ: 0x4209696b

0x04: # Kirby
  threshold: 40
  centerImageXOffset: 32
  centerImageYOffset: 204
  centerImageScaling: .48
  sideImagesXOffset: -141
  sideImagesYOffset: -24
  sideImagesScaling: .29
  reverseSides: False
  actionState: 0x41
  frame: 0x42300000
  camX: 0x3fde0e18
  camY: 0x4068dce3
  camZ: 0x41ea3184

0x06: # Link
  threshold: 0
  centerImageXOffset: 38
  centerImageYOffset: 159
  centerImageScaling: 1.17
  sideImagesXOffset: -500
  sideImagesYOffset: -122
  sideImagesScaling: .65
  reverseSides: False
  faceLeft: True
  actionState: 0x165
  frame: 0x41d00000
  camX: 0xc218dcf5
  camY: 0x40a0c470
  camZ: 0x420321bd

0x07: # Luigi
  threshold: 0
  centerImageXOffset: 64
  centerImageYOffset: 191
  centerImageScaling: .88
  sideImagesXOffset: -276
  sideImagesYOffset: -66
  sideImagesScaling: .47
  reverseSides: False
  actionState: 0x156
  frame: 0x41c00000
  camX: 0x416a6406
  camY: 0x4092702e
  camZ: 0x420f905a

0x08: # Mario
  threshold: 0
  centerImageXOffset: 148
  centerImageYOffset: 191
  centerImageScaling: .88
  sideImagesXOffset: -236
  sideImagesYOffset: -50
  sideImagesScaling: .435
  reverseSides: False
  actionState: 0x42
  frame: 0x40400000
  camX: 0x410ef8c5
  camY: 0x3fcee488
  camZ: 0x420cd983

0x09: # Marth
  threshold: 40
  centerImageXOffset: -324
  centerImageYOffset: 140
  centerImageScaling: 1.2
  sideImagesXOffset: -340
  sideImagesYOffset: -70
  sideImagesScaling: .49
  reverseSides: True
  faceLeft: True
  actionState: 0x156
  frame: 0x41500000
  camX: 0xc134fc98
  camY: 0xc05ec8b4
  camZ: 0x42250ad6

0x0A: # Mewtwo
  threshold: 40
  centerImageXOffset: 17
  centerImageYOffset: 170
  centerImageScaling: .77
  sideImagesXOffset: -160
  sideImagesYOffset: -14
  sideImagesScaling: .35
  reverseSides: True
  actionState: 0x2c
  frame: 0x41200000
  camX: 0xc1a22fd1
  camY: 0x407e0820
  camZ: 0x420e4f45

0x0B: # Ness
  threshold: 40
  centerImageXOffset: 70
  centerImageYOffset: 152
  centerImageScaling: .6
  sideImagesXOffset: -310
  sideImagesYOffset: -50
  sideImagesScaling: .44
  reverseSides: False
  actionState: 0x155
  frame: 0x40c00000
  camX: 0x41702ee6
  camY: 0xbf2987c7
  camZ: 0x41ed8c18

0x0C: # Peach
  threshold: 40
  centerImageXOffset: 30
  centerImageYOffset: 140
  centerImageScaling: .75
  sideImagesXOffset: -445
  sideImagesYOffset: -130
  sideImagesScaling: .55
  reverseSides: True
  actionState: 0x15a
  frame: 0x41900000
  camX: 0xc04b69b1
  camY: 0xc035324b
  camZ: 0x423828df

# 0x0C: # Peach (alt)
#   threshold: 40
#   centerImageXOffset: 30
#   centerImageYOffset: 140
#   centerImageScaling: .75
#   sideImagesXOffset: -310
#   sideImagesYOffset: -50
#   sideImagesScaling: .46
#   reverseSides: True
#   actionState: 0x42
#   frame: 0x42200000
#   camX: 0x42066e74
#   camY: 0xc0d8b1c4
#   camZ: 0x4187507a

0x18: # Pichu
  threshold: 40
  centerImageXOffset: -174
  centerImageYOffset: 220
  centerImageScaling: .47
  sideImagesXOffset: -291
  sideImagesYOffset: -58
  sideImagesScaling: .45
  reverseSides: False
  actionState: 0x15d
  frame: 0x40400000
  camX: 0xc20b37a2
  camY: 0xc02de0e6
  camZ: 0x402557b0

0x0D: # Pikachu
  threshold: 40
  centerImageXOffset: -38
  centerImageYOffset: 42
  centerImageScaling: .88
  sideImagesXOffset: -166
  sideImagesYOffset: -5
  sideImagesScaling: .369
  reverseSides: False
  grounded: True
  actionState: 0x108
  frame: 0x42600000
  camX: 0xc0361ee5
  camY: 0x40b85b80
  camZ: 0x41fd2549

0x17: # Roy
  threshold: 30
  centerImageXOffset: -4
  centerImageYOffset: 185
  centerImageScaling: .94
  sideImagesXOffset: -204
  sideImagesYOffset: -28
  sideImagesScaling: .42
  reverseSides: False
  grounded: True
  actionState: 0x108
  frame: 0x42340000
  camX: 0x418510f7
  camY: 0x400e6b44
  camZ: 0x41f61733

#0x10: # Samus

0x13: # Sheik
  threshold: 40
  centerImageXOffset: 13
  centerImageYOffset: 124
  centerImageScaling: 1
  sideImagesXOffset: -230
  sideImagesYOffset: -16
  sideImagesScaling: .40
  reverseSides: True
  faceLeft: True
  actionState: 0x15c
  frame: 0x40a00000
  camX: 0xc1fa8fcb
  camY: 0xc0436685
  camZ: 0x3fcd739a

0x11: # Yoshi
  threshold: 40
  centerImageXOffset: 20
  centerImageYOffset: 164
  centerImageScaling: .706
  sideImagesXOffset: -134
  sideImagesYOffset: -17
  sideImagesScaling: .35
  reverseSides: True
  actionState: 0x35
  frame: 0x41600000
  camX: 0xc04b69b1
  camY: 0x40905a28
  camZ: 0x42320264

0x15: # Young Link
  threshold: 30
  centerImageXOffset: 64
  centerImageYOffset: 160
  centerImageScaling: 1
  sideImagesXOffset: -494
  sideImagesYOffset: -164
  sideImagesScaling: .65
  reverseSides: False
  faceLeft: True
  actionState: 0x165
  frame: 0x41d00000
  camX: 0xc218dcf5
  camY: 0x40a0c470
  camZ: 0x420321bd

