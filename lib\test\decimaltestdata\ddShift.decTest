------------------------------------------------------------------------
-- ddShift.decTest -- shift decDouble coefficient left or right       --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- Sanity check
ddshi001 shift                 0    0  ->  0
ddshi002 shift                 0    2  ->  0
ddshi003 shift                 1    2  ->  100
ddshi004 shift                 1   15  ->  1000000000000000
ddshi005 shift                 1   16  ->  0
ddshi006 shift                 1   -1  ->  0
ddshi007 shift                 0   -2  ->  0
ddshi008 shift  1234567890123456   -1  ->  123456789012345
ddshi009 shift  1234567890123456   -15 ->  1
ddshi010 shift  1234567890123456   -16 ->  0
ddshi011 shift  9934567890123456   -15 ->  9
ddshi012 shift  9934567890123456   -16 ->  0

-- rhs must be an integer
ddshi015 shift        1    1.5    -> NaN Invalid_operation
ddshi016 shift        1    1.0    -> NaN Invalid_operation
ddshi017 shift        1    0.1    -> NaN Invalid_operation
ddshi018 shift        1    0.0    -> NaN Invalid_operation
ddshi019 shift        1    1E+1   -> NaN Invalid_operation
ddshi020 shift        1    1E+99  -> NaN Invalid_operation
ddshi021 shift        1    Inf    -> NaN Invalid_operation
ddshi022 shift        1    -Inf   -> NaN Invalid_operation
-- and |rhs| <= precision
ddshi025 shift        1    -1000  -> NaN Invalid_operation
ddshi026 shift        1    -17    -> NaN Invalid_operation
ddshi027 shift        1     17    -> NaN Invalid_operation
ddshi028 shift        1     1000  -> NaN Invalid_operation

-- full shifting pattern
ddshi030 shift  1234567890123456         -16  -> 0
ddshi031 shift  1234567890123456         -15  -> 1
ddshi032 shift  1234567890123456         -14  -> 12
ddshi033 shift  1234567890123456         -13  -> 123
ddshi034 shift  1234567890123456         -12  -> 1234
ddshi035 shift  1234567890123456         -11  -> 12345
ddshi036 shift  1234567890123456         -10  -> 123456
ddshi037 shift  1234567890123456         -9   -> 1234567
ddshi038 shift  1234567890123456         -8   -> 12345678
ddshi039 shift  1234567890123456         -7   -> 123456789
ddshi040 shift  1234567890123456         -6   -> 1234567890
ddshi041 shift  1234567890123456         -5   -> 12345678901
ddshi042 shift  1234567890123456         -4   -> 123456789012
ddshi043 shift  1234567890123456         -3   -> 1234567890123
ddshi044 shift  1234567890123456         -2   -> 12345678901234
ddshi045 shift  1234567890123456         -1   -> 123456789012345
ddshi046 shift  1234567890123456         -0   -> 1234567890123456

ddshi047 shift  1234567890123456         +0   -> 1234567890123456
ddshi048 shift  1234567890123456         +1   -> 2345678901234560
ddshi049 shift  1234567890123456         +2   -> 3456789012345600
ddshi050 shift  1234567890123456         +3   -> 4567890123456000
ddshi051 shift  1234567890123456         +4   -> 5678901234560000
ddshi052 shift  1234567890123456         +5   -> 6789012345600000
ddshi053 shift  1234567890123456         +6   -> 7890123456000000
ddshi054 shift  1234567890123456         +7   -> 8901234560000000
ddshi055 shift  1234567890123456         +8   -> 9012345600000000
ddshi056 shift  1234567890123456         +9   ->  123456000000000
ddshi057 shift  1234567890123456         +10  -> 1234560000000000
ddshi058 shift  1234567890123456         +11  -> 2345600000000000
ddshi059 shift  1234567890123456         +12  -> 3456000000000000
ddshi060 shift  1234567890123456         +13  -> 4560000000000000
ddshi061 shift  1234567890123456         +14  -> 5600000000000000
ddshi062 shift  1234567890123456         +15  -> 6000000000000000
ddshi063 shift  1234567890123456         +16  -> 0

-- zeros
ddshi070 shift  0E-10              +9   ->   0E-10
ddshi071 shift  0E-10              -9   ->   0E-10
ddshi072 shift  0.000              +9   ->   0.000
ddshi073 shift  0.000              -9   ->   0.000
ddshi074 shift  0E+10              +9   ->   0E+10
ddshi075 shift  0E+10              -9   ->   0E+10
ddshi076 shift -0E-10              +9   ->  -0E-10
ddshi077 shift -0E-10              -9   ->  -0E-10
ddshi078 shift -0.000              +9   ->  -0.000
ddshi079 shift -0.000              -9   ->  -0.000
ddshi080 shift -0E+10              +9   ->  -0E+10
ddshi081 shift -0E+10              -9   ->  -0E+10

-- Nmax, Nmin, Ntiny
ddshi141 shift  9.999999999999999E+384     -1  -> 9.99999999999999E+383
ddshi142 shift  9.999999999999999E+384     -15 -> 9E+369
ddshi143 shift  9.999999999999999E+384      1  -> 9.999999999999990E+384
ddshi144 shift  9.999999999999999E+384      15 -> 9.000000000000000E+384
ddshi145 shift  1E-383                     -1  -> 0E-383
ddshi146 shift  1E-383                     -15 -> 0E-383
ddshi147 shift  1E-383                      1  -> 1.0E-382
ddshi148 shift  1E-383                      15 -> 1.000000000000000E-368
ddshi151 shift  1.000000000000000E-383     -1  -> 1.00000000000000E-384
ddshi152 shift  1.000000000000000E-383     -15 -> 1E-398
ddshi153 shift  1.000000000000000E-383      1  -> 0E-398
ddshi154 shift  1.000000000000000E-383      15 -> 0E-398
ddshi155 shift  9.000000000000000E-383     -1  -> 9.00000000000000E-384
ddshi156 shift  9.000000000000000E-383     -15 -> 9E-398
ddshi157 shift  9.000000000000000E-383      1  -> 0E-398
ddshi158 shift  9.000000000000000E-383      15 -> 0E-398
ddshi160 shift  1E-398                     -1  -> 0E-398
ddshi161 shift  1E-398                     -15 -> 0E-398
ddshi162 shift  1E-398                      1  -> 1.0E-397
ddshi163 shift  1E-398                      15 -> 1.000000000000000E-383
--  negatives
ddshi171 shift -9.999999999999999E+384     -1  -> -9.99999999999999E+383
ddshi172 shift -9.999999999999999E+384     -15 -> -9E+369
ddshi173 shift -9.999999999999999E+384      1  -> -9.999999999999990E+384
ddshi174 shift -9.999999999999999E+384      15 -> -9.000000000000000E+384
ddshi175 shift -1E-383                     -1  -> -0E-383
ddshi176 shift -1E-383                     -15 -> -0E-383
ddshi177 shift -1E-383                      1  -> -1.0E-382
ddshi178 shift -1E-383                      15 -> -1.000000000000000E-368
ddshi181 shift -1.000000000000000E-383     -1  -> -1.00000000000000E-384
ddshi182 shift -1.000000000000000E-383     -15 -> -1E-398
ddshi183 shift -1.000000000000000E-383      1  -> -0E-398
ddshi184 shift -1.000000000000000E-383      15 -> -0E-398
ddshi185 shift -9.000000000000000E-383     -1  -> -9.00000000000000E-384
ddshi186 shift -9.000000000000000E-383     -15 -> -9E-398
ddshi187 shift -9.000000000000000E-383      1  -> -0E-398
ddshi188 shift -9.000000000000000E-383      15 -> -0E-398
ddshi190 shift -1E-398                     -1  -> -0E-398
ddshi191 shift -1E-398                     -15 -> -0E-398
ddshi192 shift -1E-398                      1  -> -1.0E-397
ddshi193 shift -1E-398                      15 -> -1.000000000000000E-383

-- more negatives (of sanities)
ddshi201 shift                -0    0  -> -0
ddshi202 shift                -0    2  -> -0
ddshi203 shift                -1    2  -> -100
ddshi204 shift                -1   15  -> -1000000000000000
ddshi205 shift                -1   16  -> -0
ddshi206 shift                -1   -1  -> -0
ddshi207 shift                -0   -2  -> -0
ddshi208 shift -1234567890123456   -1  -> -123456789012345
ddshi209 shift -1234567890123456   -15 -> -1
ddshi210 shift -1234567890123456   -16 -> -0
ddshi211 shift -9934567890123456   -15 -> -9
ddshi212 shift -9934567890123456   -16 -> -0


-- Specials; NaNs are handled as usual
ddshi781 shift -Inf  -8     -> -Infinity
ddshi782 shift -Inf  -1     -> -Infinity
ddshi783 shift -Inf  -0     -> -Infinity
ddshi784 shift -Inf   0     -> -Infinity
ddshi785 shift -Inf   1     -> -Infinity
ddshi786 shift -Inf   8     -> -Infinity
ddshi787 shift -1000 -Inf   -> NaN Invalid_operation
ddshi788 shift -Inf  -Inf   -> NaN Invalid_operation
ddshi789 shift -1    -Inf   -> NaN Invalid_operation
ddshi790 shift -0    -Inf   -> NaN Invalid_operation
ddshi791 shift  0    -Inf   -> NaN Invalid_operation
ddshi792 shift  1    -Inf   -> NaN Invalid_operation
ddshi793 shift  1000 -Inf   -> NaN Invalid_operation
ddshi794 shift  Inf  -Inf   -> NaN Invalid_operation

ddshi800 shift  Inf  -Inf   -> NaN Invalid_operation
ddshi801 shift  Inf  -8     -> Infinity
ddshi802 shift  Inf  -1     -> Infinity
ddshi803 shift  Inf  -0     -> Infinity
ddshi804 shift  Inf   0     -> Infinity
ddshi805 shift  Inf   1     -> Infinity
ddshi806 shift  Inf   8     -> Infinity
ddshi807 shift  Inf   Inf   -> NaN Invalid_operation
ddshi808 shift -1000  Inf   -> NaN Invalid_operation
ddshi809 shift -Inf   Inf   -> NaN Invalid_operation
ddshi810 shift -1     Inf   -> NaN Invalid_operation
ddshi811 shift -0     Inf   -> NaN Invalid_operation
ddshi812 shift  0     Inf   -> NaN Invalid_operation
ddshi813 shift  1     Inf   -> NaN Invalid_operation
ddshi814 shift  1000  Inf   -> NaN Invalid_operation
ddshi815 shift  Inf   Inf   -> NaN Invalid_operation

ddshi821 shift  NaN -Inf    ->  NaN
ddshi822 shift  NaN -1000   ->  NaN
ddshi823 shift  NaN -1      ->  NaN
ddshi824 shift  NaN -0      ->  NaN
ddshi825 shift  NaN  0      ->  NaN
ddshi826 shift  NaN  1      ->  NaN
ddshi827 shift  NaN  1000   ->  NaN
ddshi828 shift  NaN  Inf    ->  NaN
ddshi829 shift  NaN  NaN    ->  NaN
ddshi830 shift -Inf  NaN    ->  NaN
ddshi831 shift -1000 NaN    ->  NaN
ddshi832 shift -1    NaN    ->  NaN
ddshi833 shift -0    NaN    ->  NaN
ddshi834 shift  0    NaN    ->  NaN
ddshi835 shift  1    NaN    ->  NaN
ddshi836 shift  1000 NaN    ->  NaN
ddshi837 shift  Inf  NaN    ->  NaN

ddshi841 shift  sNaN -Inf   ->  NaN  Invalid_operation
ddshi842 shift  sNaN -1000  ->  NaN  Invalid_operation
ddshi843 shift  sNaN -1     ->  NaN  Invalid_operation
ddshi844 shift  sNaN -0     ->  NaN  Invalid_operation
ddshi845 shift  sNaN  0     ->  NaN  Invalid_operation
ddshi846 shift  sNaN  1     ->  NaN  Invalid_operation
ddshi847 shift  sNaN  1000  ->  NaN  Invalid_operation
ddshi848 shift  sNaN  NaN   ->  NaN  Invalid_operation
ddshi849 shift  sNaN sNaN   ->  NaN  Invalid_operation
ddshi850 shift  NaN  sNaN   ->  NaN  Invalid_operation
ddshi851 shift -Inf  sNaN   ->  NaN  Invalid_operation
ddshi852 shift -1000 sNaN   ->  NaN  Invalid_operation
ddshi853 shift -1    sNaN   ->  NaN  Invalid_operation
ddshi854 shift -0    sNaN   ->  NaN  Invalid_operation
ddshi855 shift  0    sNaN   ->  NaN  Invalid_operation
ddshi856 shift  1    sNaN   ->  NaN  Invalid_operation
ddshi857 shift  1000 sNaN   ->  NaN  Invalid_operation
ddshi858 shift  Inf  sNaN   ->  NaN  Invalid_operation
ddshi859 shift  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
ddshi861 shift  NaN1   -Inf    ->  NaN1
ddshi862 shift +NaN2   -1000   ->  NaN2
ddshi863 shift  NaN3    1000   ->  NaN3
ddshi864 shift  NaN4    Inf    ->  NaN4
ddshi865 shift  NaN5   +NaN6   ->  NaN5
ddshi866 shift -Inf     NaN7   ->  NaN7
ddshi867 shift -1000    NaN8   ->  NaN8
ddshi868 shift  1000    NaN9   ->  NaN9
ddshi869 shift  Inf    +NaN10  ->  NaN10
ddshi871 shift  sNaN11  -Inf   ->  NaN11  Invalid_operation
ddshi872 shift  sNaN12  -1000  ->  NaN12  Invalid_operation
ddshi873 shift  sNaN13   1000  ->  NaN13  Invalid_operation
ddshi874 shift  sNaN14   NaN17 ->  NaN14  Invalid_operation
ddshi875 shift  sNaN15  sNaN18 ->  NaN15  Invalid_operation
ddshi876 shift  NaN16   sNaN19 ->  NaN19  Invalid_operation
ddshi877 shift -Inf    +sNaN20 ->  NaN20  Invalid_operation
ddshi878 shift -1000    sNaN21 ->  NaN21  Invalid_operation
ddshi879 shift  1000    sNaN22 ->  NaN22  Invalid_operation
ddshi880 shift  Inf     sNaN23 ->  NaN23  Invalid_operation
ddshi881 shift +NaN25  +sNaN24 ->  NaN24  Invalid_operation
ddshi882 shift -NaN26    NaN28 -> -NaN26
ddshi883 shift -sNaN27  sNaN29 -> -NaN27  Invalid_operation
ddshi884 shift  1000    -NaN30 -> -NaN30
ddshi885 shift  1000   -sNaN31 -> -NaN31  Invalid_operation
