------------------------------------------------------------------------
-- dqCopy.decTest -- quiet decQuad copy                               --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decQuads.
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check
dqcpy001 copy       +7.50  -> 7.50

-- Infinities
dqcpy011 copy  Infinity    -> Infinity
dqcpy012 copy  -Infinity   -> -Infinity

-- NaNs, 0 payload
dqcpy021 copy         NaN  -> NaN
dqcpy022 copy        -NaN  -> -NaN
dqcpy023 copy        sNaN  -> sNaN
dqcpy024 copy       -sNaN  -> -sNaN

-- NaNs, non-0 payload
dqcpy031 copy       NaN10  -> NaN10
dqcpy032 copy      -NaN10  -> -NaN10
dqcpy033 copy      sNaN10  -> sNaN10
dqcpy034 copy     -sNaN10  -> -sNaN10
dqcpy035 copy       NaN7   -> NaN7
dqcpy036 copy      -NaN7   -> -NaN7
dqcpy037 copy      sNaN101 -> sNaN101
dqcpy038 copy     -sNaN101 -> -sNaN101

-- finites
dqcpy101 copy          7   -> 7
dqcpy102 copy         -7   -> -7
dqcpy103 copy         75   -> 75
dqcpy104 copy        -75   -> -75
dqcpy105 copy       7.50   -> 7.50
dqcpy106 copy      -7.50   -> -7.50
dqcpy107 copy       7.500  -> 7.500
dqcpy108 copy      -7.500  -> -7.500

-- zeros
dqcpy111 copy          0   -> 0
dqcpy112 copy         -0   -> -0
dqcpy113 copy       0E+4   -> 0E+4
dqcpy114 copy      -0E+4   -> -0E+4
dqcpy115 copy     0.0000   -> 0.0000
dqcpy116 copy    -0.0000   -> -0.0000
dqcpy117 copy      0E-141  -> 0E-141
dqcpy118 copy     -0E-141  -> -0E-141

-- full coefficients, alternating bits
dqcpy121 copy   2682682682682682682682682682682682    ->  2682682682682682682682682682682682
dqcpy122 copy  -2682682682682682682682682682682682    -> -2682682682682682682682682682682682
dqcpy123 copy   1341341341341341341341341341341341    ->  1341341341341341341341341341341341
dqcpy124 copy  -1341341341341341341341341341341341    -> -1341341341341341341341341341341341

-- Nmax, Nmin, Ntiny
dqcpy131 copy  9.999999999999999999999999999999999E+6144   ->  9.999999999999999999999999999999999E+6144
dqcpy132 copy  1E-6143                                     ->  1E-6143
dqcpy133 copy  1.000000000000000000000000000000000E-6143   ->  1.000000000000000000000000000000000E-6143
dqcpy134 copy  1E-6176                                     ->  1E-6176

dqcpy135 copy  -1E-6176                                    -> -1E-6176
dqcpy136 copy  -1.000000000000000000000000000000000E-6143  -> -1.000000000000000000000000000000000E-6143
dqcpy137 copy  -1E-6143                                    -> -1E-6143
dqcpy138 copy  -9.999999999999999999999999999999999E+6144  -> -9.999999999999999999999999999999999E+6144
