------------------------------------------------------------------------
-- tointegralx.decTest -- round decimal to integral value, exact      --
-- Copyright (c) IBM Corporation, 2001, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- This set of tests tests the extended specification 'round-to-integral
-- value' operation (from IEEE 854, later modified in 754r).
-- All non-zero results are defined as being those from either copy or
-- quantize, so those are assumed to have been tested.

-- This tests toIntegraExact, which may set Inexact

extended:    1
precision:   9
rounding:    half_up
maxExponent: 999
minExponent: -999

intxx001 tointegralx      0     ->  0
intxx002 tointegralx      0.0   ->  0
intxx003 tointegralx      0.1   ->  0 Inexact Rounded
intxx004 tointegralx      0.2   ->  0 Inexact Rounded
intxx005 tointegralx      0.3   ->  0 Inexact Rounded
intxx006 tointegralx      0.4   ->  0 Inexact Rounded
intxx007 tointegralx      0.5   ->  1 Inexact Rounded
intxx008 tointegralx      0.6   ->  1 Inexact Rounded
intxx009 tointegralx      0.7   ->  1 Inexact Rounded
intxx010 tointegralx      0.8   ->  1 Inexact Rounded
intxx011 tointegralx      0.9   ->  1 Inexact Rounded
intxx012 tointegralx      1     ->  1
intxx013 tointegralx      1.0   ->  1 Rounded
intxx014 tointegralx      1.1   ->  1 Inexact Rounded
intxx015 tointegralx      1.2   ->  1 Inexact Rounded
intxx016 tointegralx      1.3   ->  1 Inexact Rounded
intxx017 tointegralx      1.4   ->  1 Inexact Rounded
intxx018 tointegralx      1.5   ->  2 Inexact Rounded
intxx019 tointegralx      1.6   ->  2 Inexact Rounded
intxx020 tointegralx      1.7   ->  2 Inexact Rounded
intxx021 tointegralx      1.8   ->  2 Inexact Rounded
intxx022 tointegralx      1.9   ->  2 Inexact Rounded
-- negatives
intxx031 tointegralx     -0     -> -0
intxx032 tointegralx     -0.0   -> -0
intxx033 tointegralx     -0.1   -> -0 Inexact Rounded
intxx034 tointegralx     -0.2   -> -0 Inexact Rounded
intxx035 tointegralx     -0.3   -> -0 Inexact Rounded
intxx036 tointegralx     -0.4   -> -0 Inexact Rounded
intxx037 tointegralx     -0.5   -> -1 Inexact Rounded
intxx038 tointegralx     -0.6   -> -1 Inexact Rounded
intxx039 tointegralx     -0.7   -> -1 Inexact Rounded
intxx040 tointegralx     -0.8   -> -1 Inexact Rounded
intxx041 tointegralx     -0.9   -> -1 Inexact Rounded
intxx042 tointegralx     -1     -> -1
intxx043 tointegralx     -1.0   -> -1 Rounded
intxx044 tointegralx     -1.1   -> -1 Inexact Rounded
intxx045 tointegralx     -1.2   -> -1 Inexact Rounded
intxx046 tointegralx     -1.3   -> -1 Inexact Rounded
intxx047 tointegralx     -1.4   -> -1 Inexact Rounded
intxx048 tointegralx     -1.5   -> -2 Inexact Rounded
intxx049 tointegralx     -1.6   -> -2 Inexact Rounded
intxx050 tointegralx     -1.7   -> -2 Inexact Rounded
intxx051 tointegralx     -1.8   -> -2 Inexact Rounded
intxx052 tointegralx     -1.9   -> -2 Inexact Rounded
-- next two would be NaN using quantize(x, 0)
intxx053 tointegralx    10E+30  -> 1.0E+31
intxx054 tointegralx   -10E+30  -> -1.0E+31

-- numbers around precision
precision: 9
intxx060 tointegralx '56267E-10'   -> '0'               Inexact Rounded
intxx061 tointegralx '56267E-5'    -> '1'               Inexact Rounded
intxx062 tointegralx '56267E-2'    -> '563'             Inexact Rounded
intxx063 tointegralx '56267E-1'    -> '5627'            Inexact Rounded
intxx065 tointegralx '56267E-0'    -> '56267'
intxx066 tointegralx '56267E+0'    -> '56267'
intxx067 tointegralx '56267E+1'    -> '5.6267E+5'
intxx068 tointegralx '56267E+2'    -> '5.6267E+6'
intxx069 tointegralx '56267E+3'    -> '5.6267E+7'
intxx070 tointegralx '56267E+4'    -> '5.6267E+8'
intxx071 tointegralx '56267E+5'    -> '5.6267E+9'
intxx072 tointegralx '56267E+6'    -> '5.6267E+10'
intxx073 tointegralx '1.23E+96'    -> '1.23E+96'
intxx074 tointegralx '1.23E+384'   -> '1.23E+384'
intxx075 tointegralx '1.23E+999'   -> '1.23E+999'

intxx080 tointegralx '-56267E-10'  -> '-0'              Inexact Rounded
intxx081 tointegralx '-56267E-5'   -> '-1'              Inexact Rounded
intxx082 tointegralx '-56267E-2'   -> '-563'            Inexact Rounded
intxx083 tointegralx '-56267E-1'   -> '-5627'           Inexact Rounded
intxx085 tointegralx '-56267E-0'   -> '-56267'
intxx086 tointegralx '-56267E+0'   -> '-56267'
intxx087 tointegralx '-56267E+1'   -> '-5.6267E+5'
intxx088 tointegralx '-56267E+2'   -> '-5.6267E+6'
intxx089 tointegralx '-56267E+3'   -> '-5.6267E+7'
intxx090 tointegralx '-56267E+4'   -> '-5.6267E+8'
intxx091 tointegralx '-56267E+5'   -> '-5.6267E+9'
intxx092 tointegralx '-56267E+6'   -> '-5.6267E+10'
intxx093 tointegralx '-1.23E+96'   -> '-1.23E+96'
intxx094 tointegralx '-1.23E+384'  -> '-1.23E+384'
intxx095 tointegralx '-1.23E+999'  -> '-1.23E+999'

-- subnormal inputs
intxx100 tointegralx        1E-999 -> 0                 Inexact Rounded
intxx101 tointegralx      0.1E-999 -> 0                 Inexact Rounded
intxx102 tointegralx     0.01E-999 -> 0                 Inexact Rounded
intxx103 tointegralx        0E-999 -> 0

-- specials and zeros
intxx120 tointegralx 'Inf'       ->  Infinity
intxx121 tointegralx '-Inf'      -> -Infinity
intxx122 tointegralx   NaN       ->  NaN
intxx123 tointegralx  sNaN       ->  NaN  Invalid_operation
intxx124 tointegralx     0       ->  0
intxx125 tointegralx    -0       -> -0
intxx126 tointegralx     0.000   ->  0
intxx127 tointegralx     0.00    ->  0
intxx128 tointegralx     0.0     ->  0
intxx129 tointegralx     0       ->  0
intxx130 tointegralx     0E-3    ->  0
intxx131 tointegralx     0E-2    ->  0
intxx132 tointegralx     0E-1    ->  0
intxx133 tointegralx     0E-0    ->  0
intxx134 tointegralx     0E+1    ->  0E+1
intxx135 tointegralx     0E+2    ->  0E+2
intxx136 tointegralx     0E+3    ->  0E+3
intxx137 tointegralx     0E+4    ->  0E+4
intxx138 tointegralx     0E+5    ->  0E+5
intxx139 tointegralx    -0.000   -> -0
intxx140 tointegralx    -0.00    -> -0
intxx141 tointegralx    -0.0     -> -0
intxx142 tointegralx    -0       -> -0
intxx143 tointegralx    -0E-3    -> -0
intxx144 tointegralx    -0E-2    -> -0
intxx145 tointegralx    -0E-1    -> -0
intxx146 tointegralx    -0E-0    -> -0
intxx147 tointegralx    -0E+1    -> -0E+1
intxx148 tointegralx    -0E+2    -> -0E+2
intxx149 tointegralx    -0E+3    -> -0E+3
intxx150 tointegralx    -0E+4    -> -0E+4
intxx151 tointegralx    -0E+5    -> -0E+5
-- propagating NaNs
intxx152 tointegralx   NaN808    ->  NaN808
intxx153 tointegralx  sNaN080    ->  NaN80  Invalid_operation
intxx154 tointegralx  -NaN808    -> -NaN808
intxx155 tointegralx -sNaN080    -> -NaN80  Invalid_operation
intxx156 tointegralx  -NaN       -> -NaN
intxx157 tointegralx -sNaN       -> -NaN    Invalid_operation

-- examples
rounding:    half_up
precision:   9
intxx200 tointegralx     2.1    -> 2                    Inexact Rounded
intxx201 tointegralx   100      -> 100
intxx202 tointegralx   100.0    -> 100                  Rounded
intxx203 tointegralx   101.5    -> 102                  Inexact Rounded
intxx204 tointegralx  -101.5    -> -102                 Inexact Rounded
intxx205 tointegralx   10E+5    -> 1.0E+6
intxx206 tointegralx  7.89E+77  -> 7.89E+77
intxx207 tointegralx   -Inf     -> -Infinity


-- all rounding modes
rounding:    half_even

intxx210 tointegralx     55.5   ->  56   Inexact Rounded
intxx211 tointegralx     56.5   ->  56   Inexact Rounded
intxx212 tointegralx     57.5   ->  58   Inexact Rounded
intxx213 tointegralx    -55.5   -> -56   Inexact Rounded
intxx214 tointegralx    -56.5   -> -56   Inexact Rounded
intxx215 tointegralx    -57.5   -> -58   Inexact Rounded

rounding:    half_up

intxx220 tointegralx     55.5   ->  56   Inexact Rounded
intxx221 tointegralx     56.5   ->  57   Inexact Rounded
intxx222 tointegralx     57.5   ->  58   Inexact Rounded
intxx223 tointegralx    -55.5   -> -56   Inexact Rounded
intxx224 tointegralx    -56.5   -> -57   Inexact Rounded
intxx225 tointegralx    -57.5   -> -58   Inexact Rounded

rounding:    half_down

intxx230 tointegralx     55.5   ->  55   Inexact Rounded
intxx231 tointegralx     56.5   ->  56   Inexact Rounded
intxx232 tointegralx     57.5   ->  57   Inexact Rounded
intxx233 tointegralx    -55.5   -> -55   Inexact Rounded
intxx234 tointegralx    -56.5   -> -56   Inexact Rounded
intxx235 tointegralx    -57.5   -> -57   Inexact Rounded

rounding:    up

intxx240 tointegralx     55.3   ->  56   Inexact Rounded
intxx241 tointegralx     56.3   ->  57   Inexact Rounded
intxx242 tointegralx     57.3   ->  58   Inexact Rounded
intxx243 tointegralx    -55.3   -> -56   Inexact Rounded
intxx244 tointegralx    -56.3   -> -57   Inexact Rounded
intxx245 tointegralx    -57.3   -> -58   Inexact Rounded

rounding:    down

intxx250 tointegralx     55.7   ->  55   Inexact Rounded
intxx251 tointegralx     56.7   ->  56   Inexact Rounded
intxx252 tointegralx     57.7   ->  57   Inexact Rounded
intxx253 tointegralx    -55.7   -> -55   Inexact Rounded
intxx254 tointegralx    -56.7   -> -56   Inexact Rounded
intxx255 tointegralx    -57.7   -> -57   Inexact Rounded

rounding:    ceiling

intxx260 tointegralx     55.3   ->  56   Inexact Rounded
intxx261 tointegralx     56.3   ->  57   Inexact Rounded
intxx262 tointegralx     57.3   ->  58   Inexact Rounded
intxx263 tointegralx    -55.3   -> -55   Inexact Rounded
intxx264 tointegralx    -56.3   -> -56   Inexact Rounded
intxx265 tointegralx    -57.3   -> -57   Inexact Rounded

rounding:    floor

intxx270 tointegralx     55.7   ->  55   Inexact Rounded
intxx271 tointegralx     56.7   ->  56   Inexact Rounded
intxx272 tointegralx     57.7   ->  57   Inexact Rounded
intxx273 tointegralx    -55.7   -> -56   Inexact Rounded
intxx274 tointegralx    -56.7   -> -57   Inexact Rounded
intxx275 tointegralx    -57.7   -> -58   Inexact Rounded

-- Int and uInt32 edge values for testing conversions
precision: 16
intxx300 tointegralx -2147483646  -> -2147483646
intxx301 tointegralx -2147483647  -> -2147483647
intxx302 tointegralx -2147483648  -> -2147483648
intxx303 tointegralx -2147483649  -> -2147483649
intxx304 tointegralx  2147483646  ->  2147483646
intxx305 tointegralx  2147483647  ->  2147483647
intxx306 tointegralx  2147483648  ->  2147483648
intxx307 tointegralx  2147483649  ->  2147483649
intxx308 tointegralx  4294967294  ->  4294967294
intxx309 tointegralx  4294967295  ->  4294967295
intxx310 tointegralx  4294967296  ->  4294967296
intxx311 tointegralx  4294967297  ->  4294967297
