------------------------------------------------------------------------
-- nextplus.decTest -- decimal next that is greater [754r nextup]     --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 384
minexponent: -383

nextp001 nextplus  0.999999995 ->   0.999999996
nextp002 nextplus  0.999999996 ->   0.999999997
nextp003 nextplus  0.999999997 ->   0.999999998
nextp004 nextplus  0.999999998 ->   0.999999999
nextp005 nextplus  0.999999999 ->   1.00000000
nextp006 nextplus  1.00000000  ->   1.00000001
nextp007 nextplus  1.0         ->   1.00000001
nextp008 nextplus  1           ->   1.00000001
nextp009 nextplus  1.00000001  ->   1.00000002
nextp010 nextplus  1.00000002  ->   1.00000003
nextp011 nextplus  1.00000003  ->   1.00000004
nextp012 nextplus  1.00000004  ->   1.00000005
nextp013 nextplus  1.00000005  ->   1.00000006
nextp014 nextplus  1.00000006  ->   1.00000007
nextp015 nextplus  1.00000007  ->   1.00000008
nextp016 nextplus  1.00000008  ->   1.00000009
nextp017 nextplus  1.00000009  ->   1.00000010
nextp018 nextplus  1.00000010  ->   1.00000011
nextp019 nextplus  1.00000011  ->   1.00000012

nextp021 nextplus -0.999999995 ->  -0.999999994
nextp022 nextplus -0.999999996 ->  -0.999999995
nextp023 nextplus -0.999999997 ->  -0.999999996
nextp024 nextplus -0.999999998 ->  -0.999999997
nextp025 nextplus -0.999999999 ->  -0.999999998
nextp026 nextplus -1.00000000  ->  -0.999999999
nextp027 nextplus -1.0         ->  -0.999999999
nextp028 nextplus -1           ->  -0.999999999
nextp029 nextplus -1.00000001  ->  -1.00000000
nextp030 nextplus -1.00000002  ->  -1.00000001
nextp031 nextplus -1.00000003  ->  -1.00000002
nextp032 nextplus -1.00000004  ->  -1.00000003
nextp033 nextplus -1.00000005  ->  -1.00000004
nextp034 nextplus -1.00000006  ->  -1.00000005
nextp035 nextplus -1.00000007  ->  -1.00000006
nextp036 nextplus -1.00000008  ->  -1.00000007
nextp037 nextplus -1.00000009  ->  -1.00000008
nextp038 nextplus -1.00000010  ->  -1.00000009
nextp039 nextplus -1.00000011  ->  -1.00000010
nextp040 nextplus -1.00000012  ->  -1.00000011

-- input operand is >precision
nextp041 nextplus  1.00000010998  ->   1.00000011
nextp042 nextplus  1.00000010999  ->   1.00000011
nextp043 nextplus  1.00000011000  ->   1.00000012
nextp044 nextplus  1.00000011001  ->   1.00000012
nextp045 nextplus  1.00000011002  ->   1.00000012
nextp046 nextplus  1.00000011002  ->   1.00000012
nextp047 nextplus  1.00000011052  ->   1.00000012
nextp048 nextplus  1.00000011552  ->   1.00000012
nextp049 nextplus -1.00000010998  ->  -1.00000010
nextp050 nextplus -1.00000010999  ->  -1.00000010
nextp051 nextplus -1.00000011000  ->  -1.00000010
nextp052 nextplus -1.00000011001  ->  -1.00000011
nextp053 nextplus -1.00000011002  ->  -1.00000011
nextp054 nextplus -1.00000011002  ->  -1.00000011
nextp055 nextplus -1.00000011052  ->  -1.00000011
nextp056 nextplus -1.00000011552  ->  -1.00000011
-- ultra-tiny inputs
nextp060 nextplus  1E-99999       ->   1E-391
nextp061 nextplus  1E-999999999   ->   1E-391
nextp062 nextplus  1E-391         ->   2E-391
nextp063 nextplus -1E-99999       ->  -0E-391
nextp064 nextplus -1E-999999999   ->  -0E-391
nextp065 nextplus -1E-391         ->  -0E-391

-- Zeros
nextp100 nextplus  0           ->  1E-391
nextp101 nextplus  0.00        ->  1E-391
nextp102 nextplus  0E-300      ->  1E-391
nextp103 nextplus  0E+300      ->  1E-391
nextp104 nextplus  0E+30000    ->  1E-391
nextp105 nextplus -0           ->  1E-391
nextp106 nextplus -0.00        ->  1E-391
nextp107 nextplus -0E-300      ->  1E-391
nextp108 nextplus -0E+300      ->  1E-391
nextp109 nextplus -0E+30000    ->  1E-391

maxExponent: 999
minexponent: -999
precision: 9
-- specials
nextp150 nextplus   Inf    ->  Infinity
nextp151 nextplus  -Inf    -> -9.99999999E+999
nextp152 nextplus   NaN    ->  NaN
nextp153 nextplus  sNaN    ->  NaN   Invalid_operation
nextp154 nextplus   NaN77  ->  NaN77
nextp155 nextplus  sNaN88  ->  NaN88 Invalid_operation
nextp156 nextplus  -NaN    -> -NaN
nextp157 nextplus -sNaN    -> -NaN   Invalid_operation
nextp158 nextplus  -NaN77  -> -NaN77
nextp159 nextplus -sNaN88  -> -NaN88 Invalid_operation

-- Nmax, Nmin, Ntiny, subnormals
nextp170 nextplus  9.99999999E+999   -> Infinity
nextp171 nextplus  9.99999998E+999   -> 9.99999999E+999
nextp172 nextplus  1E-999            -> 1.00000001E-999
nextp173 nextplus  1.00000000E-999   -> 1.00000001E-999
nextp174 nextplus  9E-1007           -> 1.0E-1006
nextp175 nextplus  9.9E-1006         -> 1.00E-1005
nextp176 nextplus  9.9999E-1003      -> 1.00000E-1002
nextp177 nextplus  9.9999999E-1000   -> 1.00000000E-999
nextp178 nextplus  9.9999998E-1000   -> 9.9999999E-1000
nextp179 nextplus  9.9999997E-1000   -> 9.9999998E-1000
nextp180 nextplus  0E-1007           -> 1E-1007
nextp181 nextplus  1E-1007           -> 2E-1007
nextp182 nextplus  2E-1007           -> 3E-1007

nextp183 nextplus  -0E-1007          ->  1E-1007
nextp184 nextplus  -1E-1007          -> -0E-1007
nextp185 nextplus  -2E-1007          -> -1E-1007
nextp186 nextplus  -10E-1007         -> -9E-1007
nextp187 nextplus  -100E-1007        -> -9.9E-1006
nextp188 nextplus  -100000E-1007     -> -9.9999E-1003
nextp189 nextplus  -1.0000E-999      -> -9.9999999E-1000
nextp190 nextplus  -1.00000000E-999  -> -9.9999999E-1000
nextp191 nextplus  -1E-999           -> -9.9999999E-1000
nextp192 nextplus  -9.99999998E+999  -> -9.99999997E+999
nextp193 nextplus  -9.99999999E+999  -> -9.99999998E+999

-- Null tests
nextp900 nextplus  # -> NaN Invalid_operation

