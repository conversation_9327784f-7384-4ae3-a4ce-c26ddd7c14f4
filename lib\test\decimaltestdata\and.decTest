------------------------------------------------------------------------
-- and.decTest -- digitwise logical AND                               --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 999
minExponent: -999

-- Sanity check (truth table)
andx001 and             0    0 ->    0
andx002 and             0    1 ->    0
andx003 and             1    0 ->    0
andx004 and             1    1 ->    1
andx005 and          1100 1010 -> 1000
andx006 and          1111   10 ->   10
andx007 and          1111 1010 -> 1010

-- and at msd and msd-1
andx010 and 000000000 000000000 ->           0
andx011 and 000000000 100000000 ->           0
andx012 and 100000000 000000000 ->           0
andx013 and 100000000 100000000 ->   100000000
andx014 and 000000000 000000000 ->           0
andx015 and 000000000 010000000 ->           0
andx016 and 010000000 000000000 ->           0
andx017 and 010000000 010000000 ->    10000000

-- Various lengths
--          123456789     123456789      123456789
andx021 and 111111111     111111111  ->  111111111
andx022 and 111111111111  111111111  ->  111111111
andx023 and 111111111111   11111111  ->   11111111
andx024 and 111111111      11111111  ->   11111111
andx025 and 111111111       1111111  ->    1111111
andx026 and 111111111111     111111  ->     111111
andx027 and 111111111111      11111  ->      11111
andx028 and 111111111111       1111  ->       1111
andx029 and 111111111111        111  ->        111
andx031 and 111111111111         11  ->         11
andx032 and 111111111111          1  ->          1
andx033 and 111111111111 1111111111  ->  111111111
andx034 and 11111111111 11111111111  ->  111111111
andx035 and 1111111111 111111111111  ->  111111111
andx036 and 111111111 1111111111111  ->  111111111

andx040 and 111111111  111111111111  ->  111111111
andx041 and  11111111  111111111111  ->   11111111
andx042 and  11111111     111111111  ->   11111111
andx043 and   1111111     111111111  ->    1111111
andx044 and    111111     111111111  ->     111111
andx045 and     11111     111111111  ->      11111
andx046 and      1111     111111111  ->       1111
andx047 and       111     111111111  ->        111
andx048 and        11     111111111  ->         11
andx049 and         1     111111111  ->          1

andx050 and 1111111111  1  ->  1
andx051 and  111111111  1  ->  1
andx052 and   11111111  1  ->  1
andx053 and    1111111  1  ->  1
andx054 and     111111  1  ->  1
andx055 and      11111  1  ->  1
andx056 and       1111  1  ->  1
andx057 and        111  1  ->  1
andx058 and         11  1  ->  1
andx059 and          1  1  ->  1

andx060 and 1111111111  0  ->  0
andx061 and  111111111  0  ->  0
andx062 and   11111111  0  ->  0
andx063 and    1111111  0  ->  0
andx064 and     111111  0  ->  0
andx065 and      11111  0  ->  0
andx066 and       1111  0  ->  0
andx067 and        111  0  ->  0
andx068 and         11  0  ->  0
andx069 and          1  0  ->  0

andx070 and 1  1111111111  ->  1
andx071 and 1   111111111  ->  1
andx072 and 1    11111111  ->  1
andx073 and 1     1111111  ->  1
andx074 and 1      111111  ->  1
andx075 and 1       11111  ->  1
andx076 and 1        1111  ->  1
andx077 and 1         111  ->  1
andx078 and 1          11  ->  1
andx079 and 1           1  ->  1

andx080 and 0  1111111111  ->  0
andx081 and 0   111111111  ->  0
andx082 and 0    11111111  ->  0
andx083 and 0     1111111  ->  0
andx084 and 0      111111  ->  0
andx085 and 0       11111  ->  0
andx086 and 0        1111  ->  0
andx087 and 0         111  ->  0
andx088 and 0          11  ->  0
andx089 and 0           1  ->  0

andx090 and 011111111  111111111  ->   11111111
andx091 and 101111111  111111111  ->  101111111
andx092 and 110111111  111111111  ->  110111111
andx093 and 111011111  111111111  ->  111011111
andx094 and 111101111  111111111  ->  111101111
andx095 and 111110111  111111111  ->  111110111
andx096 and 111111011  111111111  ->  111111011
andx097 and 111111101  111111111  ->  111111101
andx098 and 111111110  111111111  ->  111111110

andx100 and 111111111  011111111  ->   11111111
andx101 and 111111111  101111111  ->  101111111
andx102 and 111111111  110111111  ->  110111111
andx103 and 111111111  111011111  ->  111011111
andx104 and 111111111  111101111  ->  111101111
andx105 and 111111111  111110111  ->  111110111
andx106 and 111111111  111111011  ->  111111011
andx107 and 111111111  111111101  ->  111111101
andx108 and 111111111  111111110  ->  111111110

-- non-0/1 should not be accepted, nor should signs
andx220 and 111111112  111111111  ->  NaN Invalid_operation
andx221 and 333333333  333333333  ->  NaN Invalid_operation
andx222 and 555555555  555555555  ->  NaN Invalid_operation
andx223 and 777777777  777777777  ->  NaN Invalid_operation
andx224 and 999999999  999999999  ->  NaN Invalid_operation
andx225 and 222222222  999999999  ->  NaN Invalid_operation
andx226 and 444444444  999999999  ->  NaN Invalid_operation
andx227 and 666666666  999999999  ->  NaN Invalid_operation
andx228 and 888888888  999999999  ->  NaN Invalid_operation
andx229 and 999999999  222222222  ->  NaN Invalid_operation
andx230 and 999999999  444444444  ->  NaN Invalid_operation
andx231 and 999999999  666666666  ->  NaN Invalid_operation
andx232 and 999999999  888888888  ->  NaN Invalid_operation
-- a few randoms
andx240 and  567468689 -934981942 ->  NaN Invalid_operation
andx241 and  567367689  934981942 ->  NaN Invalid_operation
andx242 and -631917772 -706014634 ->  NaN Invalid_operation
andx243 and -756253257  138579234 ->  NaN Invalid_operation
andx244 and  835590149  567435400 ->  NaN Invalid_operation
-- test MSD
andx250 and  200000000 100000000 ->  NaN Invalid_operation
andx251 and  700000000 100000000 ->  NaN Invalid_operation
andx252 and  800000000 100000000 ->  NaN Invalid_operation
andx253 and  900000000 100000000 ->  NaN Invalid_operation
andx254 and  200000000 000000000 ->  NaN Invalid_operation
andx255 and  700000000 000000000 ->  NaN Invalid_operation
andx256 and  800000000 000000000 ->  NaN Invalid_operation
andx257 and  900000000 000000000 ->  NaN Invalid_operation
andx258 and  100000000 200000000 ->  NaN Invalid_operation
andx259 and  100000000 700000000 ->  NaN Invalid_operation
andx260 and  100000000 800000000 ->  NaN Invalid_operation
andx261 and  100000000 900000000 ->  NaN Invalid_operation
andx262 and  000000000 200000000 ->  NaN Invalid_operation
andx263 and  000000000 700000000 ->  NaN Invalid_operation
andx264 and  000000000 800000000 ->  NaN Invalid_operation
andx265 and  000000000 900000000 ->  NaN Invalid_operation
-- test MSD-1
andx270 and  020000000 100000000 ->  NaN Invalid_operation
andx271 and  070100000 100000000 ->  NaN Invalid_operation
andx272 and  080010000 100000001 ->  NaN Invalid_operation
andx273 and  090001000 100000010 ->  NaN Invalid_operation
andx274 and  100000100 020010100 ->  NaN Invalid_operation
andx275 and  100000000 070001000 ->  NaN Invalid_operation
andx276 and  100000010 080010100 ->  NaN Invalid_operation
andx277 and  100000000 090000010 ->  NaN Invalid_operation
-- test LSD
andx280 and  001000002 100000000 ->  NaN Invalid_operation
andx281 and  000000007 100000000 ->  NaN Invalid_operation
andx282 and  000000008 100000000 ->  NaN Invalid_operation
andx283 and  000000009 100000000 ->  NaN Invalid_operation
andx284 and  100000000 000100002 ->  NaN Invalid_operation
andx285 and  100100000 001000007 ->  NaN Invalid_operation
andx286 and  100010000 010000008 ->  NaN Invalid_operation
andx287 and  100001000 100000009 ->  NaN Invalid_operation
-- test Middie
andx288 and  001020000 100000000 ->  NaN Invalid_operation
andx289 and  000070001 100000000 ->  NaN Invalid_operation
andx290 and  000080000 100010000 ->  NaN Invalid_operation
andx291 and  000090000 100001000 ->  NaN Invalid_operation
andx292 and  100000010 000020100 ->  NaN Invalid_operation
andx293 and  100100000 000070010 ->  NaN Invalid_operation
andx294 and  100010100 000080001 ->  NaN Invalid_operation
andx295 and  100001000 000090000 ->  NaN Invalid_operation
-- signs
andx296 and -100001000 -000000000 ->  NaN Invalid_operation
andx297 and -100001000  000010000 ->  NaN Invalid_operation
andx298 and  100001000 -000000000 ->  NaN Invalid_operation
andx299 and  100001000  000011000 ->  1000

-- Nmax, Nmin, Ntiny
andx331 and  2   9.99999999E+999     -> NaN Invalid_operation
andx332 and  3   1E-999              -> NaN Invalid_operation
andx333 and  4   1.00000000E-999     -> NaN Invalid_operation
andx334 and  5   1E-1007             -> NaN Invalid_operation
andx335 and  6   -1E-1007            -> NaN Invalid_operation
andx336 and  7   -1.00000000E-999    -> NaN Invalid_operation
andx337 and  8   -1E-999             -> NaN Invalid_operation
andx338 and  9   -9.99999999E+999    -> NaN Invalid_operation
andx341 and  9.99999999E+999     -18 -> NaN Invalid_operation
andx342 and  1E-999               01 -> NaN Invalid_operation
andx343 and  1.00000000E-999     -18 -> NaN Invalid_operation
andx344 and  1E-1007              18 -> NaN Invalid_operation
andx345 and  -1E-1007            -10 -> NaN Invalid_operation
andx346 and  -1.00000000E-999     18 -> NaN Invalid_operation
andx347 and  -1E-999              10 -> NaN Invalid_operation
andx348 and  -9.99999999E+999    -18 -> NaN Invalid_operation

-- A few other non-integers
andx361 and  1.0                  1  -> NaN Invalid_operation
andx362 and  1E+1                 1  -> NaN Invalid_operation
andx363 and  0.0                  1  -> NaN Invalid_operation
andx364 and  0E+1                 1  -> NaN Invalid_operation
andx365 and  9.9                  1  -> NaN Invalid_operation
andx366 and  9E+1                 1  -> NaN Invalid_operation
andx371 and  0 1.0                   -> NaN Invalid_operation
andx372 and  0 1E+1                  -> NaN Invalid_operation
andx373 and  0 0.0                   -> NaN Invalid_operation
andx374 and  0 0E+1                  -> NaN Invalid_operation
andx375 and  0 9.9                   -> NaN Invalid_operation
andx376 and  0 9E+1                  -> NaN Invalid_operation

-- All Specials are in error
andx780 and -Inf  -Inf   -> NaN Invalid_operation
andx781 and -Inf  -1000  -> NaN Invalid_operation
andx782 and -Inf  -1     -> NaN Invalid_operation
andx783 and -Inf  -0     -> NaN Invalid_operation
andx784 and -Inf   0     -> NaN Invalid_operation
andx785 and -Inf   1     -> NaN Invalid_operation
andx786 and -Inf   1000  -> NaN Invalid_operation
andx787 and -1000 -Inf   -> NaN Invalid_operation
andx788 and -Inf  -Inf   -> NaN Invalid_operation
andx789 and -1    -Inf   -> NaN Invalid_operation
andx790 and -0    -Inf   -> NaN Invalid_operation
andx791 and  0    -Inf   -> NaN Invalid_operation
andx792 and  1    -Inf   -> NaN Invalid_operation
andx793 and  1000 -Inf   -> NaN Invalid_operation
andx794 and  Inf  -Inf   -> NaN Invalid_operation

andx800 and  Inf  -Inf   -> NaN Invalid_operation
andx801 and  Inf  -1000  -> NaN Invalid_operation
andx802 and  Inf  -1     -> NaN Invalid_operation
andx803 and  Inf  -0     -> NaN Invalid_operation
andx804 and  Inf   0     -> NaN Invalid_operation
andx805 and  Inf   1     -> NaN Invalid_operation
andx806 and  Inf   1000  -> NaN Invalid_operation
andx807 and  Inf   Inf   -> NaN Invalid_operation
andx808 and -1000  Inf   -> NaN Invalid_operation
andx809 and -Inf   Inf   -> NaN Invalid_operation
andx810 and -1     Inf   -> NaN Invalid_operation
andx811 and -0     Inf   -> NaN Invalid_operation
andx812 and  0     Inf   -> NaN Invalid_operation
andx813 and  1     Inf   -> NaN Invalid_operation
andx814 and  1000  Inf   -> NaN Invalid_operation
andx815 and  Inf   Inf   -> NaN Invalid_operation

andx821 and  NaN -Inf    -> NaN Invalid_operation
andx822 and  NaN -1000   -> NaN Invalid_operation
andx823 and  NaN -1      -> NaN Invalid_operation
andx824 and  NaN -0      -> NaN Invalid_operation
andx825 and  NaN  0      -> NaN Invalid_operation
andx826 and  NaN  1      -> NaN Invalid_operation
andx827 and  NaN  1000   -> NaN Invalid_operation
andx828 and  NaN  Inf    -> NaN Invalid_operation
andx829 and  NaN  NaN    -> NaN Invalid_operation
andx830 and -Inf  NaN    -> NaN Invalid_operation
andx831 and -1000 NaN    -> NaN Invalid_operation
andx832 and -1    NaN    -> NaN Invalid_operation
andx833 and -0    NaN    -> NaN Invalid_operation
andx834 and  0    NaN    -> NaN Invalid_operation
andx835 and  1    NaN    -> NaN Invalid_operation
andx836 and  1000 NaN    -> NaN Invalid_operation
andx837 and  Inf  NaN    -> NaN Invalid_operation

andx841 and  sNaN -Inf   ->  NaN  Invalid_operation
andx842 and  sNaN -1000  ->  NaN  Invalid_operation
andx843 and  sNaN -1     ->  NaN  Invalid_operation
andx844 and  sNaN -0     ->  NaN  Invalid_operation
andx845 and  sNaN  0     ->  NaN  Invalid_operation
andx846 and  sNaN  1     ->  NaN  Invalid_operation
andx847 and  sNaN  1000  ->  NaN  Invalid_operation
andx848 and  sNaN  NaN   ->  NaN  Invalid_operation
andx849 and  sNaN sNaN   ->  NaN  Invalid_operation
andx850 and  NaN  sNaN   ->  NaN  Invalid_operation
andx851 and -Inf  sNaN   ->  NaN  Invalid_operation
andx852 and -1000 sNaN   ->  NaN  Invalid_operation
andx853 and -1    sNaN   ->  NaN  Invalid_operation
andx854 and -0    sNaN   ->  NaN  Invalid_operation
andx855 and  0    sNaN   ->  NaN  Invalid_operation
andx856 and  1    sNaN   ->  NaN  Invalid_operation
andx857 and  1000 sNaN   ->  NaN  Invalid_operation
andx858 and  Inf  sNaN   ->  NaN  Invalid_operation
andx859 and  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
andx861 and  NaN1   -Inf    -> NaN Invalid_operation
andx862 and +NaN2   -1000   -> NaN Invalid_operation
andx863 and  NaN3    1000   -> NaN Invalid_operation
andx864 and  NaN4    Inf    -> NaN Invalid_operation
andx865 and  NaN5   +NaN6   -> NaN Invalid_operation
andx866 and -Inf     NaN7   -> NaN Invalid_operation
andx867 and -1000    NaN8   -> NaN Invalid_operation
andx868 and  1000    NaN9   -> NaN Invalid_operation
andx869 and  Inf    +NaN10  -> NaN Invalid_operation
andx871 and  sNaN11  -Inf   -> NaN Invalid_operation
andx872 and  sNaN12  -1000  -> NaN Invalid_operation
andx873 and  sNaN13   1000  -> NaN Invalid_operation
andx874 and  sNaN14   NaN17 -> NaN Invalid_operation
andx875 and  sNaN15  sNaN18 -> NaN Invalid_operation
andx876 and  NaN16   sNaN19 -> NaN Invalid_operation
andx877 and -Inf    +sNaN20 -> NaN Invalid_operation
andx878 and -1000    sNaN21 -> NaN Invalid_operation
andx879 and  1000    sNaN22 -> NaN Invalid_operation
andx880 and  Inf     sNaN23 -> NaN Invalid_operation
andx881 and +NaN25  +sNaN24 -> NaN Invalid_operation
andx882 and -NaN26    NaN28 -> NaN Invalid_operation
andx883 and -sNaN27  sNaN29 -> NaN Invalid_operation
andx884 and  1000    -NaN30 -> NaN Invalid_operation
andx885 and  1000   -sNaN31 -> NaN Invalid_operation
