------------------------------------------------------------------------
-- ddMinMag.decTest -- decDouble minnummag                            --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- we assume that base comparison is tested in compare.decTest, so
-- these mainly cover special cases and rounding
precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- sanity checks
ddmng001 minmag  -2  -2  -> -2
ddmng002 minmag  -2  -1  -> -1
ddmng003 minmag  -2   0  ->  0
ddmng004 minmag  -2   1  ->  1
ddmng005 minmag  -2   2  -> -2
ddmng006 minmag  -1  -2  -> -1
ddmng007 minmag  -1  -1  -> -1
ddmng008 minmag  -1   0  ->  0
ddmng009 minmag  -1   1  -> -1
ddmng010 minmag  -1   2  -> -1
ddmng011 minmag   0  -2  ->  0
ddmng012 minmag   0  -1  ->  0
ddmng013 minmag   0   0  ->  0
ddmng014 minmag   0   1  ->  0
ddmng015 minmag   0   2  ->  0
ddmng016 minmag   1  -2  ->  1
ddmng017 minmag   1  -1  -> -1
ddmng018 minmag   1   0  ->  0
ddmng019 minmag   1   1  ->  1
ddmng020 minmag   1   2  ->  1
ddmng021 minmag   2  -2  -> -2
ddmng022 minmag   2  -1  -> -1
ddmng023 minmag   2   0  ->  0
ddmng025 minmag   2   1  ->  1
ddmng026 minmag   2   2  ->  2

-- extended zeros
ddmng030 minmag   0     0   ->  0
ddmng031 minmag   0    -0   -> -0
ddmng032 minmag   0    -0.0 -> -0.0
ddmng033 minmag   0     0.0 ->  0.0
ddmng034 minmag  -0     0   -> -0
ddmng035 minmag  -0    -0   -> -0
ddmng036 minmag  -0    -0.0 -> -0
ddmng037 minmag  -0     0.0 -> -0
ddmng038 minmag   0.0   0   ->  0.0
ddmng039 minmag   0.0  -0   -> -0
ddmng040 minmag   0.0  -0.0 -> -0.0
ddmng041 minmag   0.0   0.0 ->  0.0
ddmng042 minmag  -0.0   0   -> -0.0
ddmng043 minmag  -0.0  -0   -> -0
ddmng044 minmag  -0.0  -0.0 -> -0.0
ddmng045 minmag  -0.0   0.0 -> -0.0

ddmng046 minmag   0E1  -0E1 -> -0E+1
ddmng047 minmag  -0E1   0E2 -> -0E+1
ddmng048 minmag   0E2   0E1 ->  0E+1
ddmng049 minmag   0E1   0E2 ->  0E+1
ddmng050 minmag  -0E3  -0E2 -> -0E+3
ddmng051 minmag  -0E2  -0E3 -> -0E+3

-- Specials
ddmng090 minmag  Inf  -Inf   -> -Infinity
ddmng091 minmag  Inf  -1000  -> -1000
ddmng092 minmag  Inf  -1     -> -1
ddmng093 minmag  Inf  -0     -> -0
ddmng094 minmag  Inf   0     ->  0
ddmng095 minmag  Inf   1     ->  1
ddmng096 minmag  Inf   1000  ->  1000
ddmng097 minmag  Inf   Inf   ->  Infinity
ddmng098 minmag -1000  Inf   -> -1000
ddmng099 minmag -Inf   Inf   -> -Infinity
ddmng100 minmag -1     Inf   -> -1
ddmng101 minmag -0     Inf   -> -0
ddmng102 minmag  0     Inf   ->  0
ddmng103 minmag  1     Inf   ->  1
ddmng104 minmag  1000  Inf   ->  1000
ddmng105 minmag  Inf   Inf   ->  Infinity

ddmng120 minmag -Inf  -Inf   -> -Infinity
ddmng121 minmag -Inf  -1000  -> -1000
ddmng122 minmag -Inf  -1     -> -1
ddmng123 minmag -Inf  -0     -> -0
ddmng124 minmag -Inf   0     ->  0
ddmng125 minmag -Inf   1     ->  1
ddmng126 minmag -Inf   1000  ->  1000
ddmng127 minmag -Inf   Inf   -> -Infinity
ddmng128 minmag -Inf  -Inf   -> -Infinity
ddmng129 minmag -1000 -Inf   -> -1000
ddmng130 minmag -1    -Inf   -> -1
ddmng131 minmag -0    -Inf   -> -0
ddmng132 minmag  0    -Inf   ->  0
ddmng133 minmag  1    -Inf   ->  1
ddmng134 minmag  1000 -Inf   ->  1000
ddmng135 minmag  Inf  -Inf   -> -Infinity

-- 2004.08.02 754r chooses number over NaN in mixed cases
ddmng141 minmag  NaN -Inf    ->  -Infinity
ddmng142 minmag  NaN -1000   ->  -1000
ddmng143 minmag  NaN -1      ->  -1
ddmng144 minmag  NaN -0      ->  -0
ddmng145 minmag  NaN  0      ->  0
ddmng146 minmag  NaN  1      ->  1
ddmng147 minmag  NaN  1000   ->  1000
ddmng148 minmag  NaN  Inf    ->  Infinity
ddmng149 minmag  NaN  NaN    ->  NaN
ddmng150 minmag -Inf  NaN    -> -Infinity
ddmng151 minmag -1000 NaN    -> -1000
ddmng152 minmag -1   -NaN    -> -1
ddmng153 minmag -0    NaN    -> -0
ddmng154 minmag  0   -NaN    ->  0
ddmng155 minmag  1    NaN    ->  1
ddmng156 minmag  1000 NaN    ->  1000
ddmng157 minmag  Inf  NaN    ->  Infinity

ddmng161 minmag  sNaN -Inf   ->  NaN  Invalid_operation
ddmng162 minmag  sNaN -1000  ->  NaN  Invalid_operation
ddmng163 minmag  sNaN -1     ->  NaN  Invalid_operation
ddmng164 minmag  sNaN -0     ->  NaN  Invalid_operation
ddmng165 minmag -sNaN  0     -> -NaN  Invalid_operation
ddmng166 minmag -sNaN  1     -> -NaN  Invalid_operation
ddmng167 minmag  sNaN  1000  ->  NaN  Invalid_operation
ddmng168 minmag  sNaN  NaN   ->  NaN  Invalid_operation
ddmng169 minmag  sNaN sNaN   ->  NaN  Invalid_operation
ddmng170 minmag  NaN  sNaN   ->  NaN  Invalid_operation
ddmng171 minmag -Inf  sNaN   ->  NaN  Invalid_operation
ddmng172 minmag -1000 sNaN   ->  NaN  Invalid_operation
ddmng173 minmag -1    sNaN   ->  NaN  Invalid_operation
ddmng174 minmag -0    sNaN   ->  NaN  Invalid_operation
ddmng175 minmag  0    sNaN   ->  NaN  Invalid_operation
ddmng176 minmag  1    sNaN   ->  NaN  Invalid_operation
ddmng177 minmag  1000 sNaN   ->  NaN  Invalid_operation
ddmng178 minmag  Inf  sNaN   ->  NaN  Invalid_operation
ddmng179 minmag  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
ddmng181 minmag  NaN9   -Inf   -> -Infinity
ddmng182 minmag -NaN8    9990  ->  9990
ddmng183 minmag  NaN71   Inf   ->  Infinity

ddmng184 minmag  NaN1    NaN54 ->  NaN1
ddmng185 minmag  NaN22  -NaN53 ->  NaN22
ddmng186 minmag -NaN3    NaN6  -> -NaN3
ddmng187 minmag -NaN44   NaN7  -> -NaN44

ddmng188 minmag -Inf     NaN41 -> -Infinity
ddmng189 minmag -9999   -NaN33 -> -9999
ddmng190 minmag  Inf     NaN2  ->  Infinity

ddmng191 minmag  sNaN99 -Inf    ->  NaN99 Invalid_operation
ddmng192 minmag  sNaN98 -11     ->  NaN98 Invalid_operation
ddmng193 minmag -sNaN97  NaN8   -> -NaN97 Invalid_operation
ddmng194 minmag  sNaN69 sNaN94  ->  NaN69 Invalid_operation
ddmng195 minmag  NaN95  sNaN93  ->  NaN93 Invalid_operation
ddmng196 minmag -Inf    sNaN92  ->  NaN92 Invalid_operation
ddmng197 minmag  088    sNaN91  ->  NaN91 Invalid_operation
ddmng198 minmag  Inf   -sNaN90  -> -NaN90 Invalid_operation
ddmng199 minmag  NaN    sNaN86  ->  NaN86 Invalid_operation

-- old rounding checks
ddmng221 minmag -12345678000 1  -> 1
ddmng222 minmag 1 -12345678000  -> 1
ddmng223 minmag -1234567800  1  -> 1
ddmng224 minmag 1 -1234567800   -> 1
ddmng225 minmag -1234567890  1  -> 1
ddmng226 minmag 1 -1234567890   -> 1
ddmng227 minmag -1234567891  1  -> 1
ddmng228 minmag 1 -1234567891   -> 1
ddmng229 minmag -12345678901 1  -> 1
ddmng230 minmag 1 -12345678901  -> 1
ddmng231 minmag -1234567896  1  -> 1
ddmng232 minmag 1 -1234567896   -> 1
ddmng233 minmag 1234567891  1   -> 1
ddmng234 minmag 1 1234567891    -> 1
ddmng235 minmag 12345678901 1   -> 1
ddmng236 minmag 1 12345678901   -> 1
ddmng237 minmag 1234567896  1   -> 1
ddmng238 minmag 1 1234567896    -> 1

-- from examples
ddmng280 minmag '3'   '2'  ->  '2'
ddmng281 minmag '-10' '3'  ->  '3'
ddmng282 minmag '1.0' '1'  ->  '1.0'
ddmng283 minmag '1' '1.0'  ->  '1.0'
ddmng284 minmag '7' 'NaN'  ->  '7'

-- expanded list from min/max 754r purple prose
-- [explicit tests for exponent ordering]
ddmng401 minmag  Inf    1.1     ->  1.1
ddmng402 minmag  1.1    1       ->  1
ddmng403 minmag  1      1.0     ->  1.0
ddmng404 minmag  1.0    0.1     ->  0.1
ddmng405 minmag  0.1    0.10    ->  0.10
ddmng406 minmag  0.10   0.100   ->  0.100
ddmng407 minmag  0.10   0       ->  0
ddmng408 minmag  0      0.0     ->  0.0
ddmng409 minmag  0.0   -0       -> -0
ddmng410 minmag  0.0   -0.0     -> -0.0
ddmng411 minmag  0.00  -0.0     -> -0.0
ddmng412 minmag  0.0   -0.00    -> -0.00
ddmng413 minmag  0     -0.0     -> -0.0
ddmng414 minmag  0     -0       -> -0
ddmng415 minmag -0.0   -0       -> -0
ddmng416 minmag -0     -0.100   -> -0
ddmng417 minmag -0.100 -0.10    -> -0.10
ddmng418 minmag -0.10  -0.1     -> -0.1
ddmng419 minmag -0.1   -1.0     -> -0.1
ddmng420 minmag -1.0   -1       -> -1
ddmng421 minmag -1     -1.1     -> -1
ddmng423 minmag -1.1   -Inf     -> -1.1
-- same with operands reversed
ddmng431 minmag  1.1    Inf     ->  1.1
ddmng432 minmag  1      1.1     ->  1
ddmng433 minmag  1.0    1       ->  1.0
ddmng434 minmag  0.1    1.0     ->  0.1
ddmng435 minmag  0.10   0.1     ->  0.10
ddmng436 minmag  0.100  0.10    ->  0.100
ddmng437 minmag  0      0.10    ->  0
ddmng438 minmag  0.0    0       ->  0.0
ddmng439 minmag -0      0.0     -> -0
ddmng440 minmag -0.0    0.0     -> -0.0
ddmng441 minmag -0.0    0.00    -> -0.0
ddmng442 minmag -0.00   0.0     -> -0.00
ddmng443 minmag -0.0    0       -> -0.0
ddmng444 minmag -0      0       -> -0
ddmng445 minmag -0     -0.0     -> -0
ddmng446 minmag -0.100 -0       -> -0
ddmng447 minmag -0.10  -0.100   -> -0.10
ddmng448 minmag -0.1   -0.10    -> -0.1
ddmng449 minmag -1.0   -0.1     -> -0.1
ddmng450 minmag -1     -1.0     -> -1
ddmng451 minmag -1.1   -1       -> -1
ddmng453 minmag -Inf   -1.1     -> -1.1
-- largies
ddmng460 minmag  1000   1E+3    ->  1000
ddmng461 minmag  1E+3   1000    ->  1000
ddmng462 minmag  1000  -1E+3    -> -1E+3
ddmng463 minmag  1E+3   -384    -> -384
ddmng464 minmag -384    1E+3    -> -384
ddmng465 minmag -1E+3   1000    -> -1E+3
ddmng466 minmag -384   -1E+3    -> -384
ddmng467 minmag -1E+3   -384    -> -384

-- subnormals
ddmng510 minmag  1.00E-383       0  ->   0
ddmng511 minmag  0.1E-383        0  ->   0
ddmng512 minmag  0.10E-383       0  ->   0
ddmng513 minmag  0.100E-383      0  ->   0
ddmng514 minmag  0.01E-383       0  ->   0
ddmng515 minmag  0.999E-383      0  ->   0
ddmng516 minmag  0.099E-383      0  ->   0
ddmng517 minmag  0.009E-383      0  ->   0
ddmng518 minmag  0.001E-383      0  ->   0
ddmng519 minmag  0.0009E-383     0  ->   0
ddmng520 minmag  0.0001E-383     0  ->   0

ddmng530 minmag -1.00E-383       0  ->   0
ddmng531 minmag -0.1E-383        0  ->   0
ddmng532 minmag -0.10E-383       0  ->   0
ddmng533 minmag -0.100E-383      0  ->   0
ddmng534 minmag -0.01E-383       0  ->   0
ddmng535 minmag -0.999E-383      0  ->   0
ddmng536 minmag -0.099E-383      0  ->   0
ddmng537 minmag -0.009E-383      0  ->   0
ddmng538 minmag -0.001E-383      0  ->   0
ddmng539 minmag -0.0009E-383     0  ->   0
ddmng540 minmag -0.0001E-383     0  ->   0


-- Null tests
ddmng900 minmag 10  # -> NaN Invalid_operation
ddmng901 minmag  # 10 -> NaN Invalid_operation
