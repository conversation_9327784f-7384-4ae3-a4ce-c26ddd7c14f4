------------------------------------------------------------------------
-- testall.decTest -- run all general decimal arithmetic testcases    --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- core tests (using Extended: 1) --------------------------------------
dectest: base

dectest: abs
dectest: add
dectest: and
dectest: clamp
dectest: class
dectest: compare
dectest: comparesig
dectest: comparetotal
dectest: comparetotmag
dectest: copy
dectest: copyabs
dectest: copynegate
dectest: copysign
dectest: divide
dectest: divideint
dectest: exp
dectest: fma
dectest: inexact
dectest: invert
dectest: ln
dectest: logb
dectest: log10
dectest: max
dectest: maxmag
dectest: min
dectest: minmag
dectest: minus
dectest: multiply
dectest: nextminus
dectest: nextplus
dectest: nexttoward
dectest: or
dectest: plus
dectest: power
dectest: powersqrt
dectest: quantize
dectest: randoms
dectest: reduce               -- [was called normalize]
dectest: remainder
dectest: remaindernear
dectest: rescale              -- [obsolete]
dectest: rotate
dectest: rounding
dectest: samequantum
dectest: scaleb
dectest: shift
dectest: squareroot
dectest: subtract
dectest: tointegral
dectest: tointegralx
dectest: trim
dectest: xor

-- The next are for the Strawman 4d concrete representations and
-- tests at those sizes [including dsEncode, ddEncode, and dqEncode,
-- which replace decimal32, decimal64, and decimal128]
dectest: decSingle
dectest: decDouble
dectest: decQuad

-- General 31->33-digit boundary tests
dectest: randombound32

