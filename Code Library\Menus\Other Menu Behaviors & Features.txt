

Damage HUD Overhaul 1.10
Replaces the Damage HUD update function to provide new features, such as 4 digit damage, customizable animations, and adjustable colors.
<https://smashboards.com/threads/damage-hud-overhaul.471310/>
[Punkline]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x802F5F4C ---- 3C60802F38834EDC -> 3C60802F 60644c04
------------- 0x802F491C ----

7C0802A6 90010004 9421FF90 DBE10068 DBC10060 DBA10058 DB810050 BF61003C 7C7B1B78 88030010 5400D7FF 41820084 C382E094 7F7DDB78 C3E2E098 3B800000 C3C2E0A0 C3A2E09C 3BE00001 3BC00000 4808BBBD 880DB6C1 EC1C0072 7C000775 41820014 FC000050 9BCDB6C1 EC00F828 4800000C EC00F82A 9BEDB6C1 D01D0034 4808BB8D EC1EE87A 3B9C0001 2C1C0004 D01D0044 3BBD0004 4180FFB8 881B0010 38600000 50603672 981B0010 48000198 C3A2E08C 7F7DDB78 C3C2E0AC 3BE00000 C3E2E0B0 83DD0054 281E0000 40820014 386DA110 388003E1 38ADA118 48093829 C01E0038 D001002C 8001002C 5400007E 9001002C C001002C FC00E840 40800084 83DD0054 C39D0034 281E0000 40820014 386DA110 3880044E 38ADA118 480937E9 C01E0038 EC00E02A D01E0038 801E0014 5400018D 4082004C 281E0000 41820044 40820014 386DA110 38800234 38ADA118 480937B5 809E0014 38600000 54800211 40820010 54800673 41820008 38600001 2C030000 4082000C 7FC3F378 4807E851 83DD0054 281E0000 40820014 386DA110 388003EE 38ADA118 4809376D C01E003C FC00F040 40810090 83DD0054 C39D0044 281E0000 40820014 386DA110 3880045A 38ADA118 48093741 C01E003C EC00E02A D01E003C 801E0014 5400018D 4082004C 281E0000 41820044 40820014 386DA110 38800234 38ADA118 4809370D 809E0014 38600000 54800211 40820010 54800673 41820008 38600001 2C030000 4082000C 7FC3F378 4807E7A9 C01D0044 EC00F828 D01D0044 3BFF0001 2C1F0004 3BBD0004 4180FE84 BB61003C 80010074 CBE10068 CBC10060 CBA10058 CB810050 38210070 7C0803A6 4E800020 7C0802A6 90010004 9421FF98 DBE10060 DBC10058 DBA10050 DB810048 DB610040 DB410038 DB210030 93E1002C 93C10028 7C7E1B78 93A10024 93810020 8803000F 28000000 418202D8 28000001 40820118 3BE00000 3BBE0000 839D0054 C39D0014 281C0000 40820014 386DA110 388003A4 38ADA118 48093629 D39C0038 801C0014 5400018D 4082004C 281C0000 41820044 40820014 386DA110 38800234 38ADA118 480935FD 809C0014 38600000 54800211 40820010 54800673 41820008 38600001 2C030000 4082000C 7F83E378 4807E699 839D0054 C39D0024 281C0000 40820014 386DA110 388003B3 38ADA118 480935B1 D39C003C 801C0014 5400018D 4082004C 281C0000 41820044 40820014 386DA110 38800234 38ADA118 48093585 809C0014 38600000 54800211 40820010 54800673 41820008 38600001 2C030000 4082000C 7F83E378 4807E621 3BFF0001 2C1F0004 3BBD0004 4180FF04 38000000 981E000F 480001BC 887E000E 3C004330 C822E078 9061001C C042E0A4 90010018 C8010018 EC000828 EF420032 FC1A1040 4080000C FF401090 48000014 C002E0A8 FC1A0040 40810008 FF400090 C382E0B4 7FDDF378 C3A2E070 3BE00000 C3C2E06C C3E2E0B0 4808B7E5 EC01E028 EC1D0032 EF7A0032 4808B7D5 EC01E028 FC1BF040 EC1D0032 EF3A0032 4080000C EC3BF828 48000008 EC3BF82A FC19F040 4080000C EF39F828 48000008 EF39F82A 839D0054 C01D0014 281C0000 EF60082A 40820014 386DA110 388003A4 38ADA118 48093475 D37C0038 801C0014 5400018D 4082004C 281C0000 41820044 40820014 386DA110 38800234 38ADA118 48093449 809C0014 38600000 54800211 40820010 54800673 41820008 38600001 2C030000 4082000C 7F83E378 4807E4E5 839D0054 C01D0024 281C0000 EF60C82A 40820014 386DA110 388003B3 38ADA118 480933F9 D37C003C 801C0014 5400018D 4082004C 281C0000 41820044 40820014 386DA110 38800234 38ADA118 480933CD 809C0014 38600000 54800211 40820010 54800673 41820008 38600001 2C030000 4082000C 7F83E378 4807E469 3BFF0001 2C1F0004 3BBD0004 4180FEB4 887E000F 3803FFFF 981E000F 8001006C CBE10060 CBC10058 CBA10050 CB810048 CB610040 CB410038 CB210030 83E1002C 83C10028 83A10024 83810020 38210068 7C0803A6 4E800020 7C0802A6 3C80804A 90010004 38000006 7C0903A6 9421FF08 DBE100F0 DBC100E8 DBA100E0 BEE100BC 3B2410C8 38B90000 38800000 83C30028 80050000 7C001840 40820010 1C040064 7FF90214 48000014 38A50064 38840001 4200FFE0 3BE00000 881F0010 5400CFFF 41820010 7FE3FB78 4BFFF9D1 48000BD8 7FC3F378 4BD16145 889F0010 3BA30000 5480E7FF 418202CC 5480FFBE 28000001 4082000C 2C1D0000 408202B8 881F0010 38600001 50600F7C 981F0010 7FC3F378 4807A6B1 3AF90260 80B9025C 80D90260 7FC3F378 80F90264 38800001 4BD170CD 7FC3F378 C022E06C 4807A901 809F0060 28040000 4182007C 80770000 80840018 80630000 80840008 80630000 83040008 80830004 7F03C378 80840004 80840004 80840008 80840008 4806970D 887F0008 7C630774 4BD3F62D 2C030000 41820014 7F03C378 C022E068 480697E5 48000010 7F03C378 C022E06C 480697D5 80780064 C022E06C 480702D5 7F03C378 48069B2D 387E0000 38800001 38A00400 4BF3A389 3C606666 A8FF000A 3B836667 80770000 7C1C3896 835F005C 80630000 809A0018 80630000 7C001670 80A40008 54060FFE 80830008 7C003214 80650008 1C00000A 80840008 7C003850 5418063E 48069669 930100B4 3F604330 807A0018 936100B0 80630008 C822E078 C80100B0 C042E070 EC000828 80630008 EC220032 48069731 807A0018 C022E06C 80630008 80630008 80630064 48070225 3C6051EC A8FF000A 3B43851F 80770000 7C1A3896 831F0058 80630000 80980018 80630000 7C002E70 80A40008 54060FFE 80830008 7C003214 80650008 1C000064 80840008 7C003850 7C1C0096 7C001670 54050FFE 7C002A14 541C063E 480695C1 938100AC 80780018 936100A8 80630008 C822E078 C80100A8 C042E070 EC000828 80630008 EC220032 4806968D 80780018 C022E06C 80630008 80630008 80630064 48070181 3C601062 A8FF000A 38034DD3 80770000 7C003896 831F0054 80630000 80980018 80630000 7C003670 80A40008 54060FFE 80830008 7C003214 80650008 1C0003E8 80840008 7C003850 7C1A0096 7C002E70 54050FFE 7C002A14 5417063E 4806951D 92E100A4 80780018 936100A0 80630008 C822E078 C80100A0 C042E070 EC000828 80630008 EC220032 480695E9 80780018 C022E06C 80630008 80630008 80630064 480700DD 7FC3F378 4807B6F1 3C606666 A8DF000A 3B436667 831F005C 7C1A3096 80780018 80630008 3AF90260 80630008 80990260 7C001670 54050FFE 80840000 7C002A14 1C00000A 80840000 80840008 7C003050 5419063E 80840008 4806947D 932100A4 3F804330 80780018 938100A0 80630008 C822E078 C80100A0 C042E070 EC000828 80630008 EC220032 48069545 80780018 C022E06C 80630008 80630008 80630064 48070039 3C6051EC A8FF000A 3B63851F 80770000 7C1B3896 831F0058 80630000 80980018 80630000 7C002E70 80A40008 54060FFE 80830008 7C003214 80650008 1C000064 80840008 7C003850 7C1A0096 7C001670 54050FFE 7C002A14 5419063E 480693D5 932100AC 80780018 938100A8 80630008 C822E078 C80100A8 C042E070 EC000828 80630008 EC220032 480694A1 80780018 C022E06C 80630008 80630008 80630064 4806FF95 3C601062 A89F000A 38034DD3 7C002096 7C003670 54030FFE 7C001A14 1C0003E8 7C002050 7C1B0096 7C002E70 54030FFE 7C001A15 40820040 7C1B2096 7C002E70 54030FFE 7C001A14 1C000064 7C002050 7C1A0096 7C001670 54030FFE 7C001A15 40820014 807F0058 38800010 4807C9B5 48000010 807F0058 38800010 4807CBA5 3C601062 A8FF000A 3B834DD3 80770000 7C1C3896 831F0054 80630000 80980018 80630000 7C003670 80A40008 54060FFE 80830008 7C003214 80650008 1C0003E8 80840008 3CA051EC 7C003850 3B65851F 7C1B0096 7C002E70 54050FFE 7C002A14 5417063E 480692A9 92E100A4 3C004330 80780018 900100A0 80630008 C822E078 C80100A0 C042E070 EC000828 80630008 EC220032 48069371 80780018 C022E06C 80630008 80630008 80630064 4806FE65 A89F000A 7C1C2096 7C003670 54030FFE 7C001A14 1C0003E8 7C002050 7C1B0096 7C002E70 54030FFE 7C001A15 40820014 807F0054 38800010 4807C8B9 48000010 807F0054 38800010 4807CAA9 A87F000C A81F000A 7C030000 4182032C 887F0008 7C630774 4BD3F125 2C030000 41820140 A87F000A 2C030064 4081000C 38600064 48000010 7C600735 40800008 38600000 7C600734 88EDA108 6C038000 880DA10C 906100A4 3CC04330 7C070050 C882E080 90C100A0 6C008000 C002E08C C82100A0 38ADA108 900100AC 388DA10C EC212028 90E100B4 380000FF C0A2E068 EC610024 90C100A8 C842E078 90C100B0 C82100A8 C80100B0 EC651828 EC212028 EC001028 EC03007A FC00001E D8010098 8061009C 98610040 88E50001 88640001 90E1008C 7C671850 6C638000 90C10088 90610094 C8010088 90C10090 EC001028 C8210090 EC212028 EC03007A FC00001E D8010080 80610084 98610041 88A50002 88640002 90A10074 7C651850 6C638000 90C10070 9061007C C8010070 90C10078 EC001028 C8210078 EC212028 EC03007A FC00001E D8010068 8061006C 98610042 98010043 80010040 90010060 48000134 A87F000A 2C03012C 4081000C 3860012C 48000010 7C600735 40800008 38600000 7C600734 88EDA108 6C038000 880DA10C 9061006C 3CC04330 7C070050 C862E080 90C10068 6C008000 C002E088 C8210068 38ADA108 90010074 388DA10C EC211828 90E1007C 380000FF C842E078 EC810024 90C10070 90C10078 C8210070 C8010078 EC211828 EC001028 EC04007A FC00001E D8010080 80610084 98610038 88E50001 88640001 90E10094 7C671850 6C638000 90C10090 9061008C C8010090 90C10088 EC001028 C8210088 EC211828 EC04007A FC00001E D8010098 8061009C 98610039 88A50002 88640002 90A100AC 7C651850 6C638000 90C100A8 906100A4 C80100A8 90C100A0 EC001028 C82100A0 EC211828 EC04007A FC00001E D80100B0 806100B4 9861003A 9801003B 80010038 90010060 807F0054 88010060 80630018 80830008 8064000C 98030004 88A10061 8064000C 98A30005 88C10062 8064000C 98C30006 807F0058 88010060 80630018 80830008 8064000C 98030004 8064000C 98A30005 8064000C 98C30006 807F005C 88010060 80630018 80830008 8064000C 98030004 8064000C 98A30005 8064000C 98C30006 807F0060 88010060 80630018 80830008 8064000C 98030004 8064000C 98A30005 8064000C 98C30006 7FC3F378 4BD15869 2C030000 41820064 3AE00000 3B1F0000 83380054 28190000 40820014 386DA110 388003E1 38ADA118 480929C1 C0190038 D0180014 83380054 28190000 40820014 386DA110 388003EE 38ADA118 4809299D C019003C 3AF70001 2C170004 D0180024 3B180004 4180FFAC 3C606666 A89F000A 38036667 7C002096 7C001670 54030FFE 7C001A14 1C00000A 7C002050 2C000001 4082000C C3E2E090 48000008 C3E2E06C 3C6051EC 3803851F 7C002096 7C002E70 54030FFE 7C001A14 1C000064 3C606666 7C002050 38636667 7C030096 7C001670 54030FFE 7C001A14 2C000001 4082000C C3C2E090 48000008 C3C2E06C 82FF0060 C01F0020 28170000 EFA0F828 40820014 386DA110 388003A4 38ADA118 480928DD D3B70038 80170014 5400018D 4082004C 28170000 41820044 40820014 386DA110 38800234 38ADA118 480928B1 80970014 38600000 54800211 40820010 54800673 41820008 38600001 2C030000 4082000C 7EE3BB78 4807D94D EFFFF02A 82FF0058 C01F0018 28170000 EFA0F82A 40820014 386DA110 388003A4 38ADA118 4809285D D3B70038 80170014 5400018D 4082004C 28170000 41820044 40820014 386DA110 38800234 38ADA118 48092831 80970014 38600000 54800211 40820010 54800673 41820008 38600001 2C030000 4082000C 7EE3BB78 4807D8CD 3C601062 A89F000A 38034DD3 7C002096 7C003670 54030FFE 7C001A14 1C0003E8 3C6051EC 7C002050 3863851F 7C030096 7C002E70 54030FFE 7C001A14 2C000001 4082000C C002E090 48000008 C002E06C EC3E002A 82FF0054 C01F0014 28170000 EFFF082A EFA0F82A 40820014 386DA110 388003A4 38ADA118 48092789 D3B70038 80170014 5400018D 4082004C 28170000 41820044 40820014 386DA110 38800234 38ADA118 4809275D 80970014 38600000 54800211 40820010 54800673 41820008 38600001 2C030000 4082000C 7EE3BB78 4807D7F9 881F0010 5400DFFF 4182000C 3800000A 981F000F 387F0000 389D0000 4BFFF075 880DB6C0 28000005 4180000C C002E0B8 D01E002C BAE100BC 800100FC CBE100F0 CBC100E8 CBA100E0
->

3D80802F 618C5950 A18C0000 7C006000
b 0x802f5c14
3D80802F 618C5950 A18C0000 7C006000
b 0x802F5D1C
3D80802F 618C5950 A18C0000 B181FFF0 7D97E2A6 3D600005 7D77E3A6 E001FFF0 7D97E3A6
b 0x8006cd40
3D80804C 618C1FB0 816C0000 716B0060 41A2000C 38000000 48000010 3D80802F 618C5950 A00C0000
b 0x80189700
3D80802F 618C5950 A18C0000 7C036040
b 0x80189738
3D80802F 618C5950 A18C0000 7C006040
b 0x80189758
28830000 3D80804C 618C1FB8 816C0000 716B0060 4182000C 38000000 48000010 3803FFFF 40860008
b 0x801896fc
b 0x801896d0
3D80802F 618C5950 A00C0002
b 0x801B93C4
3C60802F 60635A28 808DB6BC 80840020 38A30000 38C40568 80060010 90050010 90A40634 38A30040 80C41180 90A411B8 90C50008 38A3004C 80C446BC 90A446F4 90C50008 80010024
b 0x802F6774
7C0802A6 90010004 9421FFE0 93E1000C 7C7F1B78 D0210010 D0410014
bl 0x8035e708
7FE3FB78 C0210010
bl 0x8035e800
807F0064 C0210014
bl 0x8036530c
83E1000C 38210020 80010004 7C0803A6 4E800020 7C0802A6 90010004 9421FFE0 90610010 D0210014
bl 0x8000C07C
80610010 C0210014
bl 0x8036F8BC
38210020 80010004 7C0803A6 4E800020 80030014 5400018D 4C820020 54000211 4082000C 54000673 4C820020
b 0x803732E8
7C042800 7C843000 4C802B82 54631839 4C422382 41820048 7D0784AA 7C044000 40810044 7C633A14 7C071800 7D0784AA 38E70008 7C8A4000 4C0029C2 41800028 7C045000 4181FFE4 7C832378 3881FFF0 7D0485AA 7C8484AA 48000028 39200000 39600000 38000000 9001FFF0 C021FFF0 7D234B78 7D645B78 7D254B78 4E800020 7D57E2A6 3D003005 7D17E3A6 7C641850 7D243050 9061FFE0 9121FFE2 B061FFE6 E0217FE0 E0413FE4 7D57E3A6 10811114 106208D4 7CA32B78 7CE43B78 EC241824 10210C20 7C17E2A6 3CA00804 60A50804 7CB7E3A6 C0C28028 ECC60828 9061FFF0 9081FFF4 E0417FF0 E0617FF2 E0817FF4 E0A17FF6 10040072 100201BA F0017FF8 10050072 100301BA F0017FFA 80A1FFF8 7C17E3A6 4E800020 7C0802A6 7C800026 90010004 9421FF20 90810014 BE010018 3E00802F 62105950 3E20804A 623110C8 3E40804A 62521064 38B20258 84920064 7C032000 7C922800 4182000C 4184FFF0 480005B0 3E60802F 627359C8 88D20008 54C62036 3A730006 82830028 82B20060 82B50008 8372000F 577C877C 88720008
bl 0x80034634
507B8B9C 577B021E 7F638120 7E83A378
bl 0x8000B09C
7C601120 4FEBF902 2C1C0002 4D6B1102 4D6BFB82 4D284A02 3B000004 418900AC 418800A8 83510260 835A0000 835A0000 837A0004 837B0004 837B0004 835A0008 837B0008 835A0008 837B0008 40AB0034 3911025C 38000000 7E83A378 900100A0 38800001 C02100A0 7CA864AA 4BFFFD99 38600002 88920010 5064077C 98920010 80F0001C 40AE0008 80F00020 2C070000 41A0001C 40AE0010 80F00028 90F00020 4800000C 80F00024 90F0001C 38600FFF A092000A 38A00000 A0D00000 4BFFFD9D 90A10094 A3B2000A 2C180004 4E021382 2C180003 4E221382 5717103B 4E421382 3AD20050 7C76B82E 40B20008 7EA3AB78 40A800EC 40A90064 8B8DB6C1 3BA00001
bl 0x80380528
D02100A0
bl 0x80380528
C04100A0 102114E0 E0500034 7F9CEA79 9B8DB6C1 E070002C 100110FA 4182000C 10200050 10010460 40B2000C F0130008 48000150 102004E0 3AD20030 7C16BD2E 3AD20040 7C36BD2E 48000138 3AD20030 7C76BC2E 3AD20040 7C96BC2E 40B2000C C0730008 C093000C C0230038 C043003C C0B00040 C0D0003C FCE03050 EC21182A EC42202A EC842828 FC023040 FC823840 40A10010 FC403090 40A40008 FC403890 D0230038 D043003C 40B20010 D0730008 D093000C 48000014 3AD20030 7C76BD2E 3AD20040 7C96BD2E 4BFFFC65 480000B4 3B80000A 80630018 80630008 8083000C 80630008 80010094 90040004 40B00028 7F64DB78 3BC00000 3FE03F80 BFC100A0 C02100A0 40AE0048 3BC00001 C02100A4 4800003C 7F44D378 3BC00000 2C1D0000 41A20014 7FFDE3D6 7FDFE1D6 7FDEE850 7FFDFB78 3CA03F04 7CD7E2A6 7CB7E3A6 9BC100A0 E021F0A0 7CD7E3A6 3BE10058 7FDFC1AE 3BE10060 7C3FBD2E 38000000 900100A0 C04100A0 4BFFFB45 57C0103A 3BF00050 7C3F042E 3BE10074 7C3FBD2E 3718FFFF 38000000 7C008120 41800008 4BFFFE2C 40A80014 88120010 540006B0 98120010 480002B8 418802B4 7E83A378
bl 0x80370928
E0210074 C0A28044 E041007C 10A21154 10C214E0 10820956 FCC03050 10852116 D0C10084 10810914 A072000A 10842114 F0A1007C F0810074 A092000C 7C032000 40AE0008 7C041800 7C77E2A6 3C800704 3CA00004 60A50004 7CB7E3A6 E032F00E D0210098 40A10048 7C97E3A6 E0307004 7CB7E3A6 E052F00E C0700008 10030098 1060005A E0907006 10032040 108320C0 40A00008 10642420 40A50008 106424E0 EC420072 10421C60 F052700E 7C77E3A6 80120024 90130004 8812000F 2C000001 4D810B82 4DA21382 7E83A378
bl 0x8000B09C
9061009C 3B000004 3B40000A 5717103B 4E421382 2C180003 4E600382 3AD20050 7C76B82E 40B20008 7EA3AB78 7C7B1B78 8001009C 2C000000 41A2003C BBC30038 40B20024 BFD30000 C0520018 C0320014 FC411028 EC21102A D0230038 D0330000 48000014 3AD20010 7FD6B92E 3AD20020 7FF6B92E 8012000A 3AC10074 2C0003E8 7C56BC2E 4180000C C0100018 EC42002A 3AD20010 7C36BC2E 40B20008 C0330000 EC21102A D0230038 38800010 4093001C A0B2000A 7C1A2800 1F5A000A 4081000C
bl 0x80371D9C
48000024
bl 0x80371F9C
3AD20010 7D76BC2E 3AD20020 7D96BC2E 118B6460 40B20008 E1930000 40AC0088
bl 0x80380528
D02100A0
bl 0x80380528
C10100A0 110144E0 C122805C C1428048 11084828 110802B2 C070004C FC801850 106820EE 7C17E2A6 3C600004 7C77E3A6 E0B2F00E 7C17E3A6 C0500044 C0F00048 ECC500B2 FC061040 40A00008 FCC01090 FC063840 40A10008 FCC03890 110819BA 1188602A 40B20024 8872000F 3863FFFF 9872000F 48000014 40AD0014 40B2000C 38000000 9812000F F19B0038 7F63DB78 4BFFF921 3718FFFF 38000000 7C008120 4080FE74 C030000C E0500010 FC600890 A012000A 2C0003E8 41A00008 10630098 880DB6C0 2C000004 40A10008 1063009A D074002C D0340030 7E83A378 4BFFF8D5 80810014 BA010018 382100E0 80010004 7C8FF120 7C0803A6 4E800020

# default color data: damage
# 802F5218
00000000 FFFFFFFF
0000012C 500000FF
00000000

# default color data: stamina
# 802F522C
00000000 500000FF
00000064 FFFFFFFF
00000000


# default custom gradient allocation:
# 802F5240
00000000 FFFFFFFF
00000028 FFFFE0FF
00000050 FFCC91FF
00000078 FA926CFF
000000A0 DE5F5CFF
000000C8 B63249FF
000000F0 850F2CFF
00000118 500000FF
00000000 00000000
00000000 00000000
00000000 00000000
00000000 00000000
00000000 00000000
00000000 00000000
00000000 00000000
00000000 00000000
00000000

# additional space
00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000

# additional example gradients:

# 802F569C
# white->red monochrome, with tint->tone->shade
# classic colors, but with a bit more emphasis on the red
00000000 FFFFFFFF
00000064 FF9078FF
000000C8 CB0518FF
0000012C 500000FF
00000000

# 802F56C0
# lightyellow->red analogous,  tint->tone
00000000 FFFFFFFF
0000001E FFFFE0FF
0000005A FFC94EFF
00000096 FF8808FF
000000D2 FF0000FF
00000000


# 802F56EC
# lightcyan->orange complementary,  tint->tone
0000001E E8FFFFFF
0000005A F8B685FF
00000096 E36D28FF
000000D2 C00000FF
00000000

# 802F5710
# lightyellow->dark burgundy analogous, tint->tone->shade
00000000 FFFFFFFF
00000028 FFFFE0FF
00000050 FFCC91FF
00000078 FA926CFF
000000A0 DE5F5CFF
000000C8 B63249FF
000000F0 850F2CFF
00000118 500000FF
00000000


# 802F5754
# pink->salmon->indigo analogous, tint->tone->shade
0000001E FDE0DDFF
00000046 FFAB9CFF
0000006E FA726FFF
00000096 DA3F61FF
000000BE A91762FF
000000E6 6D0260FF
0000010E 300050FF
00000000

# 802F5790
# yellowgreen->jade->darkblue analogous, tint->tone->shade
0000000A F2FFE2FF
00000032 A5DBBAFF
0000005A 69AFB7FF
00000082 4C80A7FF
000000FA 000050FF
00000000

# 802F57BC
# yellow->orange->red->purple->darkred analogous, tint->tone->shade
00000000 FFFFF0FF
0000003C FFC65EFF
00000078 FF8521FF
000000B4 FF0000FF
000000F0 BE0042FF
0000012C 810040FF
00000168 500000FF
00000000

# 802F57F8
# higher contrast variation of the above; complementary
00000000 FFFFF0FF
0000003C FFC961FF
00000078 FF871CFF
000000B4 FF0000FF
000000F0 B00048FF
0000012C 5F0060FF
00000168 000050FF
00000000

# 802F5834
# lightyellow->green->teal analogous, tint->tone->shade
0000000A FFFFE0FF
00000032 FAE983FF
0000005A D9D844FF
00000082 9ACD32FF
000000AA 39A844FF
000000D2 217A4DFF
000000FA 005050FF
00000000

# 802F5870
# jade->teal variation of the above
00000000 FFFFE0FF
00000032 C3DF93FF
00000064 83BC7AFF
00000096 009999FF
000000C8 0B8080FF
000000FA 0F6767FF
0000012C 105050FF
00000000

# 802F58AC
# lavender->red analogous, tint->tone->shade
00000000 E6E6FAFF
0000003C E0AEDDFF
00000078 D973B5FF
000000B4 DC143CFF
000000F0 AB0B29FF
0000012C 7C0417FF
00000168 500000FF
00000000

# 802F58E8
# cyan->red complementary, tint->tone
00000000 E8FFFFFF
00000019 D0E9F9FF
00000032 CDCDF2FF
0000004B D9ABE6FF
00000064 EC82CCFF
0000007D FF4D99FF
0000012C FF0000FF
00000000

# 802F5924
# cyan->darkblue analogous, tint->tone->shade
00000000 E8FFFFFF
0000003C 57BCBBFF
00000078 007080FF
000000B4 173968FF
00000168 000050FF
00000000

# USER PARAMETERS

# damage limit:
# 802F5950
270F     # Damage Capacity (must be <= 9999) (0x270F)
0096     # Starting HP in Stamina mode

# shake:
# 802F5954
80       # Shake Intensity Slider (0...256 == 0.0...2.0)
80       # Shake Duration  Slider (0...256 == 0.0...2.0)
0A       # Shake Duration Range min (in frames)
0A       # Shake Duration Range max (in frames)
3F2A7EFA # Shake Frames per Damage Point (capped by range)

# scale:
# 802F595C
3F800000 # Default XY scale
3F47AE14 # X-Width % for 4 digit number
3F266666 # X-Width % for 5+ player HUD width
3FC00000 # X-Padding for 4 digit number

# active colors:
# 802F596C
802F5240 # Active Damage colors
00000000
# if these are null, they will copy the "default" colors instead
# else, pointer address is used to define a unique color gradient

# default colors:
# 802F5974
802F5218 # Default Damage colors
802F522C # Default Stamina colors
# when active colors are nullified, these are used instead

# death anim:
# 802F597C
3F1BB98C # Random X-Velocity Limit
3F4F9DB2 # Random Y-Velocity Limit
3E9BB2FF # X-Velocity pad
3F9BB646 # Y-Velocity pad
42C80000 # Y-Translation Range
3E4FAACE # Gravity
# modify these to change the procedural animation constraints

# shake anim:
# 802F5994
3DCFAACE # Damage Weight (distance per damage)
3FC2A64C # Shake Maximum Distance
3E4FAACE # Shake Distance Padding
# modify these to change the procedural animation constraints

# font spacing:
# 802F59A0
00000000 # 0
3F01C433 # 1
00000000 # 2
00000000 # 3
00000000 # 4
00000000 # 5
00000000 # 6
00000000 # 7
00000000 # 8
00000000 # 9

# variables, and other data used by functions:
# 802F59C8
00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00080000 00000000 00000000 00000000 00000000 00000000 00000000 3F800000 3F800000 3F800000 C12FAE19 C1081547 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000

# toc:
802F491C 802F4930 802F4944 802F496C 802F4998 802F49AC 802F49C0 802F49F0 802F4A00 802F4A4C 802F4A98 802F4ACC 802F4AEC 802F4B6C 802F4BAC 802F4C04 802F5218 802F522C 802F5240 802F569C 802F56C0 802F56EC 802F5710 802F5754 802F5790 802F57BC 802F57F8 802F5834 802F5870 802F58AC 802F58E8 802F5924 802F5950 802F5954 802F595C 802F596C 802F5974 802F597C 802F5994 802F59A0 802F59C8 802F5A28 802F5A68 802F5A74 802F5A88

------------- 0x802F5C10 ---- 2C0003E7 -> b 0x802F491C
------------- 0x802F5C18 ---- 380003E7 -> 7D806378
------------- 0x802F5D18 ---- 2C0003E7 -> b 0x802F4930
------------- 0x802F5D20 ---- 380003E7 -> 7D806378
------------- 0x8006CD3C ---- C00288B0 -> b 0x802F4944
# set cap according to damage max
------------- 0x801896FC ---- 380003E7 -> b 0x802F496C
# fixes cap when decrementing below 0 in training mode
------------- 0x80189734 ---- 280303E7 -> b 0x802F4998
# compares against damage cap instead of 999 in training mode
------------- 0x80189754 ---- 280003E7 -> b 0x802F49AC
# compares against damage cap instead of 999 in training mode
------------- 0x801896C8 ---- 41820034 -> 60000000
------------- 0x801896CC ---- 3803FFFF -> b 0x802F49C0
# allows holding L/R while decrementing skip to 0 in training mode
------------- 0x801B93C0 ---- 38000096 -> b 0x802F49F0
# set stamina mode starting HP
------------- 0x802F6770 ---- 80010024 -> b 0x802F4A00
# HUD INIT: the stack is collapsing for end of init function
------------- 0x802F6178 ---- 4BFFED65 -> bl 0x802F4C04
# HUD UPDATE: main overhaul code, as a function call


	-==-


Disable Automatic Start Screen Transitions
-Game never transitions away from the title screen by itself.
[achilles]
Version -- DOL Offset ------ Hex to Replace ----------
1.02 ------ 0x19e838 ---- 800db078 -> 38000000 ----


	-==-


Set Menu Music in Sound Test
- The last song played in Sound Test will play for all menus, including the CSS and SSS.
- Remembers the last song even after matches.
[Dan Salvato] 

Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0x2480B8 ---- 7C7C002E -> Branch

7C7C002E 2C030000
40820008 3860FFFF
808D8840 98641851
3C808046 60849D40
9064005C 2C030000
40800008 38600000
4BE200C8

----------- 0x22B514 ---- 4BDF55F5 -> Branch

3C808046 60849D40
8084005C 2C040000
41820018 41810008
38800000 80AD8840
98851851 7C832378
3D808002 618C3F28
7D8903A6 4E800421
4BE034E8


	-==-


Extended Name Entry
English alphabet name tags can be up to 8 characters in length.
Press X Button to shift between upper and lowercase characters.
[Dan Salvato]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8023C718 --- 38030001 -> Branch

1CC30003 7CC6F214
88060001 2C000000
7C601B78 41820008
38030001 48000000

1.02 ----- 0x8023C270 --- 98030000 -> Branch

88C30000 2C060000 
41820008 38630001
98030000 48000000

1.02 ----- 0x8023C710 --- 28030003 -> Branch

28030003 4180001C 
1CC30003 7CC6F214	
88060001 2C000000
40820008 38600002
28030003 48000000

1.02 ----- 0x8023cdd4 --- 7C7E01AE -> 7c7e032e
1.02 ----- 0x8023cdb0 --- 98040000 -> b0040000
1.02 ----- 0x8023C5A0 --- 546005AD -> Branch

5460056B 41820068
88BC0050 28050002
4082005C 3CA0804D
60A54D90 38C00000
2C0600B4 41810038
7CE628AE 2C070041
41800024 2C07007A
4181001C 2C070061
4180000C 38E7FFE0
48000008 38E70020
7CE629AE 38C60004
4BFFFFC8 3D808023
618CC7EC 7D8903A6
4E800420 546005AD
60000000 48000000

1.02 ----- 0x804d4d90 ---

82690000 82730000
82580000 81440000
82680000 82720000
82570000 81900000
82670000 82710000
82560000 81950000
82660000 82700000
82550000 81930000
82650000 826f0000
82790000 82540000
81970000 82640000
826e0000 82780000
82530000 81480000
82630000 826d0000
82770000 82520000
81490000 82620000
826c0000 82760000
82510000 81810000
82610000 826b0000
82750000 82500000
817b0000 82600000
826a0000 82740000
824f0000 817c0000

->

4a000000 54000000
39000000 2e000000
49000000 53000000
38000000 81900000
48000000 52000000
37000000 81950000
47000000 51000000
36000000 81930000
46000000 50000000
5a000000 35000000
81970000 45000000
4f000000 59000000
34000000 81480000
44000000 4e000000
58000000 33000000
81490000 43000000
4d000000 57000000
32000000 81810000
42000000 4c000000
56000000 31000000
817b0000 41000000
4b000000 55000000
30000000 817c0000


	-==-


Name Tag Shadow Box Automatically Scales to Tag Length
[Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x802FCCE8 --- 807E0010 -> Branch

3C608003 6063556C
7C6803A6 887F0000
4E800021 5460063E
28000078 41820070
3C608045 6063D84F
1C0001A4 7C630214
38800000 8C030001
2C000000 41820018
38840001 2C000080
41A0FFEC 8C030001
4BFFFFE4 2C040005
41800034 3C604080
9061FFF0 C221FFF0
3E404330 CA028C58
9241FFEC 9081FFF0
C9E1FFEC EDEF8028
EDEF8824 807E0010
D1E3002C 807E0010
60000000 48000000


	-==-


Automatically Select Yes if coins are lower than the requirements
For those who lost the battles in regular match, this will select the "YES" button for you without having to manually select it. There is also no coin loss.
[Brandondorf9999]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.00 ----- 0x8019ea7c --- 801E0070 -> 4800000c
1.00 ----- 0x8019ea24 --- 3C600001 -> 4800001c
1.00 ----- 0x8019ea40 --- 3800000A -> 38000009
1.00 ----- 0x8019fc90 --- 40820020 -> 40820018
1.00 ----- 0x8019FD3C --- 38000000 -> 38000001

1.01 ----- 0x801a0390 --- 40820020 -> 40820018
1.01 ----- 0x801A043C --- 38000000 -> 38000001
1.01 ----- 0x8019f17c --- 801E0070 -> 4800000c
1.01 ----- 0x8019F124 --- 3C600001 -> 4800001c
1.01 ----- 0x8019F140 --- 3800000A -> 38000009

1.02 ----- 0x801A0A84 --- 38000000 -> 38000001
1.02 ----- 0x801A09D8 --- 40820020 -> 40820018
1.02 ----- 0x8019F7C4 --- 801E0070 -> 4800000c
1.02 ----- 0x8019F76C --- 3C600001 -> 4800001C
1.02 ----- 0x8019F788 --- 3800000A -> 38000009

PAL ------ 0x801a02cc --- 3C600001 -> 4800001c
PAL ------ 0x801a02e8 --- 3800000A -> 38000009
PAL ------ 0x801a0324 --- 801E0070 -> 4800000c
PAL ------ 0x801A15E4 --- 38000000 -> 38000001
PAL ------ 0x801a1538 --- 40820020 -> 40820018