------------------------------------------------------------------------
-- ddEncode.decTest -- decimal eight-byte format testcases            --
-- Copyright (c) IBM Corporation, 2000, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
-- [Previously called decimal64.decTest]
version: 2.59

-- This set of tests is for the eight-byte concrete representation.
-- Its characteristics are:
--
--  1 bit  sign
--  5 bits combination field
--  8 bits exponent continuation
-- 50 bits coefficient continuation
--
-- Total exponent length 10 bits
-- Total coefficient length 54 bits (16 digits)
--
-- Elimit =  767 (maximum encoded exponent)
-- Emax   =  384 (largest exponent value)
-- Emin   = -383 (smallest exponent value)
-- bias   =  398 (subtracted from encoded exponent) = -Etiny

-- The testcases here have only exactly representable data on the
-- 'left-hand-side'; rounding from strings is tested in 'base'
-- testcase groups.

extended:    1
clamp:       1
precision:   16
rounding:    half_up
maxExponent: 384
minExponent: -383

-- General testcases
-- (mostly derived from the Strawman 4 document and examples)
dece001 apply   #A2300000000003D0 -> -7.50
dece002 apply   -7.50             -> #A2300000000003D0
-- derivative canonical plain strings
dece003 apply   #A23c0000000003D0 -> -7.50E+3
dece004 apply   -7.50E+3          -> #A23c0000000003D0
dece005 apply   #A2380000000003D0 -> -750
dece006 apply   -750              -> #A2380000000003D0
dece007 apply   #A2340000000003D0 -> -75.0
dece008 apply   -75.0             -> #A2340000000003D0
dece009 apply   #A22c0000000003D0 -> -0.750
dece010 apply   -0.750            -> #A22c0000000003D0
dece011 apply   #A2280000000003D0 -> -0.0750
dece012 apply   -0.0750           -> #A2280000000003D0
dece013 apply   #A2200000000003D0 -> -0.000750
dece014 apply   -0.000750         -> #A2200000000003D0
dece015 apply   #A2180000000003D0 -> -0.00000750
dece016 apply   -0.00000750       -> #A2180000000003D0
dece017 apply   #A2140000000003D0 -> -7.50E-7
dece018 apply   -7.50E-7          -> #A2140000000003D0

-- Normality
dece020 apply   1234567890123456   -> #263934b9c1e28e56
dece021 apply  -1234567890123456   -> #a63934b9c1e28e56
dece022 apply   1234.567890123456  -> #260934b9c1e28e56
dece023 apply  #260934b9c1e28e56   -> 1234.567890123456
dece024 apply   1111111111111111   -> #2638912449124491
dece025 apply   9999999999999999   -> #6e38ff3fcff3fcff

-- Nmax and similar
dece031 apply   9999999999999999E+369   -> #77fcff3fcff3fcff
dece032 apply   9.999999999999999E+384  -> #77fcff3fcff3fcff
dece033 apply   #77fcff3fcff3fcff       -> 9.999999999999999E+384
dece034 apply   1.234567890123456E+384  -> #47fd34b9c1e28e56
dece035 apply   #47fd34b9c1e28e56       -> 1.234567890123456E+384
-- fold-downs (more below)
dece036 apply   1.23E+384               -> #47fd300000000000 Clamped
dece037 apply   #47fd300000000000       -> 1.230000000000000E+384
decd038 apply   1E+384                  -> #47fc000000000000 Clamped
decd039 apply   #47fc000000000000       -> 1.000000000000000E+384

decd051 apply   12345                   -> #22380000000049c5
decd052 apply   #22380000000049c5       -> 12345
decd053 apply   1234                    -> #2238000000000534
decd054 apply   #2238000000000534       -> 1234
decd055 apply   123                     -> #22380000000000a3
decd056 apply   #22380000000000a3       -> 123
decd057 apply   12                      -> #2238000000000012
decd058 apply   #2238000000000012       -> 12
decd059 apply   1                       -> #2238000000000001
decd060 apply   #2238000000000001       -> 1
decd061 apply   1.23                    -> #22300000000000a3
decd062 apply   #22300000000000a3       -> 1.23
decd063 apply   123.45                  -> #22300000000049c5
decd064 apply   #22300000000049c5       -> 123.45

-- Nmin and below
decd071 apply   1E-383                  -> #003c000000000001
decd072 apply   #003c000000000001       -> 1E-383
decd073 apply   1.000000000000000E-383  -> #0400000000000000
decd074 apply   #0400000000000000       -> 1.000000000000000E-383
decd075 apply   1.000000000000001E-383  -> #0400000000000001
decd076 apply   #0400000000000001       -> 1.000000000000001E-383

decd077 apply   0.100000000000000E-383  -> #0000800000000000      Subnormal
decd078 apply   #0000800000000000       -> 1.00000000000000E-384  Subnormal
decd079 apply   0.000000000000010E-383  -> #0000000000000010      Subnormal
decd080 apply   #0000000000000010       -> 1.0E-397               Subnormal
decd081 apply   0.00000000000001E-383   -> #0004000000000001      Subnormal
decd082 apply   #0004000000000001       -> 1E-397                 Subnormal
decd083 apply   0.000000000000001E-383  -> #0000000000000001      Subnormal
decd084 apply   #0000000000000001       -> 1E-398                 Subnormal
-- next is smallest all-nines
decd085 apply   9999999999999999E-398   -> #6400ff3fcff3fcff
decd086 apply   #6400ff3fcff3fcff       -> 9.999999999999999E-383
-- and a problematic divide result
decd088 apply   1.111111111111111E-383  -> #0400912449124491
decd089 apply   #0400912449124491       -> 1.111111111111111E-383

-- forties
decd090 apply        40                -> #2238000000000040
decd091 apply        39.99             -> #2230000000000cff

-- underflows cannot be tested as all LHS exact

-- Same again, negatives
-- Nmax and similar
decd122 apply  -9.999999999999999E+384  -> #f7fcff3fcff3fcff
decd123 apply   #f7fcff3fcff3fcff       -> -9.999999999999999E+384
decd124 apply  -1.234567890123456E+384  -> #c7fd34b9c1e28e56
decd125 apply   #c7fd34b9c1e28e56       -> -1.234567890123456E+384
-- fold-downs (more below)
decd130 apply  -1.23E+384               -> #c7fd300000000000 Clamped
decd131 apply   #c7fd300000000000       -> -1.230000000000000E+384
decd132 apply  -1E+384                  -> #c7fc000000000000 Clamped
decd133 apply   #c7fc000000000000       -> -1.000000000000000E+384

-- overflows
decd151 apply  -12345                   -> #a2380000000049c5
decd152 apply   #a2380000000049c5       -> -12345
decd153 apply  -1234                    -> #a238000000000534
decd154 apply   #a238000000000534       -> -1234
decd155 apply  -123                     -> #a2380000000000a3
decd156 apply   #a2380000000000a3       -> -123
decd157 apply  -12                      -> #a238000000000012
decd158 apply   #a238000000000012       -> -12
decd159 apply  -1                       -> #a238000000000001
decd160 apply   #a238000000000001       -> -1
decd161 apply  -1.23                    -> #a2300000000000a3
decd162 apply   #a2300000000000a3       -> -1.23
decd163 apply  -123.45                  -> #a2300000000049c5
decd164 apply   #a2300000000049c5       -> -123.45

-- Nmin and below
decd171 apply  -1E-383                  -> #803c000000000001
decd172 apply   #803c000000000001       -> -1E-383
decd173 apply  -1.000000000000000E-383  -> #8400000000000000
decd174 apply   #8400000000000000       -> -1.000000000000000E-383
decd175 apply  -1.000000000000001E-383  -> #8400000000000001
decd176 apply   #8400000000000001       -> -1.000000000000001E-383

decd177 apply  -0.100000000000000E-383  -> #8000800000000000       Subnormal
decd178 apply   #8000800000000000       -> -1.00000000000000E-384  Subnormal
decd179 apply  -0.000000000000010E-383  -> #8000000000000010       Subnormal
decd180 apply   #8000000000000010       -> -1.0E-397               Subnormal
decd181 apply  -0.00000000000001E-383   -> #8004000000000001       Subnormal
decd182 apply   #8004000000000001       -> -1E-397                 Subnormal
decd183 apply  -0.000000000000001E-383  -> #8000000000000001       Subnormal
decd184 apply   #8000000000000001       -> -1E-398                 Subnormal
-- next is smallest all-nines
decd185 apply   -9999999999999999E-398   -> #e400ff3fcff3fcff
decd186 apply   #e400ff3fcff3fcff       -> -9.999999999999999E-383
-- and a tricky subnormal
decd187 apply   1.11111111111524E-384    -> #00009124491246a4      Subnormal
decd188 apply   #00009124491246a4        -> 1.11111111111524E-384  Subnormal

-- near-underflows
decd189 apply   -1e-398                 -> #8000000000000001  Subnormal
decd190 apply   -1.0e-398               -> #8000000000000001  Subnormal Rounded

-- zeros
decd401 apply   0E-500                  -> #0000000000000000  Clamped
decd402 apply   0E-400                  -> #0000000000000000  Clamped
decd403 apply   0E-398                  -> #0000000000000000
decd404 apply   #0000000000000000       -> 0E-398
decd405 apply   0.000000000000000E-383  -> #0000000000000000
decd406 apply   #0000000000000000       -> 0E-398
decd407 apply   0E-2                    -> #2230000000000000
decd408 apply   #2230000000000000       -> 0.00
decd409 apply   0                       -> #2238000000000000
decd410 apply   #2238000000000000       -> 0
decd411 apply   0E+3                    -> #2244000000000000
decd412 apply   #2244000000000000       -> 0E+3
decd413 apply   0E+369                  -> #43fc000000000000
decd414 apply   #43fc000000000000       -> 0E+369
-- clamped zeros...
decd415 apply   0E+370                  -> #43fc000000000000  Clamped
decd416 apply   #43fc000000000000       -> 0E+369
decd417 apply   0E+384                  -> #43fc000000000000  Clamped
decd418 apply   #43fc000000000000       -> 0E+369
decd419 apply   0E+400                  -> #43fc000000000000  Clamped
decd420 apply   #43fc000000000000       -> 0E+369
decd421 apply   0E+500                  -> #43fc000000000000  Clamped
decd422 apply   #43fc000000000000       -> 0E+369

-- negative zeros
decd431 apply   -0E-400                 -> #8000000000000000  Clamped
decd432 apply   -0E-400                 -> #8000000000000000  Clamped
decd433 apply   -0E-398                 -> #8000000000000000
decd434 apply   #8000000000000000       -> -0E-398
decd435 apply   -0.000000000000000E-383 -> #8000000000000000
decd436 apply   #8000000000000000       -> -0E-398
decd437 apply   -0E-2                   -> #a230000000000000
decd438 apply   #a230000000000000       -> -0.00
decd439 apply   -0                      -> #a238000000000000
decd440 apply   #a238000000000000       -> -0
decd441 apply   -0E+3                   -> #a244000000000000
decd442 apply   #a244000000000000       -> -0E+3
decd443 apply   -0E+369                 -> #c3fc000000000000
decd444 apply   #c3fc000000000000       -> -0E+369
-- clamped zeros...
decd445 apply   -0E+370                 -> #c3fc000000000000  Clamped
decd446 apply   #c3fc000000000000       -> -0E+369
decd447 apply   -0E+384                 -> #c3fc000000000000  Clamped
decd448 apply   #c3fc000000000000       -> -0E+369
decd449 apply   -0E+400                 -> #c3fc000000000000  Clamped
decd450 apply   #c3fc000000000000       -> -0E+369
decd451 apply   -0E+500                 -> #c3fc000000000000  Clamped
decd452 apply   #c3fc000000000000       -> -0E+369

-- exponents
decd460 apply   #225c000000000007 -> 7E+9
decd461 apply   7E+9  -> #225c000000000007
decd462 apply   #23c4000000000007 -> 7E+99
decd463 apply   7E+99 -> #23c4000000000007

-- Specials
decd500 apply   Infinity          -> #7800000000000000
decd501 apply   #7878787878787878 -> #7800000000000000
decd502 apply   #7800000000000000 -> Infinity
decd503 apply   #7979797979797979 -> #7800000000000000
decd504 apply   #7900000000000000 -> Infinity
decd505 apply   #7a7a7a7a7a7a7a7a -> #7800000000000000
decd506 apply   #7a00000000000000 -> Infinity
decd507 apply   #7b7b7b7b7b7b7b7b -> #7800000000000000
decd508 apply   #7b00000000000000 -> Infinity

decd509 apply   NaN               -> #7c00000000000000
decd510 apply   #7c7c7c7c7c7c7c7c -> #7c007c7c7c7c7c7c
decd511 apply   #7c00000000000000 -> NaN
decd512 apply   #7d7d7d7d7d7d7d7d -> #7c017d7d7d7d7d7d
decd513 apply   #7d00000000000000 -> NaN
decd514 apply   #7e7e7e7e7e7e7e7e -> #7e007e7e7e7e7c7e
decd515 apply   #7e00000000000000 -> sNaN
decd516 apply   #7f7f7f7f7f7f7f7f -> #7e007f7f7f7f7c7f
decd517 apply   #7f00000000000000 -> sNaN
decd518 apply   #7fffffffffffffff -> sNaN999999999999999
decd519 apply   #7fffffffffffffff -> #7e00ff3fcff3fcff

decd520 apply   -Infinity         -> #f800000000000000
decd521 apply   #f878787878787878 -> #f800000000000000
decd522 apply   #f800000000000000 -> -Infinity
decd523 apply   #f979797979797979 -> #f800000000000000
decd524 apply   #f900000000000000 -> -Infinity
decd525 apply   #fa7a7a7a7a7a7a7a -> #f800000000000000
decd526 apply   #fa00000000000000 -> -Infinity
decd527 apply   #fb7b7b7b7b7b7b7b -> #f800000000000000
decd528 apply   #fb00000000000000 -> -Infinity

decd529 apply   -NaN              -> #fc00000000000000
decd530 apply   #fc7c7c7c7c7c7c7c -> #fc007c7c7c7c7c7c
decd531 apply   #fc00000000000000 -> -NaN
decd532 apply   #fd7d7d7d7d7d7d7d -> #fc017d7d7d7d7d7d
decd533 apply   #fd00000000000000 -> -NaN
decd534 apply   #fe7e7e7e7e7e7e7e -> #fe007e7e7e7e7c7e
decd535 apply   #fe00000000000000 -> -sNaN
decd536 apply   #ff7f7f7f7f7f7f7f -> #fe007f7f7f7f7c7f
decd537 apply   #ff00000000000000 -> -sNaN
decd538 apply   #ffffffffffffffff -> -sNaN999999999999999
decd539 apply   #ffffffffffffffff -> #fe00ff3fcff3fcff

-- diagnostic NaNs
decd540 apply   NaN                 -> #7c00000000000000
decd541 apply   NaN0                -> #7c00000000000000
decd542 apply   NaN1                -> #7c00000000000001
decd543 apply   NaN12               -> #7c00000000000012
decd544 apply   NaN79               -> #7c00000000000079
decd545 apply   NaN12345            -> #7c000000000049c5
decd546 apply   NaN123456           -> #7c00000000028e56
decd547 apply   NaN799799           -> #7c000000000f7fdf
decd548 apply   NaN799799799799799  -> #7c03dff7fdff7fdf
decd549 apply   NaN999999999999999  -> #7c00ff3fcff3fcff
-- too many digits

-- fold-down full sequence
decd601 apply   1E+384                  -> #47fc000000000000 Clamped
decd602 apply   #47fc000000000000       -> 1.000000000000000E+384
decd603 apply   1E+383                  -> #43fc800000000000 Clamped
decd604 apply   #43fc800000000000       -> 1.00000000000000E+383
decd605 apply   1E+382                  -> #43fc100000000000 Clamped
decd606 apply   #43fc100000000000       -> 1.0000000000000E+382
decd607 apply   1E+381                  -> #43fc010000000000 Clamped
decd608 apply   #43fc010000000000       -> 1.000000000000E+381
decd609 apply   1E+380                  -> #43fc002000000000 Clamped
decd610 apply   #43fc002000000000       -> 1.00000000000E+380
decd611 apply   1E+379                  -> #43fc000400000000 Clamped
decd612 apply   #43fc000400000000       -> 1.0000000000E+379
decd613 apply   1E+378                  -> #43fc000040000000 Clamped
decd614 apply   #43fc000040000000       -> 1.000000000E+378
decd615 apply   1E+377                  -> #43fc000008000000 Clamped
decd616 apply   #43fc000008000000       -> 1.00000000E+377
decd617 apply   1E+376                  -> #43fc000001000000 Clamped
decd618 apply   #43fc000001000000       -> 1.0000000E+376
decd619 apply   1E+375                  -> #43fc000000100000 Clamped
decd620 apply   #43fc000000100000       -> 1.000000E+375
decd621 apply   1E+374                  -> #43fc000000020000 Clamped
decd622 apply   #43fc000000020000       -> 1.00000E+374
decd623 apply   1E+373                  -> #43fc000000004000 Clamped
decd624 apply   #43fc000000004000       -> 1.0000E+373
decd625 apply   1E+372                  -> #43fc000000000400 Clamped
decd626 apply   #43fc000000000400       -> 1.000E+372
decd627 apply   1E+371                  -> #43fc000000000080 Clamped
decd628 apply   #43fc000000000080       -> 1.00E+371
decd629 apply   1E+370                  -> #43fc000000000010 Clamped
decd630 apply   #43fc000000000010       -> 1.0E+370
decd631 apply   1E+369                  -> #43fc000000000001
decd632 apply   #43fc000000000001       -> 1E+369
decd633 apply   1E+368                  -> #43f8000000000001
decd634 apply   #43f8000000000001       -> 1E+368
-- same with 9s
decd641 apply   9E+384                  -> #77fc000000000000 Clamped
decd642 apply   #77fc000000000000       -> 9.000000000000000E+384
decd643 apply   9E+383                  -> #43fc8c0000000000 Clamped
decd644 apply   #43fc8c0000000000       -> 9.00000000000000E+383
decd645 apply   9E+382                  -> #43fc1a0000000000 Clamped
decd646 apply   #43fc1a0000000000       -> 9.0000000000000E+382
decd647 apply   9E+381                  -> #43fc090000000000 Clamped
decd648 apply   #43fc090000000000       -> 9.000000000000E+381
decd649 apply   9E+380                  -> #43fc002300000000 Clamped
decd650 apply   #43fc002300000000       -> 9.00000000000E+380
decd651 apply   9E+379                  -> #43fc000680000000 Clamped
decd652 apply   #43fc000680000000       -> 9.0000000000E+379
decd653 apply   9E+378                  -> #43fc000240000000 Clamped
decd654 apply   #43fc000240000000       -> 9.000000000E+378
decd655 apply   9E+377                  -> #43fc000008c00000 Clamped
decd656 apply   #43fc000008c00000       -> 9.00000000E+377
decd657 apply   9E+376                  -> #43fc000001a00000 Clamped
decd658 apply   #43fc000001a00000       -> 9.0000000E+376
decd659 apply   9E+375                  -> #43fc000000900000 Clamped
decd660 apply   #43fc000000900000       -> 9.000000E+375
decd661 apply   9E+374                  -> #43fc000000023000 Clamped
decd662 apply   #43fc000000023000       -> 9.00000E+374
decd663 apply   9E+373                  -> #43fc000000006800 Clamped
decd664 apply   #43fc000000006800       -> 9.0000E+373
decd665 apply   9E+372                  -> #43fc000000002400 Clamped
decd666 apply   #43fc000000002400       -> 9.000E+372
decd667 apply   9E+371                  -> #43fc00000000008c Clamped
decd668 apply   #43fc00000000008c       -> 9.00E+371
decd669 apply   9E+370                  -> #43fc00000000001a Clamped
decd670 apply   #43fc00000000001a       -> 9.0E+370
decd671 apply   9E+369                  -> #43fc000000000009
decd672 apply   #43fc000000000009       -> 9E+369
decd673 apply   9E+368                  -> #43f8000000000009
decd674 apply   #43f8000000000009       -> 9E+368


-- Selected DPD codes
decd700 apply   #2238000000000000       -> 0
decd701 apply   #2238000000000009       -> 9
decd702 apply   #2238000000000010       -> 10
decd703 apply   #2238000000000019       -> 19
decd704 apply   #2238000000000020       -> 20
decd705 apply   #2238000000000029       -> 29
decd706 apply   #2238000000000030       -> 30
decd707 apply   #2238000000000039       -> 39
decd708 apply   #2238000000000040       -> 40
decd709 apply   #2238000000000049       -> 49
decd710 apply   #2238000000000050       -> 50
decd711 apply   #2238000000000059       -> 59
decd712 apply   #2238000000000060       -> 60
decd713 apply   #2238000000000069       -> 69
decd714 apply   #2238000000000070       -> 70
decd715 apply   #2238000000000071       -> 71
decd716 apply   #2238000000000072       -> 72
decd717 apply   #2238000000000073       -> 73
decd718 apply   #2238000000000074       -> 74
decd719 apply   #2238000000000075       -> 75
decd720 apply   #2238000000000076       -> 76
decd721 apply   #2238000000000077       -> 77
decd722 apply   #2238000000000078       -> 78
decd723 apply   #2238000000000079       -> 79

decd725 apply   #223800000000029e       -> 994
decd726 apply   #223800000000029f       -> 995
decd727 apply   #22380000000002a0       -> 520
decd728 apply   #22380000000002a1       -> 521
-- from telco test data
decd730 apply   #2238000000000188       -> 308
decd731 apply   #22380000000001a3       -> 323
decd732 apply   #223800000000002a       ->  82
decd733 apply   #22380000000001a9       -> 329
decd734 apply   #2238000000000081       -> 101
decd735 apply   #22380000000002a2       -> 522

-- DPD: one of each of the huffman groups
decd740 apply   #22380000000003f7       -> 777
decd741 apply   #22380000000003f8       -> 778
decd742 apply   #22380000000003eb       -> 787
decd743 apply   #223800000000037d       -> 877
decd744 apply   #223800000000039f       -> 997
decd745 apply   #22380000000003bf       -> 979
decd746 apply   #22380000000003df       -> 799
decd747 apply   #223800000000006e       -> 888

-- DPD all-highs cases (includes the 24 redundant codes)
decd750 apply   #223800000000006e       -> 888
decd751 apply   #223800000000016e       -> 888
decd752 apply   #223800000000026e       -> 888
decd753 apply   #223800000000036e       -> 888
decd754 apply   #223800000000006f       -> 889
decd755 apply   #223800000000016f       -> 889
decd756 apply   #223800000000026f       -> 889
decd757 apply   #223800000000036f       -> 889

decd760 apply   #223800000000007e       -> 898
decd761 apply   #223800000000017e       -> 898
decd762 apply   #223800000000027e       -> 898
decd763 apply   #223800000000037e       -> 898
decd764 apply   #223800000000007f       -> 899
decd765 apply   #223800000000017f       -> 899
decd766 apply   #223800000000027f       -> 899
decd767 apply   #223800000000037f       -> 899

decd770 apply   #22380000000000ee       -> 988
decd771 apply   #22380000000001ee       -> 988
decd772 apply   #22380000000002ee       -> 988
decd773 apply   #22380000000003ee       -> 988
decd774 apply   #22380000000000ef       -> 989
decd775 apply   #22380000000001ef       -> 989
decd776 apply   #22380000000002ef       -> 989
decd777 apply   #22380000000003ef       -> 989

decd780 apply   #22380000000000fe       -> 998
decd781 apply   #22380000000001fe       -> 998
decd782 apply   #22380000000002fe       -> 998
decd783 apply   #22380000000003fe       -> 998
decd784 apply   #22380000000000ff       -> 999
decd785 apply   #22380000000001ff       -> 999
decd786 apply   #22380000000002ff       -> 999
decd787 apply   #22380000000003ff       -> 999

-- values around [u]int32 edges (zeros done earlier)
decd800 apply -2147483646  -> #a23800008c78af46
decd801 apply -2147483647  -> #a23800008c78af47
decd802 apply -2147483648  -> #a23800008c78af48
decd803 apply -2147483649  -> #a23800008c78af49
decd804 apply  2147483646  -> #223800008c78af46
decd805 apply  2147483647  -> #223800008c78af47
decd806 apply  2147483648  -> #223800008c78af48
decd807 apply  2147483649  -> #223800008c78af49
decd808 apply  4294967294  -> #2238000115afb55a
decd809 apply  4294967295  -> #2238000115afb55b
decd810 apply  4294967296  -> #2238000115afb57a
decd811 apply  4294967297  -> #2238000115afb57b

decd820 apply  #a23800008c78af46 -> -2147483646
decd821 apply  #a23800008c78af47 -> -2147483647
decd822 apply  #a23800008c78af48 -> -2147483648
decd823 apply  #a23800008c78af49 -> -2147483649
decd824 apply  #223800008c78af46 ->  2147483646
decd825 apply  #223800008c78af47 ->  2147483647
decd826 apply  #223800008c78af48 ->  2147483648
decd827 apply  #223800008c78af49 ->  2147483649
decd828 apply  #2238000115afb55a ->  4294967294
decd829 apply  #2238000115afb55b ->  4294967295
decd830 apply  #2238000115afb57a ->  4294967296
decd831 apply  #2238000115afb57b ->  4294967297

-- for narrowing
decd840 apply  #2870000000000000 ->  2.000000000000000E-99

-- some miscellaneous
decd850 apply  #0004070000000000 -> 7.000000000000E-385  Subnormal
decd851 apply  #0008000000020000 -> 1.00000E-391         Subnormal

