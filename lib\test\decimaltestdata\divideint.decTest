------------------------------------------------------------------------
-- divideint.decTest -- decimal integer division                      --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 384
minexponent: -383

dvix001 divideint  1     1    ->  1
dvix002 divideint  2     1    ->  2
dvix003 divideint  1     2    ->  0
dvix004 divideint  2     2    ->  1
dvix005 divideint  0     1    ->  0
dvix006 divideint  0     2    ->  0
dvix007 divideint  1     3    ->  0
dvix008 divideint  2     3    ->  0
dvix009 divideint  3     3    ->  1

dvix010 divideint  2.4   1    ->  2
dvix011 divideint  2.4   -1   ->  -2
dvix012 divideint  -2.4  1    ->  -2
dvix013 divideint  -2.4  -1   ->  2
dvix014 divideint  2.40  1    ->  2
dvix015 divideint  2.400 1    ->  2
dvix016 divideint  2.4   2    ->  1
dvix017 divideint  2.400 2    ->  1
dvix018 divideint  2.    2    ->  1
dvix019 divideint  20    20   ->  1

dvix020 divideint  187   187  ->  1
dvix021 divideint  5     2    ->  2
dvix022 divideint  5     2.0    ->  2
dvix023 divideint  5     2.000  ->  2
dvix024 divideint  5     0.200  ->  25
dvix025 divideint  5     0.200  ->  25

dvix030 divideint  1     2      ->  0
dvix031 divideint  1     4      ->  0
dvix032 divideint  1     8      ->  0
dvix033 divideint  1     16     ->  0
dvix034 divideint  1     32     ->  0
dvix035 divideint  1     64     ->  0
dvix040 divideint  1    -2      -> -0
dvix041 divideint  1    -4      -> -0
dvix042 divideint  1    -8      -> -0
dvix043 divideint  1    -16     -> -0
dvix044 divideint  1    -32     -> -0
dvix045 divideint  1    -64     -> -0
dvix050 divideint -1     2      -> -0
dvix051 divideint -1     4      -> -0
dvix052 divideint -1     8      -> -0
dvix053 divideint -1     16     -> -0
dvix054 divideint -1     32     -> -0
dvix055 divideint -1     64     -> -0
dvix060 divideint -1    -2      ->  0
dvix061 divideint -1    -4      ->  0
dvix062 divideint -1    -8      ->  0
dvix063 divideint -1    -16     ->  0
dvix064 divideint -1    -32     ->  0
dvix065 divideint -1    -64     ->  0

-- similar with powers of ten
dvix160 divideint  1     1         ->  1
dvix161 divideint  1     10        ->  0
dvix162 divideint  1     100       ->  0
dvix163 divideint  1     1000      ->  0
dvix164 divideint  1     10000     ->  0
dvix165 divideint  1     100000    ->  0
dvix166 divideint  1     1000000   ->  0
dvix167 divideint  1     10000000  ->  0
dvix168 divideint  1     100000000 ->  0
dvix170 divideint  1    -1         -> -1
dvix171 divideint  1    -10        -> -0
dvix172 divideint  1    -100       -> -0
dvix173 divideint  1    -1000      -> -0
dvix174 divideint  1    -10000     -> -0
dvix175 divideint  1    -100000    -> -0
dvix176 divideint  1    -1000000   -> -0
dvix177 divideint  1    -10000000  -> -0
dvix178 divideint  1    -100000000 -> -0
dvix180 divideint -1     1         -> -1
dvix181 divideint -1     10        -> -0
dvix182 divideint -1     100       -> -0
dvix183 divideint -1     1000      -> -0
dvix184 divideint -1     10000     -> -0
dvix185 divideint -1     100000    -> -0
dvix186 divideint -1     1000000   -> -0
dvix187 divideint -1     10000000  -> -0
dvix188 divideint -1     100000000 -> -0
dvix190 divideint -1    -1         ->  1
dvix191 divideint -1    -10        ->  0
dvix192 divideint -1    -100       ->  0
dvix193 divideint -1    -1000      ->  0
dvix194 divideint -1    -10000     ->  0
dvix195 divideint -1    -100000    ->  0
dvix196 divideint -1    -1000000   ->  0
dvix197 divideint -1    -10000000  ->  0
dvix198 divideint -1    -100000000 ->  0

-- some long operand cases here
dvix070 divideint  999999999     1  ->  999999999
dvix071 divideint  999999999.4   1  ->  999999999
dvix072 divideint  999999999.5   1  ->  999999999
dvix073 divideint  999999999.9   1  ->  999999999
dvix074 divideint  999999999.999 1  ->  999999999
precision: 6
dvix080 divideint  999999999     1  ->  NaN Division_impossible
dvix081 divideint  99999999      1  ->  NaN Division_impossible
dvix082 divideint  9999999       1  ->  NaN Division_impossible
dvix083 divideint  999999        1  ->  999999
dvix084 divideint  99999         1  ->  99999
dvix085 divideint  9999          1  ->  9999
dvix086 divideint  999           1  ->  999
dvix087 divideint  99            1  ->  99
dvix088 divideint  9             1  ->  9

precision: 9
dvix090 divideint  0.            1    ->  0
dvix091 divideint  .0            1    ->  0
dvix092 divideint  0.00          1    ->  0
dvix093 divideint  0.00E+9       1    ->  0
dvix094 divideint  0.0000E-50    1    ->  0

dvix100 divideint  1  1   -> 1
dvix101 divideint  1  2   -> 0
dvix102 divideint  1  3   -> 0
dvix103 divideint  1  4   -> 0
dvix104 divideint  1  5   -> 0
dvix105 divideint  1  6   -> 0
dvix106 divideint  1  7   -> 0
dvix107 divideint  1  8   -> 0
dvix108 divideint  1  9   -> 0
dvix109 divideint  1  10  -> 0
dvix110 divideint  1  1   -> 1
dvix111 divideint  2  1   -> 2
dvix112 divideint  3  1   -> 3
dvix113 divideint  4  1   -> 4
dvix114 divideint  5  1   -> 5
dvix115 divideint  6  1   -> 6
dvix116 divideint  7  1   -> 7
dvix117 divideint  8  1   -> 8
dvix118 divideint  9  1   -> 9
dvix119 divideint  10 1   -> 10

-- from DiagBigDecimal
dvix131 divideint  101.3   1     ->  101
dvix132 divideint  101.0   1     ->  101
dvix133 divideint  101.3   3     ->  33
dvix134 divideint  101.0   3     ->  33
dvix135 divideint  2.4     1     ->  2
dvix136 divideint  2.400   1     ->  2
dvix137 divideint  18      18    ->  1
dvix138 divideint  1120    1000  ->  1
dvix139 divideint  2.4     2     ->  1
dvix140 divideint  2.400   2     ->  1
dvix141 divideint  0.5     2.000 ->  0
dvix142 divideint  8.005   7     ->  1
dvix143 divideint  5       2     ->  2
dvix144 divideint  0       2     ->  0
dvix145 divideint  0.00    2     ->  0

-- Others
dvix150 divideint  12345  4.999  ->  2469
dvix151 divideint  12345  4.99   ->  2473
dvix152 divideint  12345  4.9    ->  2519
dvix153 divideint  12345  5      ->  2469
dvix154 divideint  12345  5.1    ->  2420
dvix155 divideint  12345  5.01   ->  2464
dvix156 divideint  12345  5.001  ->  2468
dvix157 divideint    101  7.6    ->  13

-- Various flavours of divideint by 0
maxexponent: 999999999
minexponent: -999999999
dvix201 divideint  0      0   -> NaN Division_undefined
dvix202 divideint  0.0E5  0   -> NaN Division_undefined
dvix203 divideint  0.000  0   -> NaN Division_undefined
dvix204 divideint  0.0001 0   -> Infinity Division_by_zero
dvix205 divideint  0.01   0   -> Infinity Division_by_zero
dvix206 divideint  0.1    0   -> Infinity Division_by_zero
dvix207 divideint  1      0   -> Infinity Division_by_zero
dvix208 divideint  1      0.0 -> Infinity Division_by_zero
dvix209 divideint 10      0.0 -> Infinity Division_by_zero
dvix210 divideint 1E+100  0.0 -> Infinity Division_by_zero
dvix211 divideint 1E+1000 0   -> Infinity Division_by_zero
dvix214 divideint  -0.0001 0   -> -Infinity Division_by_zero
dvix215 divideint  -0.01   0   -> -Infinity Division_by_zero
dvix216 divideint  -0.1    0   -> -Infinity Division_by_zero
dvix217 divideint  -1      0   -> -Infinity Division_by_zero
dvix218 divideint  -1      0.0 -> -Infinity Division_by_zero
dvix219 divideint -10      0.0 -> -Infinity Division_by_zero
dvix220 divideint -1E+100  0.0 -> -Infinity Division_by_zero
dvix221 divideint -1E+1000 0   -> -Infinity Division_by_zero

-- test some cases that are close to exponent overflow
maxexponent: 999999999
minexponent: -999999999
dvix270 divideint 1 1e999999999    -> 0
dvix271 divideint 1 0.9e999999999  -> 0
dvix272 divideint 1 0.99e999999999 -> 0
dvix273 divideint 1 0.999999999e999999999 -> 0
dvix274 divideint 9e999999999    1       -> NaN Division_impossible
dvix275 divideint 9.9e999999999  1       -> NaN Division_impossible
dvix276 divideint 9.99e999999999 1       -> NaN Division_impossible
dvix277 divideint 9.99999999e999999999 1 -> NaN Division_impossible

dvix280 divideint 0.1 9e-999999999       -> NaN Division_impossible
dvix281 divideint 0.1 99e-999999999      -> NaN Division_impossible
dvix282 divideint 0.1 999e-999999999     -> NaN Division_impossible

dvix283 divideint 0.1 9e-999999998       -> NaN Division_impossible
dvix284 divideint 0.1 99e-999999998      -> NaN Division_impossible
dvix285 divideint 0.1 999e-999999998     -> NaN Division_impossible
dvix286 divideint 0.1 999e-999999997     -> NaN Division_impossible
dvix287 divideint 0.1 9999e-999999997    -> NaN Division_impossible
dvix288 divideint 0.1 99999e-999999997   -> NaN Division_impossible

-- GD edge cases: lhs smaller than rhs but more digits
dvix301  divideint  0.9      2      ->  0
dvix302  divideint  0.9      2.0    ->  0
dvix303  divideint  0.9      2.1    ->  0
dvix304  divideint  0.9      2.00   ->  0
dvix305  divideint  0.9      2.01   ->  0
dvix306  divideint  0.12     1      ->  0
dvix307  divideint  0.12     1.0    ->  0
dvix308  divideint  0.12     1.00   ->  0
dvix309  divideint  0.12     1.0    ->  0
dvix310  divideint  0.12     1.00   ->  0
dvix311  divideint  0.12     2      ->  0
dvix312  divideint  0.12     2.0    ->  0
dvix313  divideint  0.12     2.1    ->  0
dvix314  divideint  0.12     2.00   ->  0
dvix315  divideint  0.12     2.01   ->  0

-- overflow and underflow tests [from divide]
maxexponent: 999999999
minexponent: -999999999
dvix330 divideint +1.23456789012345E-0 9E+999999999    -> 0
dvix331 divideint 9E+999999999 +0.23456789012345E-0 -> NaN Division_impossible
dvix332 divideint +0.100 9E+999999999    -> 0
dvix333 divideint 9E-999999999 +9.100    -> 0
dvix335 divideint -1.23456789012345E-0 9E+999999999    -> -0
dvix336 divideint 9E+999999999 -0.83456789012345E-0 -> NaN Division_impossible
dvix337 divideint -0.100 9E+999999999    -> -0
dvix338 divideint 9E-999999999 -9.100    -> -0

-- long operand checks
maxexponent: 999
minexponent: -999
precision: 9
dvix401 divideint 12345678000 100 -> 123456780
dvix402 divideint 1 12345678000   -> 0
dvix403 divideint 1234567800  10  -> 123456780
dvix404 divideint 1 1234567800    -> 0
dvix405 divideint 1234567890  10  -> 123456789
dvix406 divideint 1 1234567890    -> 0
dvix407 divideint 1234567891  10  -> 123456789
dvix408 divideint 1 1234567891    -> 0
dvix409 divideint 12345678901 100 -> 123456789
dvix410 divideint 1 12345678901   -> 0
dvix411 divideint 1234567896  10  -> 123456789
dvix412 divideint 1 1234567896    -> 0
dvix413 divideint 12345678948 100 -> 123456789
dvix414 divideint 12345678949 100 -> 123456789
dvix415 divideint 12345678950 100 -> 123456789
dvix416 divideint 12345678951 100 -> 123456789
dvix417 divideint 12345678999 100 -> 123456789

precision: 15
dvix441 divideint 12345678000 1 -> 12345678000
dvix442 divideint 1 12345678000 -> 0
dvix443 divideint 1234567800  1 -> 1234567800
dvix444 divideint 1 1234567800  -> 0
dvix445 divideint 1234567890  1 -> 1234567890
dvix446 divideint 1 1234567890  -> 0
dvix447 divideint 1234567891  1 -> 1234567891
dvix448 divideint 1 1234567891  -> 0
dvix449 divideint 12345678901 1 -> 12345678901
dvix450 divideint 1 12345678901 -> 0
dvix451 divideint 1234567896  1 -> 1234567896
dvix452 divideint 1 1234567896  -> 0

precision:   9
rounding:    half_up
maxExponent: 999
minexponent: -999

-- more zeros, etc.
dvix531 divideint 5.00 1E-3    -> 5000
dvix532 divideint 00.00 0.000  -> NaN Division_undefined
dvix533 divideint 00.00 0E-3   -> NaN Division_undefined
dvix534 divideint  0    -0     -> NaN Division_undefined
dvix535 divideint -0     0     -> NaN Division_undefined
dvix536 divideint -0    -0     -> NaN Division_undefined

dvix541 divideint  0    -1     -> -0
dvix542 divideint -0    -1     ->  0
dvix543 divideint  0     1     ->  0
dvix544 divideint -0     1     -> -0
dvix545 divideint -1     0     -> -Infinity Division_by_zero
dvix546 divideint -1    -0     ->  Infinity Division_by_zero
dvix547 divideint  1     0     ->  Infinity Division_by_zero
dvix548 divideint  1    -0     -> -Infinity Division_by_zero

dvix551 divideint  0.0  -1     -> -0
dvix552 divideint -0.0  -1     ->  0
dvix553 divideint  0.0   1     ->  0
dvix554 divideint -0.0   1     -> -0
dvix555 divideint -1.0   0     -> -Infinity Division_by_zero
dvix556 divideint -1.0  -0     ->  Infinity Division_by_zero
dvix557 divideint  1.0   0     ->  Infinity Division_by_zero
dvix558 divideint  1.0  -0     -> -Infinity Division_by_zero

dvix561 divideint  0    -1.0   -> -0
dvix562 divideint -0    -1.0   ->  0
dvix563 divideint  0     1.0   ->  0
dvix564 divideint -0     1.0   -> -0
dvix565 divideint -1     0.0   -> -Infinity Division_by_zero
dvix566 divideint -1    -0.0   ->  Infinity Division_by_zero
dvix567 divideint  1     0.0   ->  Infinity Division_by_zero
dvix568 divideint  1    -0.0   -> -Infinity Division_by_zero

dvix571 divideint  0.0  -1.0   -> -0
dvix572 divideint -0.0  -1.0   ->  0
dvix573 divideint  0.0   1.0   ->  0
dvix574 divideint -0.0   1.0   -> -0
dvix575 divideint -1.0   0.0   -> -Infinity Division_by_zero
dvix576 divideint -1.0  -0.0   ->  Infinity Division_by_zero
dvix577 divideint  1.0   0.0   ->  Infinity Division_by_zero
dvix578 divideint  1.0  -0.0   -> -Infinity Division_by_zero

-- Specials
dvix580 divideint  Inf  -Inf   ->  NaN Invalid_operation
dvix581 divideint  Inf  -1000  -> -Infinity
dvix582 divideint  Inf  -1     -> -Infinity
dvix583 divideint  Inf  -0     -> -Infinity
dvix584 divideint  Inf   0     ->  Infinity
dvix585 divideint  Inf   1     ->  Infinity
dvix586 divideint  Inf   1000  ->  Infinity
dvix587 divideint  Inf   Inf   ->  NaN Invalid_operation
dvix588 divideint -1000  Inf   -> -0
dvix589 divideint -Inf   Inf   ->  NaN Invalid_operation
dvix590 divideint -1     Inf   -> -0
dvix591 divideint -0     Inf   -> -0
dvix592 divideint  0     Inf   ->  0
dvix593 divideint  1     Inf   ->  0
dvix594 divideint  1000  Inf   ->  0
dvix595 divideint  Inf   Inf   ->  NaN Invalid_operation

dvix600 divideint -Inf  -Inf   ->  NaN Invalid_operation
dvix601 divideint -Inf  -1000  ->  Infinity
dvix602 divideint -Inf  -1     ->  Infinity
dvix603 divideint -Inf  -0     ->  Infinity
dvix604 divideint -Inf   0     -> -Infinity
dvix605 divideint -Inf   1     -> -Infinity
dvix606 divideint -Inf   1000  -> -Infinity
dvix607 divideint -Inf   Inf   ->  NaN Invalid_operation
dvix608 divideint -1000  Inf   -> -0
dvix609 divideint -Inf  -Inf   ->  NaN Invalid_operation
dvix610 divideint -1    -Inf   ->  0
dvix611 divideint -0    -Inf   ->  0
dvix612 divideint  0    -Inf   -> -0
dvix613 divideint  1    -Inf   -> -0
dvix614 divideint  1000 -Inf   -> -0
dvix615 divideint  Inf  -Inf   ->  NaN Invalid_operation

dvix621 divideint  NaN -Inf    ->  NaN
dvix622 divideint  NaN -1000   ->  NaN
dvix623 divideint  NaN -1      ->  NaN
dvix624 divideint  NaN -0      ->  NaN
dvix625 divideint  NaN  0      ->  NaN
dvix626 divideint  NaN  1      ->  NaN
dvix627 divideint  NaN  1000   ->  NaN
dvix628 divideint  NaN  Inf    ->  NaN
dvix629 divideint  NaN  NaN    ->  NaN
dvix630 divideint -Inf  NaN    ->  NaN
dvix631 divideint -1000 NaN    ->  NaN
dvix632 divideint -1    NaN    ->  NaN
dvix633 divideint -0    NaN    ->  NaN
dvix634 divideint  0    NaN    ->  NaN
dvix635 divideint  1    NaN    ->  NaN
dvix636 divideint  1000 NaN    ->  NaN
dvix637 divideint  Inf  NaN    ->  NaN

dvix641 divideint  sNaN -Inf   ->  NaN  Invalid_operation
dvix642 divideint  sNaN -1000  ->  NaN  Invalid_operation
dvix643 divideint  sNaN -1     ->  NaN  Invalid_operation
dvix644 divideint  sNaN -0     ->  NaN  Invalid_operation
dvix645 divideint  sNaN  0     ->  NaN  Invalid_operation
dvix646 divideint  sNaN  1     ->  NaN  Invalid_operation
dvix647 divideint  sNaN  1000  ->  NaN  Invalid_operation
dvix648 divideint  sNaN  NaN   ->  NaN  Invalid_operation
dvix649 divideint  sNaN sNaN   ->  NaN  Invalid_operation
dvix650 divideint  NaN  sNaN   ->  NaN  Invalid_operation
dvix651 divideint -Inf  sNaN   ->  NaN  Invalid_operation
dvix652 divideint -1000 sNaN   ->  NaN  Invalid_operation
dvix653 divideint -1    sNaN   ->  NaN  Invalid_operation
dvix654 divideint -0    sNaN   ->  NaN  Invalid_operation
dvix655 divideint  0    sNaN   ->  NaN  Invalid_operation
dvix656 divideint  1    sNaN   ->  NaN  Invalid_operation
dvix657 divideint  1000 sNaN   ->  NaN  Invalid_operation
dvix658 divideint  Inf  sNaN   ->  NaN  Invalid_operation
dvix659 divideint  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dvix661 divideint  NaN9 -Inf   ->  NaN9
dvix662 divideint  NaN8  1000  ->  NaN8
dvix663 divideint  NaN7  Inf   ->  NaN7
dvix664 divideint -NaN6  NaN5  -> -NaN6
dvix665 divideint -Inf   NaN4  ->  NaN4
dvix666 divideint -1000  NaN3  ->  NaN3
dvix667 divideint  Inf  -NaN2  -> -NaN2

dvix671 divideint -sNaN99 -Inf    -> -NaN99 Invalid_operation
dvix672 divideint  sNaN98 -1      ->  NaN98 Invalid_operation
dvix673 divideint  sNaN97  NaN    ->  NaN97 Invalid_operation
dvix674 divideint  sNaN96 sNaN94  ->  NaN96 Invalid_operation
dvix675 divideint  NaN95  sNaN93  ->  NaN93 Invalid_operation
dvix676 divideint -Inf    sNaN92  ->  NaN92 Invalid_operation
dvix677 divideint  0      sNaN91  ->  NaN91 Invalid_operation
dvix678 divideint  Inf   -sNaN90  -> -NaN90 Invalid_operation
dvix679 divideint  NaN    sNaN89  ->  NaN89 Invalid_operation

-- some long operand cases again
precision: 8
dvix710 divideint  100000001     1  ->  NaN Division_impossible
dvix711 divideint  100000000.4   1  ->  NaN Division_impossible
dvix712 divideint  100000000.5   1  ->  NaN Division_impossible
dvix713 divideint  100000000.9   1  ->  NaN Division_impossible
dvix714 divideint  100000000.999 1  ->  NaN Division_impossible
precision: 6
dvix720 divideint  100000000     1  ->  NaN Division_impossible
dvix721 divideint  10000000      1  ->  NaN Division_impossible
dvix722 divideint  1000000       1  ->  NaN Division_impossible
dvix723 divideint  100000        1  ->  100000
dvix724 divideint  10000         1  ->  10000
dvix725 divideint  1000          1  ->  1000
dvix726 divideint  100           1  ->  100
dvix727 divideint  10            1  ->  10
dvix728 divideint  1             1  ->  1
dvix729 divideint  1            10  ->  0

precision: 9
maxexponent: 999999999
minexponent: -999999999
dvix732 divideint 1 0.99e999999999 -> 0
dvix733 divideint 1 0.999999999e999999999 -> 0
dvix734 divideint 9e999999999    1       -> NaN Division_impossible
dvix735 divideint 9.9e999999999  1       -> NaN Division_impossible
dvix736 divideint 9.99e999999999 1       -> NaN Division_impossible
dvix737 divideint 9.99999999e999999999 1 -> NaN Division_impossible

dvix740 divideint 0.1 9e-999999999       -> NaN Division_impossible
dvix741 divideint 0.1 99e-999999999      -> NaN Division_impossible
dvix742 divideint 0.1 999e-999999999     -> NaN Division_impossible

dvix743 divideint 0.1 9e-999999998       -> NaN Division_impossible
dvix744 divideint 0.1 99e-999999998      -> NaN Division_impossible
dvix745 divideint 0.1 999e-999999998     -> NaN Division_impossible
dvix746 divideint 0.1 999e-999999997     -> NaN Division_impossible
dvix747 divideint 0.1 9999e-999999997    -> NaN Division_impossible
dvix748 divideint 0.1 99999e-999999997   -> NaN Division_impossible


-- Null tests
dvix900 divideint  10  # -> NaN Invalid_operation
dvix901 divideint   # 10 -> NaN Invalid_operation
