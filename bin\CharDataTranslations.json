{"subActionTranslation": {"WallDamage": "Hit a Wall in Hitstun", "Wait1": "Idle Animation 1", "Wait2": "Idle Animation 2", "Wait3": "Idle Animation 3", "Wait4": "Idle Animation 4", "WalkSlow": "Slow Walk", "WalkMiddle": "Normal Walk", "WalkFast": "Fast Walk", "JumpF": "Jump Up/Forward", "JumpB": "Jump Backwards", "JumpAerialF": "Double Jump Up/Forward", "JumpAerialB": "Double Jump Backwards", "Fall": "Fall Strait Down", "FallF": "Fall With DI-Forward", "FallB": "Fall With DI-Backwards", "FallAerial": "Fall Without Jumps Remaining", "FallAerialF": "Fall DI-Forward w/out Jumps", "FallAerialB": "Fall DI-Backwards w/out Jumps", "Squat": "Start Crouching", "SquatWait": "Crouching Idle", "SquatWaitItem": "Crouching Idle w/Item", "SquatRv": "Stand-up from <PERSON><PERSON>", "EscapeN": "Spot Dodge", "EscapeF": "Roll Forward", "EscapeB": "Roll Backward", "EscapeAir": "Air Dodge", "Attack11": "Jab 1", "Attack12": "Jab 2", "Attack13": "Jab 3", "Attack100Start": "Rapid Jab Startup", "Attack100Loop": "Rapid Jab Loop", "Attack100End": "Rapid Jab Cooldown", "AttackDash": "Dash Attack", "AttackS3Hi": "Forward Tilt, Angled High", "AttackS3HiS": "Forward Tilt, Angled High-Mid", "AttackS3S": "Forward Tilt", "AttackS3LwS": "Forward Tilt, Angled Low-Mid", "AttackS3Lw": "Forward Tilt, Angled Low", "AttackHi3": "Up Tilt", "AttackLw3": "Down Tilt", "AttackS4Hi": "Forward Smash, Angled High", "AttackS4HiS": "Forward Smash, Angled High-Mid", "AttackS4": "Forward Smash", "AttackS4S": "Forward Smash", "AttackS41": "Forward Smash, Hit 1", "AttackS42": "Forward Smash, Hit 2", "AttackS4LwS": "Forward Smash, Angled Low-Mid", "AttackS4Lw": "Forward Smash, <PERSON><PERSON> Low", "AttackHi4": "Up Smash", "AttackLw4": "Down Smash", "AttackAirN": "Neutral Air", "AttackAirF": "Forward Air", "AttackAirB": "Back Air", "AttackAirHi": "Up Air", "AttackAirLw": "Down Air", "HeavyGet": "Pick up Heavy Item", "HeavyWalk1": "Walk with Heavy Item 1", "HeavyWalk2": "Walk with Heavy Item 2", "HeavyThrowF": "Throw Heavy Item Forward", "HeavyThrowB": "Throw Heavy Item Back", "HeavyThrowHi": "Throw Heavy Item Up", "HeavyThrowLw": "Throw Heavy Item Down", "Swing1": "Basic Swing", "Swing3": "Tilt Swing", "Swing4": "Smash Swing", "Swing41": "Smash Swing, Hit 1", "Swing42": "Smash Swing, Hit 2", "SwingDash": "Dash Attack Swing", "Passive": "Tech", "StopWall": "Run into Wall", "StopCeil": "Bumped into <PERSON><PERSON>", "FuraFura": "Sleep", "FuraSleepStart": "Start Sleeping", "FuraSleepLoop": "Sleep Loop", "FuraSleepEnd": "End Sleeping", "Pass": "Drop through Platform", "Ottotto": "Ledge Teeter", "MissFoot": "Slip off Platform", "CliffCatch": "Catch the Ledge", "CliffWait": "Idle Hanging on Ledge", "CliffClimbSlow": "Ledge Get-up, >=100%", "CliffClimbQuick": "Ledge Get-up, <100%", "CliffAttackSlow": "Ledge Attack, >=100%", "CliffAttackQuick": "Ledge Attack, <100%", "CliffEscapeSlow": "Ledge Roll, >=100%", "CliffEscapeQuick": "Ledge Roll, <100%", "Entry": "Warp in at Beginning of Match", "AppealR": "Taunt, Facing Right", "AppealL": "Taunt, Facing Left", "Catch": "<PERSON>rab", "CatchDash": "Dash Grab", "CatchWait": "Idle while Grabbing", "CatchAttack": "<PERSON><PERSON>", "CatchCut": "Lost Grabbed Opponent", "ThrowF": "Throw Forward", "ThrowB": "Throw Backward", "ThrowHi": "Throw Up", "ThrowLw": "Throw Down", "CapturePulledHi": "Becoming Grabbed", "CaptureWaitHi": "Idle while Grabbed", "CaptureDamageHi": "Hit while Grabbed", "CapturePulledLw": "Becoming Grabbed", "CaptureWaitLw": "Idle while Grabbed", "CaptureDamageLw": "Hit while Grabbed", "CaptureCut": "Broke out of Grab", "CaptureJump": "Broke out of Grab into Air", "Rebirth": "Starting on Respawn Platform", "RebirthWait": "Idle on Respawn Platform"}, "eventNotes": {"0x01": "Synchronous Timer|Pauses the current flow of events until the set frame is reached. Synchronous timers count down when they are reached in the events list.", "0x02": "Asynchronous Timer|Pauses the current flow of events until the set frame is reached. Asynchronous Timers start counting from the beginning of the animation.", "0x03": "Set Loop|Set loop for X iterations.", "0x04": "Execute Loop|Executes the previously set loop.", "0x05": "Subroutine|Enter the event routine specified and return after ending.", "0x06": "Return|Returns to a previous 'GoTo' event.", "0x07": "GoTo|Goto the event location specified and execute.", "0x0B": ["Hitbox|Notes on Hitbox Interaction:", "\n\n  0 - Does not hit any hitboxes?", "\n  1 - Possibly able to hit something that 0 can't?", "\n  2 - Able to hit any hitboxes while on the ground, and projectiles on ground or in air.", "\n  3 - Able to hit any hitboxes while on the ground, and projectiles on ground or in air. Will go into the rebound animation if it collides with a hitbox thats current damage is less than 9 stronger or weaker than its own damage."], "0x12": "Random Smash SFX|Generates a random smash sound effect from a pre-defined table of specific sounds.", "0x13": "Auto-cancel|Toggle the capability for this move to be (or not be) auto-canceled from this point on. 0 = on, 1 = off.", "0x14": "Reverse Direction|Reverse the direction the character is facing. Also seems to have something to do with throws/item tosses, as well as noting breakpoints for rapid jabs.", "0x17": "Allow Interrupt|Allow the current action to be interrupted by another action from this point on. Essentially this is what allows for IASA frames.", "0x1A": ["Set Body Collision State|Controls whether or not the character's body is interactable.", "\n\n0 = normal, 1 = Invulnerable, 2 = Intangible"], "0x1F": "Changle Model State|Updates the visibility of a model part to show or hide it.", "0x22": ["Throw|This can be configured to either be a throw or a grab release.", "\n\nNotes on SFX Severity:", "\n\n  0 - No sound?", "\n  1 - <PERSON><PERSON>", "\n  2 - Moderate", "\n  3 - <PERSON>"], "0x33": "Self Damage|Inflicts damage to yourself, like <PERSON><PERSON>.", "0x34": "Continuation Control|0 = earliest next, 1 = ?, 3 = open continuation window?", "0x38": "Start Smash Charge|Start charging a smash attack on this frame if the Attack button is still being held. Effects on the character while charging can also be altered."}, "specialAttributes": {"Kp": [["-1", "character", "<PERSON>ser", ""], ["0x0", "f", "Super Armor|Units of knockback reduction", "This value gets subtracted from knockback taken and applies no knockback if final value is less than or equal to 0. Presumably always active?"], ["0x4", "I", "Fire Breath|Frame delay between cycles", "The game checks if B is held or not every this many frames"], ["0x8", "f", "Fire Breath|Fuel regen rate", "Only recharges outside of Flame Breath usage"], ["0xC", "f", "Fire Breath|Fire size regen rate", "Only recharges outside of Flame Breath usage"], ["0x10", "f", "Fire Breath|Maximum amount of fuel", "Also affects spacing between graphics"], ["0x14", "f", "Fire Breath|Spew velocity of flames", ""], ["0x18", "f", "Fire Breath|Graphics scaling", "Smaller values result in larger graphics"], ["0x1C", "f", "Fire Breath|Flame graphic size during lowest fuel levels", ""], ["0x20", "I", "Fire Breath|Frequency of screen shake effect", "In frames"], ["0x24", "f", "Fire Breath|Fire X offset", ""], ["0x28", "f", "Fire Breath|Fire Y offset", ""], ["0x2C", "I", "<PERSON><PERSON><PERSON> Klaw|Damage of subsequent bites", ""], ["0x30", "f", "Ko<PERSON><PERSON> Klaw|Control stick X-axis range to register throw input", "Throw stick sensitivity"], ["0x34", "f", "<PERSON><PERSON><PERSON>|Maximum X-axis wiggle of held opponent", ""], ["0x38", "f", "<PERSON><PERSON><PERSON>|Maximum y-axis wiggle of held opponent", ""], ["0x3C", "f", "<PERSON><PERSON><PERSON>|Maximum Z-axis wiggle of held opponent?", ""], ["0x40", "f", "Ko<PERSON><PERSON> Klaw|Input frame reduction?", ""], ["0x44", "f", "<PERSON><PERSON><PERSON> Klaw|Victim breakout speed", "Duration divisor"], ["0x48", "f", "<PERSON><PERSON><PERSON>|Affects breakout speed?", ""], ["0x4C", "f", "Grab duration?", ""], ["0x50", "f", "Unknown 0x50", ""], ["0x54", "f", "Whirling Fortress|Aerial vertical momentum", ""], ["0x58", "f", "Whirling Fortress|Gravity", ""], ["0x5C", "f", "Whirling Fortress|Minimum gravity", "Negative values make <PERSON><PERSON> rise and vice versa"], ["0x60", "f", "Whirling Fortress|Grounded movement speed multiplier", ""], ["0x64", "f", "Whirling Fortress|Turning momentum preservation", "Grounded and aerial versions are inversely affected; i.e. higher values make grounded turning easier but aerial turning becomes more difficult"], ["0x68", "f", "Whirling Fortress|Grounded turning speed", ""], ["0x6C", "f", "Whirling Fortress|Aerial mobility", ""], ["0x70", "f", "Whirling Fortress|Lag Before Next", "Number of frames before this move can be used again"], ["0x74", "f", "Whirling Fortress|Duration in Shell", "Related to 'Lag Before Next?'"], ["0x78", "f", "Whirling Fortress|Unknown 0x78", "Related to 'Lag Before Next?'"], ["0x7C", "f", "Whirling Fortress|Landing lag / Freefall toggle", ""], ["0x80", "f", "Bowser Bomb|Aerial horizontal momentum multiplier", ""], ["0x84", "f", "Bowser Bomb|Initial aerial vertical momentum", ""], ["0x88", "f", "Bowser Bomb|Horizontal momentum preservation", "Do not set to 0"], ["0x8C", "f", "Bowser Bomb|Vertical momentum deceleration rate?", "Negative values make aerial version skyrocket; grounded version affected after initial jump"], ["0x90", "f", "Bowser Bomb|Gravity", ""], ["0x94", "f", "Bowser Bomb|Descent speed", "Positive values send you upwards"], ["0x98", "f", "Unknown|Unknown 0x98", ""], ["0x9C", "f", "Unknown|Unknown 0x9C", ""]], "Ca": [["-1", "character", "<PERSON><PERSON>", ""], ["0x0", "f", "Falcon Punch|Stick Y-Axis Downward Angle Range", "Minimum control stick Y-Axis range required for downward variant"], ["0x4", "f", "Falcon Punch|Stick Y-Axis Upward Angle Range", "Maximum control stick Y-Axis range required for upward variant"], ["0x8", "f", "Falcon Punch|Angle Difference", "Angle difference for angled aerial Falcon Punch"], ["0xC", "f", "Falcon Punch|Horizontal Momentum", ""], ["0x10", "f", "Falcon Punch|Vertical Momentum", "Additional vertical momentum for angled aerial Falcon Punch"], ["0x14", "f", "<PERSON><PERSON>|Gravity on Hit?", "Gravity of successful <PERSON><PERSON>?"], ["0x18", "f", "Raptor <PERSON>|Gravity on Whiffed?", "Gravity of unsuccessful <PERSON>tor <PERSON>?"], ["0x1C", "f", "Raptor <PERSON>|Whiff Gravity-Related", ""], ["0x20", "f", "<PERSON><PERSON>|Unknown 0x20", ""], ["0x24", "f", "<PERSON><PERSON>|Unknown 0x24", ""], ["0x28", "f", "<PERSON><PERSON>|Unknown 0x28", ""], ["0x2C", "f", "Rap<PERSON>|Unknown 0x2C", ""], ["0x30", "f", "<PERSON><PERSON>|Unknown 0x30", ""], ["0x34", "f", "<PERSON><PERSON>|Unknown 0x34", ""], ["0x38", "f", "Raptor Boost|Landing-Lag/Freefall Toggle on Whiff", ""], ["0x3C", "f", "Raptor Boost|Landing-Lag/Freefall Toggle on Hit", ""], ["0x40", "f", "Falcon Dive|Air Friction", "Multiplier used for slowing down aerial movement"], ["0x44", "f", "Falcon Dive|Horizontal Momentum", ""], ["0x48", "f", "Falcon Dive|Freefall Air Speed Multiplier", "Multiplies <PERSON> Falcon's MaxAerialHorizontalSpeed attribute during freefall"], ["0x4C", "f", "Falcon Dive|Landing Lag", ""], ["0x50", "f", "Falcon Dive|Unknown 0x50", ""], ["0x54", "f", "Falcon Dive|Unknown 0x54", ""], ["0x58", "f", "Falcon Dive|Input-related", ""], ["0x5C", "f", "Falcon Dive|Unknown 0x5C", ""], ["0x60", "f", "Falcon Dive|Gravity During Grab", ""], ["0x64", "f", "Falcon Dive|Unknown 0x64", "Read during aerial state?"], ["0x68", "I", "Unknown 0x68", ""], ["0x6C", "I", "Falcon Kick|Unknown 0x6C", ""], ["0x70", "f", "Falcon Kick|Particles Angle", ""], ["0x74", "f", "Falcon Kick|Speed Modifier on Hit", ""], ["0x78", "I", "Falcon Kick|Unknown 0x78", ""], ["0x7C", "f", "Falcon Kick|Ground Lag Multiplier", "Higher number = less lag"], ["0x80", "f", "Falcon Kick|Landing Lag Multiplier", "Higher number = less lag"], ["0x84", "f", "Falcon Kick|Ground Traction", ""], ["0x88", "f", "Falcon Kick|Air Landing Traction", ""]], "Gn": [["-1", "character", "Ganondorf", ""], ["0x0", "f", "Warlock Punch|Stick Y-Axis Downward Angle Range", "Minimum control stick Y-Axis range required for downward variant"], ["0x4", "f", "Warlock Punch|Stick Y-Axis Upward Angle Range", "Maximum control stick Y-Axis range required for upward variant"], ["0x8", "f", "Warlock Punch|Angle Difference", "Angle difference for angled aerial Warlock Punch"], ["0xC", "f", "Warlock Punch|Horizontal Momentum", ""], ["0x10", "f", "Warlock Punch|Vertical Momentum", "Additional vertical momentum for angled aerial Warlock Punch"], ["0x14", "f", "Warlock Punch|Unknown 0x14", ""], ["0x18", "f", "Gerudo Dragon|Gravity on Whiff?", "Gravity of unsuccessful Gerudo Dragon?"], ["0x1C", "f", "Gerudo Dragon|Whiff Gravity-Related?", "Related to gravity of unsuccessful Gerudo <PERSON>?"], ["0x20", "f", "Gerudo Dragon|Unknown 0x20", ""], ["0x24", "f", "Gerudo Dragon|Unknown 0x24", ""], ["0x28", "f", "Gerudo Dragon|Unknown 0x28", ""], ["0x2C", "f", "Gerudo Dragon|Unknown 0x2C", ""], ["0x30", "f", "Gerudo Dragon|Unknown 0x30", ""], ["0x34", "f", "Gerudo Dragon|Unknown 0x34", ""], ["0x38", "f", "Gerudo Dragon|Landing-Lag/Freefall Toggle on Whiff", ""], ["0x3C", "f", "Gerudo Dragon|Landing-Lag/Freefall Toggle on Hit", ""], ["0x40", "f", "Dark Dive|Air Friction", "Multiplier used for slowing down aerial movement"], ["0x44", "f", "Dark Dive|Horizontal Momentum", ""], ["0x48", "f", "Dark Dive|Freefall Air Speed Multiplier", "Multiplies Ganon's MaxAerialHorizontalSpeed attribute during freefall"], ["0x4C", "f", "Dark Dive|Landing Lag", ""], ["0x50", "f", "Dark Dive|Unknown 0x50", ""], ["0x54", "f", "Dark Dive|Unknown 0x54", ""], ["0x58", "f", "Dark Dive|Input-related", ""], ["0x5C", "f", "Dark Dive|Unknown 0x5C", ""], ["0x60", "f", "Dark Dive|Gravity During Grab", ""], ["0x64", "f", "Dark Dive|Unknown 0x64", "Read during aerial state?"], ["0x68", "I", "Unknown 0x68", ""], ["0x6C", "I", "Wizard's Foot|Unknown 0x6C", ""], ["0x70", "f", "Wizard's Foot|Particles Angle", ""], ["0x74", "f", "Wizard's Foot|Speed Modifier on Hit", ""], ["0x78", "I", "Wizard's Foot|Unknown 0x78", ""], ["0x7C", "f", "Wizard's Foot|Ground Lag Multiplier", "Higher number = less lag"], ["0x80", "f", "Wizard's Foot|Landing Lag Multiplier", "Higher number = less lag"], ["0x84", "f", "Wizard's Foot|Ground Traction", ""], ["0x88", "f", "Wizard's Foot|Air Landing Traction", ""]], "Lk": [["-1", "character", "Link", ""], ["0x0", "f", "Arrows|Frames to maximum charge", ""], ["0x4", "f", "Arrows|Animation's frame speed modifier", "Applied after a 1-frame delay"], ["0x8", "f", "Arrows|Landing lag/Freefall toggle", ""], ["0xC", "I", "Arrows|Item loader ID", "Or bow item ID?"], ["0x10", "I", "Arrows|Item ID to load", ""], ["0x14", "f", "Boomerang|Control stick range for throw angle", ""], ["0x18", "f", "Boomerang|Launch angle", "In radians"], ["0x1C", "f", "Boomerang|Unknown 0x1C", ""], ["0x20", "f", "Boomerang|Smash launch velocity", ""], ["0x24", "f", "Boomerang|Tilt launch velocity", ""], ["0x28", "f", "Boomerang|Return proximity", "X-Position of 'catchbox'"], ["0x2C", "I", "Boomerang|Item ID?", ""], ["0x30", "f", "Spin Attack|Landing Lag", ""], ["0x34", "f", "Spin Attack|Initial horizontal momentum", ""], ["0x38", "f", "Spin Attack|Aerial mobility", ""], ["0x3C", "f", "Spin Attack|Momentum preservation?", ""], ["0x40", "f", "Spin Attack|Initial vertical momentum", ""], ["0x44", "f", "Spin Attack|Gravity", ""], ["0x48", "I", "Bomb|Item ID", ""], ["0x4C", "f", "Down-Air|Bounce vertical momentum", ""], ["0x50", "f", "Down-Air|Hitbox rehit rate", ""], ["0x54", "I", "Down-Air|Unknown 0x54", ""], ["0x58", "I", "Down-Air|Hitbox ID #0 damage on rehit", ""], ["0x5C", "I", "Down-Air|Hitbox ID #1 damage on rehit", ""], ["0x60", "I", "Down-Air|Hitbox ID #2 damage on rehit", ""], ["0x64", "f", "Down-Air|Unknown 0x64", ""], ["0x68", "f", "Down-Air|Unknown 0x68", ""], ["0x6C", "f", "Sword Trail Rendering|Color 1", ""], ["0x70", "f", "Sword Trail Rendering|Color 2", ""], ["0x74", "f", "Sword Trail Rendering|Color 3", ""], ["0x78", "I", "Sword Trail Rendering|Bone attachment ID", ""], ["0x7C", "f", "Sword Trail Rendering|Trail width", ""], ["0x80", "f", "Sword Trail Rendering|Trail height", ""], ["0x84", "I", "Hookshot (Standing)|Start delay", "In frames. Chain object is created on the frame that follows after value"], ["0x88", "I", "Hookshot (Standing)|Frame chain is released", ""], ["0x8C", "I", "Hookshot (Standing)|Frame chain retraction begins", ""], ["0x90", "I", "Hookshot (Standing)|Frame chain is fully retracted", ""], ["0x94", "I", "Hookshot (<PERSON> grab)|Start delay", "In frames. Chain object is created on the frame that follows after value"], ["0x98", "I", "Hookshot (Dash grab)|Frame chain is released", ""], ["0x9C", "I", "Hookshot (<PERSON> grab)|Frame chain retraction begins", ""], ["0xA0", "I", "Hookshot (Dash grab)|Frame chain is fully retracted", ""], ["0xA4", "I", "Hookshot (Zair)|Start delay", "In frames. Chain object is created on the frame that follows after value"], ["0xA8", "I", "Hookshot (Zair)|Frame chain is released", ""], ["0xAC", "I", "Hookshot (Zair)|Frame chain retraction begins", ""], ["0xB0", "I", "Hookshot (Zair)|Frame chain is fully retracted", ""], ["0xB4", "f", "Hookshot (Other)|Wall release jump height", ""], ["0xB8", "I", "Hookshot (Other)|Hang duration", ""], ["0xBC", "I", "Hookshot (Other)|Item ID", "IDs other than 62 or 63 crash the game upon loading Young Link's data file"], ["0xC0", "I", "Hookshot (Other)|Unknown 0xC0", ""], ["0xC4", "I", "Hylian Shield|Collision bubble bone attachment ID", "Changes shape beyond a certain ID"], ["0xC8", "f", "Hylian Shield|Collision bubble x-offset", ""], ["0xCC", "f", "Hylian Shield|Collision bubble y-offset", ""], ["0xD0", "f", "Hylian Shield|Collision bubble z-offset", ""], ["0xD4", "f", "Hylian Shield|Collision bubble size", ""], ["0xD8", "f", "Hylian Shield|Impact momentum multiplier", "Positive numbers pull and negatives push"]], "Cl": [["-1", "character", "<PERSON> Link", ""], ["0x0", "f", "Arrows|Frames to maximum charge", ""], ["0x4", "f", "Arrows|Animation's frame speed modifier", "Applied after a 1-frame delay"], ["0x8", "f", "Arrows|Landing lag/Freefall toggle", ""], ["0xC", "I", "Arrows|Item loader ID", "Or bow item ID?"], ["0x10", "I", "Arrows|Item ID to load", ""], ["0x14", "f", "Boomerang|Control stick range for throw angle", ""], ["0x18", "f", "Boomerang|Launch angle", "In radians"], ["0x1C", "f", "Boomerang|Unknown 0x1C", ""], ["0x20", "f", "Boomerang|Smash launch velocity", ""], ["0x24", "f", "Boomerang|Tilt launch velocity", ""], ["0x28", "f", "Boomerang|Return proximity", "X-Position of 'catchbox'"], ["0x2C", "I", "Boomerang|Item ID?", ""], ["0x30", "f", "Spin Attack|Landing Lag", ""], ["0x34", "f", "Spin Attack|Initial horizontal momentum", ""], ["0x38", "f", "Spin Attack|Aerial mobility", ""], ["0x3C", "f", "Spin Attack|Momentum preservation?", ""], ["0x40", "f", "Spin Attack|Initial vertical momentum", ""], ["0x44", "f", "Spin Attack|Gravity", ""], ["0x48", "I", "Bomb|Item ID", ""], ["0x4C", "f", "Down-Air|Bounce vertical momentum", ""], ["0x50", "f", "Down-Air|Hitbox rehit rate", ""], ["0x54", "I", "Down-Air|Unknown 0x54", ""], ["0x58", "I", "Down-Air|Hitbox ID #0 damage on rehit", ""], ["0x5C", "I", "Down-Air|Hitbox ID #1 damage on rehit", ""], ["0x60", "I", "Down-Air|Hitbox ID #2 damage on rehit", ""], ["0x64", "f", "Down-Air|Unknown 0x64", ""], ["0x68", "f", "Down-Air|Unknown 0x68", ""], ["0x6C", "f", "Sword Trail Rendering|Color 1", ""], ["0x70", "f", "Sword Trail Rendering|Color 2", ""], ["0x74", "f", "Sword Trail Rendering|Color 3", ""], ["0x78", "I", "Sword Trail Rendering|Bone attachment ID", ""], ["0x7C", "f", "Sword Trail Rendering|Trail width", ""], ["0x80", "f", "Sword Trail Rendering|Trail height", ""], ["0x84", "I", "Hookshot (Standing)|Start delay", "In frames. Chain object is created on the frame that follows after value"], ["0x88", "I", "Hookshot (Standing)|Frame chain is released", ""], ["0x8C", "I", "Hookshot (Standing)|Frame chain retraction begins", ""], ["0x90", "I", "Hookshot (Standing)|Frame chain is fully retracted", ""], ["0x94", "I", "Hookshot (<PERSON> grab)|Start delay", "In frames. Chain object is created on the frame that follows after value"], ["0x98", "I", "Hookshot (Dash grab)|Frame chain is released", ""], ["0x9C", "I", "Hookshot (<PERSON> grab)|Frame chain retraction begins", ""], ["0xA0", "I", "Hookshot (Dash grab)|Frame chain is fully retracted", ""], ["0xA4", "I", "Hookshot (Zair)|Start delay", "In frames. Chain object is created on the frame that follows after value"], ["0xA8", "I", "Hookshot (Zair)|Frame chain is released", ""], ["0xAC", "I", "Hookshot (Zair)|Frame chain retraction begins", ""], ["0xB0", "I", "Hookshot (Zair)|Frame chain is fully retracted", ""], ["0xB4", "f", "Hookshot (Other)|Wall release jump height", ""], ["0xB8", "I", "Hookshot (Other)|Hang duration", ""], ["0xBC", "I", "Hookshot (Other)|Item ID", "IDs other than 62 or 63 crash the game upon loading Young Link's data file"], ["0xC0", "I", "Hookshot (Other)|Unknown 0xC0", ""], ["0xC4", "I", "Kokiri Shield|Collision bubble bone attachment ID", "Changes shape beyond a certain ID"], ["0xC8", "f", "Kokiri Shield|Collision bubble x-offset", ""], ["0xCC", "f", "Kokiri Shield|Collision bubble y-offset", ""], ["0xD0", "f", "Kokiri Shield|Collision bubble z-offset", ""], ["0xD4", "f", "Kokiri Shield|Collision bubble size", ""], ["0xD8", "f", "Kokiri Shield|Impact momentum multiplier", "Positive numbers pull and negatives push"]], "Mr": [["-1", "character", "<PERSON>", ""], ["0x0", "f", "Cape|Horizontal momentum", "Smaller values allow for more momentum"], ["0x4", "f", "Cape|Horizontal velocity", ""], ["0x8", "f", "Cape|Vertical momentum", ""], ["0xC", "f", "Cape|Gravity", ""], ["0x10", "f", "Cape|Max falling speed", ""], ["0x14", "I", "Cape|Item kind/ID", ""], ["0x18", "f", "Super Jump Punch|Falling aerial mobility", ""], ["0x1C", "f", "Super Jump Punch|Landing lag / Freefall toggle", ""], ["0x20", "f", "Super Jump Punch|Minimum stick threshold to reverse Super Jump Punch", ""], ["0x24", "f", "Super Jump Punch|Minimum stick threshold for angle change?", ""], ["0x28", "f", "Super Jump Punch|Aerial control", "How much stick input can control movement/angle?"], ["0x2C", "f", "Super Jump Punch|Initial horizontal momemtum", "Initial horizontal momentum from moving stick forward"], ["0x30", "f", "Super Jump Punch|Initial gravity", "Gravity during initial Super Jump Punch"], ["0x34", "f", "Super Jump Punch|Aerial Initial vertical momemtum", ""], ["0x38", "f", "Tornado|Grounded gravity", "Affects ability to rise during grounded tornado"], ["0x3C", "f", "Tornado|Base air speed", ""], ["0x40", "f", "Tornado|Horizontal velocity limit", ""], ["0x44", "f", "Tornado|Horizontal acceleration", ""], ["0x48", "f", "Tornado|Horizontal drift", ""], ["0x4C", "f", "Tornado|Air speed deceleration rate", ""], ["0x50", "I", "Tornado|State variable", ""], ["0x54", "f", "Tornado|Rising tap power", "Ability to rise from tapping B"], ["0x58", "f", "Tornado|Terminal rising velocity", "Maximum vertical momentum gained from tapping B"], ["0x5C", "I", "Tornado|Landing lag / Freefall toggle", "Non-0 values toggle freefall after usage; Landing lag?"], ["0x60", "I", "Cape|Reflection bubble bone attachment ID", ""], ["0x64", "I", "Cape|Max feflectable damage", ""], ["0x68", "f", "Cape|Reflection bubble X-offset", ""], ["0x6C", "f", "Cape|Reflection bubble Y-offset", ""], ["0x70", "f", "Cape|Reflection bubble Z-offset", ""], ["0x74", "f", "Cape|Reflection bubble size", ""], ["0x78", "f", "Cape|Reflection bubble damage multiplier", ""], ["0x7C", "f", "Cape|Reflection bubble speed multiplier", ""], ["0x80", "I", "Cape|Reflector behavior?", "Byte at 0x80 is used to bit-shift value @ 0x2218 of Fighter_GObj"]], "Dr": [["-1", "character", "Dr. <PERSON>", ""], ["0x0", "f", "Cape|Horizontal momentum", "Smaller values allow for more momentum"], ["0x4", "f", "Cape|Horizontal velocity", ""], ["0x8", "f", "Cape|Vertical momentum", ""], ["0xC", "f", "Cape|Gravity", ""], ["0x10", "f", "Cape|Max falling speed", ""], ["0x14", "I", "Cape|Item kind/ID", ""], ["0x18", "f", "Super Jump Punch|Falling aerial mobility", ""], ["0x1C", "f", "Super Jump Punch|Landing lag / Freefall toggle", ""], ["0x20", "f", "Super Jump Punch|Stick threshold to Reverse Super Jump Punch", ""], ["0x24", "f", "Super Jump Punch|Stick horizontal movement threshold", "Minimum control stick input threshold for angle change?"], ["0x28", "f", "Super Jump Punch|Aerial control", "How much stick input can control movement"], ["0x2C", "f", "Super Jump Punch|Initial horizontal momemtum", "Initial horizontal momentum from moving stick forward"], ["0x30", "f", "Super Jump Punch|Initial gravity", "Gravity during initial Super Jump Punch"], ["0x34", "f", "Super Jump Punch|Aerial Initial vertical momemtum", ""], ["0x38", "f", "Tornado|Grounded gravity", "Affects ability to rise during grounded tornado"], ["0x3C", "f", "Tornado|Base air speed", ""], ["0x40", "f", "Tornado|Horizontal velocity limit", ""], ["0x44", "f", "Tornado|Horizontal acceleration", ""], ["0x48", "f", "Tornado|Horizontal drift", ""], ["0x4C", "f", "Tornado|Air speed deceleration rate", ""], ["0x50", "I", "Tornado|State variable", ""], ["0x54", "f", "Tornado|Rising tap power", "Ability to rise from tapping B"], ["0x58", "f", "Tornado|Terminal rising velocity", "Maximum vertical momentum gained from tapping B"], ["0x5C", "I", "Tornado|Landing lag / Freefall toggle", "Non-0 values toggle freefall after usage; Landing lag?"], ["0x60", "I", "Cape|Reflection bubble bone attachment ID", ""], ["0x64", "I", "Cape|Max feflectable damage", ""], ["0x68", "f", "Cape|Reflection bubble X-offset", ""], ["0x6C", "f", "Cape|Reflection bubble Y-offset", ""], ["0x70", "f", "Cape|Reflection bubble Z-offset", ""], ["0x74", "f", "Cape|Reflection bubble size", ""], ["0x78", "f", "Cape|Reflection bubble damage multiplier", ""], ["0x7C", "f", "Cape|Reflection bubble speed multiplier", ""], ["0x80", "I", "Cape|Reflector behavior?", "Byte at 0x80 is used to bit-shift value @ 0x2218 of Fighter_GObj"]], "Fx": [["-1", "character", "Fox", ""], ["0x0", "f", "Blaster|Unknown 0x0", ""], ["0x4", "f", "Blaster|Unknown 0x4", ""], ["0x8", "f", "Blaster|Unknown 0x8", ""], ["0xC", "f", "Blaster|Unknown 0xC", ""], ["0x10", "f", "Blaster|Launch angle", ""], ["0x14", "f", "Blaster|Launch speed", ""], ["0x18", "f", "Blaster|Landing lag / Freefall toggle", ""], ["0x1C", "I", "Blaster|Projectile ID", ""], ["0x20", "I", "Blaster|Blaster item ID", ""], ["0x24", "f", "Fox Illusion|Gravity frame delay", "The number of frames before gravity takes effect. Allows for ledge snap if number of frames doesn't exceed startup animation duration minus one. This is possible by default"], ["0x28", "f", "Fox Illusion|Initial horizontal momentum?", "Lower values = more? Only works with fractions beginning with 0!?"], ["0x2C", "f", "Fox Illusion|Unknown 0x2C", ""], ["0x30", "f", "Fox Illusion|Unknown 0x30", ""], ["0x34", "f", "Fox Illusion|Unknown 0x34", ""], ["0x38", "f", "Fox Illusion|Ground friction", ""], ["0x3C", "f", "Fox Illusion|Air dash speed", ""], ["0x40", "f", "Fox Illusion|Air dash momentum", ""], ["0x44", "f", "Fox Illusion|Air dash deceleration rate", "Applies to the end of the dash? Might be triggered by the Set Flag subAction"], ["0x48", "f", "Fox Illusion|Ending gravity", ""], ["0x4C", "f", "Fox Illusion|Unknown 0x4C", ""], ["0x50", "f", "Fox Illusion|Landing lag / Freefal toggle", "Setting to 0 crashes the game upon landing"], ["0x54", "f", "Fire Fox|Frames before gravity takes effect", ""], ["0x58", "f", "Fire Fox|Horizontal momentum during startup", ""], ["0x5C", "f", "Fire Fox|Aerial momentum preservation on startup", ""], ["0x60", "f", "Fire Fox|Fall acceleration", ""], ["0x64", "f", "Fire Fox|Control stick x-axis range required for direction read", ""], ["0x68", "f", "Fire Fox|Frames of travel", "Does not affect distance beyond a certain point"], ["0x6C", "I", "Fire Fox|Unknown 0x6C", ""], ["0x70", "f", "Fire Fox|Aerial ending momentum", ""], ["0x74", "f", "Fire Fox|Travel speed", ""], ["0x78", "f", "Fire Fox|Reverse acceleration?", ""], ["0x7C", "f", "Fire Fox|Grounded ending momentum", ""], ["0x80", "f", "Fire Fox|Unknown 0x80", ""], ["0x84", "f", "Fire Fox|Bounce horizontal velocity", ""], ["0x88", "f", "Fire Fox|Control stick x-axis turnaround range", "The range to change the character's direction in mid-air"], ["0x8C", "f", "Fire Fox|Unknown 0x8C", ""], ["0x90", "f", "Fire Fox|Landing lag / Freefall toggle", "Setting to 0 crashes the game upon landing"], ["0x94", "f", "Fire Fox|Landing lag modifier after bounce", ""], ["0x98", "f", "Reflector|Release delay frames", "Amount of additional frames the move is held for after B release"], ["0x9C", "f", "Reflector|Turn animation duration", "In frames"], ["0xA0", "f", "Reflector|Unknown 0xA0", ""], ["0xA4", "I", "Reflector|Frames before gravity takes effect", ""], ["0xA8", "f", "Reflector|Momentum preservation", "Higher values = less preservation. Lower values accelerate to the point of instant Self-Destruct"], ["0xAC", "f", "Reflector|Fall acceleration", ""], ["0xB0", "I", "Reflector|Reflect bubble bone attachment ID", ""], ["0xB4", "I", "Reflector|Max damage reflectable", ""], ["0xB8", "f", "Reflector|Reflect bubble x-offset", ""], ["0xBC", "f", "Reflector|Reflect bubble y-offset", ""], ["0xC0", "f", "Reflector|Reflect bubble z-offset", ""], ["0xC4", "f", "Reflector|Reflect bubble size", ""], ["0xC8", "f", "Reflector|Reflection damage multiplier", ""], ["0xCC", "f", "Reflector|Reflected projectile speed multiplier", ""], ["0xD0", "I", "Reflector|Behavior?", "0xD0 is used to bit-shift value @ 0x2218 of the Fighter_GObj"]], "Fc": [["-1", "character", "Falco", ""], ["0x0", "f", "Blaster|Unknown 0x0", ""], ["0x4", "f", "Blaster|Unknown 0x4", ""], ["0x8", "f", "Blaster|Unknown 0x8", ""], ["0xC", "f", "Blaster|Unknown 0xC", ""], ["0x10", "f", "Blaster|Launch angle", ""], ["0x14", "f", "Blaster|Launch speed", ""], ["0x18", "f", "Blaster|Landing lag / Freefall toggle", ""], ["0x1C", "I", "Blaster|Projectile ID", ""], ["0x20", "I", "Blaster|Blaster item ID", ""], ["0x24", "f", "Falco Phantasm|Gravity frame delay", "The number of frames before gravity takes effect. Allows for ledge snap if number of frames doesn't exceed startup animation duration minus one. This is possible by default"], ["0x28", "f", "Falco Phantasm|Initial horizontal momentum?", "Lower values = more? Only works with fractions beginning with 0!?"], ["0x2C", "f", "Falco Phantasm|Unknown 0x2C", ""], ["0x30", "f", "Falco Phantasm|Unknown 0x30", ""], ["0x34", "f", "Falco Phantasm|Unknown 0x34", ""], ["0x38", "f", "Falco Phantasm|Ground friction", ""], ["0x3C", "f", "Falco Phantasm|Air dash speed", ""], ["0x40", "f", "Falco Phantasm|Air dash momentum", ""], ["0x44", "f", "Falco Phantasm|Air dash deceleration rate", "Applies to the end of the dash? Might be triggered by the Set Flag subAction"], ["0x48", "f", "Falco Phantasm|Ending gravity", ""], ["0x4C", "f", "Falco Phantasm|Unknown 0x4C", ""], ["0x50", "f", "Falco Phantasm|Landing lag / Freefal toggle", "Setting to 0 crashes the game upon landing"], ["0x54", "f", "Fire Bird|Frames before gravity takes effect", ""], ["0x58", "f", "Fire Bird|Horizontal momentum during startup", ""], ["0x5C", "f", "Fire Bird|Aerial momentum preservation on startup", ""], ["0x60", "f", "Fire Bird|Fall acceleration", ""], ["0x64", "f", "Fire Bird|Control stick x-axis range required for direction read", ""], ["0x68", "f", "Fire Bird|Frames of travel", "Does not affect distance beyond a certain point"], ["0x6C", "I", "Fire Bird|Unknown 0x6C", ""], ["0x70", "f", "Fire Bird|Aerial ending momentum", ""], ["0x74", "f", "Fire Bird|Travel speed", ""], ["0x78", "f", "Fire Bird|Reverse acceleration?", ""], ["0x7C", "f", "Fire Bird|Grounded ending momentum", ""], ["0x80", "f", "Fire Bird|Unknown 0x80", ""], ["0x84", "f", "Fire Bird|Bounce horizontal velocity", ""], ["0x88", "f", "Fire Bird|Control stick x-axis turnaround range", "The range to change the character's direction in mid-air"], ["0x8C", "f", "Fire Bird|Unknown 0x8C", ""], ["0x90", "f", "Fire Bird|Landing lag / Freefall toggle", "Setting to 0 crashes the game upon landing"], ["0x94", "f", "Fire Bird|Landing lag modifier after bounce", ""], ["0x98", "f", "Reflector|Release delay frames", "Amount of additional frames the move is held for after B release"], ["0x9C", "f", "Reflector|Turn animation duration", "In frames"], ["0xA0", "f", "Reflector|Unknown 0xA0", ""], ["0xA4", "I", "Reflector|Frames before gravity takes effect", ""], ["0xA8", "f", "Reflector|Momentum preservation", "Higher values = less preservation. Lower values accelerate to the point of instant Self-Destruct"], ["0xAC", "f", "Reflector|Fall acceleration", ""], ["0xB0", "I", "Reflector|Reflect bubble bone attachment ID", ""], ["0xB4", "I", "Reflector|Max damage reflectable", ""], ["0xB8", "f", "Reflector|Reflect bubble x-offset", ""], ["0xBC", "f", "Reflector|Reflect bubble y-offset", ""], ["0xC0", "f", "Reflector|Reflect bubble z-offset", ""], ["0xC4", "f", "Reflector|Reflect bubble size", ""], ["0xC8", "f", "Reflector|Reflection damage multiplier", ""], ["0xCC", "f", "Reflector|Reflected projectile speed multiplier", ""], ["0xD0", "I", "Reflector|Behavior?", "0xD0 is used to bit-shift value @ 0x2218 of the Fighter_GObj"]], "Ms": [["-1", "character", "<PERSON><PERSON>", ""], ["0x0", "I", "Shield Breaker|Charge animation iterations until full charge", "Each charge animation lasts 30 frames"], ["0x4", "I", "Shield Breaker|Base damage", ""], ["0x8", "I", "Shield Breaker|Additional damage per iteration", ""], ["0xC", "f", "Shield Breaker|Aerial horizontal momentum", ""], ["0x10", "f", "Shield Breaker|Aerial horizontal deceleration rate", ""], ["0x14", "f", "Dancing Blade|Aerial horizontal momentum", ""], ["0x18", "f", "Dancing Blade|Aerial horizontal deceleration rate", ""], ["0x1C", "f", "Dancing Blade|Aerial vertical boost", ""], ["0x20", "f", "Dancing Blade|Aerial vertical deceleration rate", ""], ["0x24", "f", "Dancing Blade|Gravity", ""], ["0x28", "f", "Dolphin Slash|Freefall mobility multiplier", "Multiplies Mart<PERSON>'s MaxAerialHorizontalSpeed attribute"], ["0x2C", "f", "Dolphin Slash|Landing lag", ""], ["0x30", "f", "Dolphin Slash|Control stick X-Axis range for reverse Up-B", ""], ["0x34", "f", "Dolphin Slash|Control stick X-Axis range for reverse Up-B horizontal momentum", ""], ["0x38", "f", "Dolphin Slash|Direction input, maximum angle difference", "Directionalazation distance?"], ["0x3C", "f", "Dolphin Slash|Displacement/Horizontal momentum from direction input", ""], ["0x40", "f", "Dolphin Slash|Aerial jump height ratio", "Relative to grounded version"], ["0x44", "f", "Dolphin Slash|Gravity after use", ""], ["0x48", "f", "Dolphin Slash|Maximum falling speed after use", ""], ["0x4C", "f", "Counter|Horizontal momentum", ""], ["0x50", "f", "Counter|Horizontal deceleration", ""], ["0x54", "f", "Counter|Gravity", ""], ["0x58", "f", "Counter|Maximum falling speed", ""], ["0x5C", "f", "Counter|Damage multiplier?", ""], ["0x60", "f", "Counter|Amount of hitlag frames on activation", ""], ["0x64", "I", "Counter|Detection bubble Bone ID", ""], ["0x68", "f", "Counter|Detection bubble X-Offset", ""], ["0x6C", "f", "Counter|Detection bubble Y-Offset", ""], ["0x70", "f", "Counter|Detection bubble Z-Offset", ""], ["0x74", "f", "Counter|Detection bubble size", ""], ["0x78", "f", "Sword Trail|Fade", "Lower values = smoother, higher values = rougher"], ["0x7C", "f", "Sword Trail|Length?", ""], ["0x80", "f", "Sword Trail|Color 1", ""], ["0x84", "f", "Sword Trail|Color 2", ""], ["0x88", "f", "Sword Trail|Color 3", ""], ["0x8C", "I", "Sword Trail|Bone attachment ID", ""], ["0x90", "f", "Sword Trail|Width", ""], ["0x94", "f", "Sword Trail|Height", ""]], "Fe": [["-1", "character", "<PERSON>", ""], ["0x0", "I", "Flare Blade|Charge animation iterations until full charge", "Each charge animation lasts 30 frames"], ["0x4", "I", "Flare Blade|Base damage", ""], ["0x8", "I", "Flare Blade|Additional damage per iteration", ""], ["0xC", "f", "Flare Blade|Aerial horizontal momentum", ""], ["0x10", "f", "Flare Blade|Aerial horizontal deceleration rate", ""], ["0x14", "f", "Double-Edge Dance|Aerial horizontal momentum", ""], ["0x18", "f", "Double-Edge Dance|Aerial horizontal deceleration rate", ""], ["0x1C", "f", "Double-Edge Dance|Aerial vertical boost", ""], ["0x20", "f", "Double-Edge Dance|Aerial vertical deceleration rate", ""], ["0x24", "f", "Double-Edge Dance|Gravity", ""], ["0x28", "f", "Blazer|Freefall mobility multiplier", "Multiplies Mart<PERSON>'s MaxAerialHorizontalSpeed attribute"], ["0x2C", "f", "Blazer|Landing lag", ""], ["0x30", "f", "Blazer|Control stick X-Axis range for reverse Up-B", ""], ["0x34", "f", "Blazer|Control stick X-Axis range for reverse Up-B horizontal momentum", ""], ["0x38", "f", "Blazer|Direction input, maximum angle difference", "Directionalazation distance?"], ["0x3C", "f", "Blazer|Displacement/Horizontal momentum from direction input", ""], ["0x40", "f", "Blazer|Aerial jump height ratio", "Relative to grounded version"], ["0x44", "f", "Blazer|Gravity after use", ""], ["0x48", "f", "Blazer|Maximum falling speed after use", ""], ["0x4C", "f", "Counter|Horizontal momentum", ""], ["0x50", "f", "Counter|Horizontal deceleration", ""], ["0x54", "f", "Counter|Gravity", ""], ["0x58", "f", "Counter|Maximum falling speed", ""], ["0x5C", "f", "Counter|Damage multiplier?", ""], ["0x60", "f", "Counter|Amount of hitlag frames on activation", ""], ["0x64", "I", "Counter|Detection bubble Bone ID", ""], ["0x68", "f", "Counter|Detection bubble X-Offset", ""], ["0x6C", "f", "Counter|Detection bubble Y-Offset", ""], ["0x70", "f", "Counter|Detection bubble Z-Offset", ""], ["0x74", "f", "Counter|Detection bubble size", ""], ["0x78", "f", "Sword Trail|Fade", "Lower values = smoother, higher values = rougher"], ["0x7C", "f", "Sword Trail|Length?", ""], ["0x80", "f", "Sword Trail|Color 1", ""], ["0x84", "f", "Sword Trail|Color 2", ""], ["0x88", "f", "Sword Trail|Color 3", ""], ["0x8C", "I", "Sword Trail|Bone attachment ID", ""], ["0x90", "f", "Sword Trail|Width", ""], ["0x94", "f", "Sword Trail|Height", ""]], "Zd": [["-1", "character", "<PERSON><PERSON><PERSON>", ""], ["0x0", "f", "Nayru's Love Physics|Unknown 0x0", ""], ["0x4", "I", "<PERSON><PERSON><PERSON>'s Love Physics|Frames before gravity takes effect", ""], ["0x8", "f", "Nayru's Love Physics|Horizontal momemtum preservation", "Higher values = less preservation. Values close to or = 0 accelerate to the point of instant Self-Destruct"], ["0xC", "f", "Nayru's Love Physics|Fall acceleration", ""], ["0x10", "I", "Din's Fire|Unknown 0x10", ""], ["0x14", "I", "Din's Fire|Max hold time", "Maximum amount of time <PERSON><PERSON><PERSON> can stay in hold action state? In frames"], ["0x18", "I", "Din's Fire|Frames before gravity takes effect", ""], ["0x1C", "I", "Din's Fire|Amount of frames Din's Fire is auto-charged", ""], ["0x20", "f", "Din's Fire|Spawn x-offset", ""], ["0x24", "f", "Din's Fire|Spawn y-offset", ""], ["0x28", "f", "Din's Fire|Unknown 0x28", ""], ["0x2C", "f", "Din's Fire|Fall acceleration", ""], ["0x30", "f", "Din's Fire|Unknown 0x30", ""], ["0x34", "f", "Din's Fire|Landing lag / Freefall toggle", ""], ["0x38", "f", "Farore's Wind|Horizontal momentum preservation?", "Lower values = more preservation. Crashes if set to 0"], ["0x3C", "f", "Farore's Wind|Vertical momentum preservation?", "Lower values = more preservation. Crashes if set to 0"], ["0x40", "f", "Farore's Wind|Fall acceleration", ""], ["0x44", "f", "Farore's Wind|Unknown 0x44", ""], ["0x48", "I", "Farore's Wind|Travel distance", ""], ["0x4C", "f", "Farore's Wind|Unknown 0x4C", ""], ["0x50", "f", "Farore's Wind|Minimum control stick X-Axis range required to apply direction", ""], ["0x54", "f", "Farore's Wind|Base momentum?", ""], ["0x58", "f", "Farore's Wind|Momentum-related", ""], ["0x5C", "f", "Farore's Wind|Momentum after aerial reappearance!?", "Shakes horizontally and goes insane at negative values"], ["0x60", "I", "Farore's Wind|Surface angle to determine whether to stop or to continue sliding", ""], ["0x64", "f", "Farore's Wind|Also momentum after reappearance!?", "WTF, man"], ["0x68", "f", "Farore's Wind|Unknown 0x68", ""], ["0x6C", "f", "Farore's Wind|Landing lag", ""], ["0x70", "f", "Transform|Horizontal momentum modifier/Preservation?", "Setting this to 0 crashes the game upon initiating Down-B"], ["0x74", "f", "Transform|Vertical momentum modifier/Preservation?", "Setting this to 0 crashes the game upon initiating Down-B"], ["0x78", "f", "Transform|Unknown 0x78", ""], ["0x7C", "f", "Transform|Unknown 0x7C", ""], ["0x80", "f", "Transform|Target transofrmation frame", "Frame of animation Sheik -> <PERSON><PERSON><PERSON> transformation transitions to after change"], ["0x84", "I", "Nayru's Love Refelection Bubble|Bone attachment ID", ""], ["0x88", "I", "<PERSON><PERSON><PERSON>'s Love Refelection Bubble|Max damage reflectable", ""], ["0x8C", "f", "<PERSON><PERSON><PERSON>'s Love Refelection Bubble|X-offset", ""], ["0x90", "f", "<PERSON><PERSON><PERSON>'s Love Refelection Bubble|Y-offset", ""], ["0x94", "f", "<PERSON><PERSON><PERSON>'s Love Refelection Bubble|Size", ""], ["0x98", "f", "<PERSON><PERSON><PERSON>'s Love Refelection Bubble|Unknown 0x98", ""], ["0x9C", "f", "Nayru's Love Refelection Bubble|Damage multiplier", ""], ["0xA0", "f", "<PERSON><PERSON><PERSON>'s Love Refelection Bubble|Reflected projectile speed multiplier", ""], ["0xA4", "I", "<PERSON><PERSON><PERSON>'s Love Refelection Bubble|Reflector behavior?", "Byte at 0xA4 is used to bit-shift value @ 0x2218 of Fighter_GObj"]], "Sk": [["-1", "character", "<PERSON><PERSON>", ""], ["0x0", "f", "Needle Storm|Grounded X-Offset of graphical effect", ""], ["0x4", "f", "Needle Storm|Grounded Y-Offset of graphical effect", ""], ["0x8", "f", "Needle Storm|Aerial X-Offset of graphical effect", ""], ["0xC", "f", "Needle Storm|Aerial Y-Offset of graphical effect", ""], ["0x10", "f", "Needle Storm|Landing lag / Freefall toggle", ""], ["0x14", "f", "Chain Dance|Base duration", "In frames"], ["0x18", "f", "Chain Dance|Rehit rate", "In frames. Refreshes if chain is dropped. Setting this to 0 disables the rehit property"], ["0x1C", "f", "Chain Dance|Frame on which chain appears", "If value is set higher than the amount of frames the startup animation lasts, it will not appear and crash upon retraction"], ["0x20", "f", "Chain Dance|Frame on which chain becomes controllable", ""], ["0x24", "f", "Chain Dance|Retraction delay after B button release", "In frames"], ["0x28", "f", "Chain Dance|Frame on which chain retraction begins", ""], ["0x2C", "f", "Vanish|Initial vertical momentum", ""], ["0x30", "f", "Vanish|Initial vertical deceleration rate?", "Higher values = less deceleration. Strange attribute"], ["0x34", "f", "Vanish|Initial fall acceleration?", ""], ["0x38", "I", "Vanish|Vanish travel distance", ""], ["0x3C", "f", "Vanish|Unknown 0x3C", ""], ["0x40", "f", "Vanish|Minimum control stick X-Axis range required for direction", ""], ["0x44", "f", "Vanish|Base momentum?", ""], ["0x48", "f", "Vanish|Momentum-related", ""], ["0x4C", "f", "Vanish|Momentum after aerial reappearance!?", "Shakes horizontally and goes insane at negative values"], ["0x50", "I", "Vanish|Surface angle slide decision", "Surface angle to determine whether to stop or to continue sliding"], ["0x54", "f", "Vanish|Also momentum after reappearance!?", "WTF, man"], ["0x58", "f", "Vanish|Unknown 0x58", ""], ["0x5C", "f", "Vanish|Landing lag", "Setting this to 0 causes the game to crash upon landing"], ["0x60", "f", "Transform|Horizontal momentum modifier/Preservation?", "Setting this to 0 crashes the game upon initiating Down-B"], ["0x64", "f", "Transform|Vertical momentum modifier/Preservation?", "Setting this to 0 crashes the game upon initiating Down-B"], ["0x68", "f", "Transform|Unknown 0c68", ""], ["0x6C", "f", "Transform|Unknown 0x6C", ""], ["0x70", "f", "Transform|Target transofrmation frame", "Frame of animation Zelda -> <PERSON><PERSON> transformation transitions to after change"]], "Dk": [["-1", "character", "Donkey Kong", ""], ["0x0", "I", "Unknown|Unknown 0x0", ""], ["0x4", "I", "", ""], ["0x8", "f", "", ""], ["0xC", "f", "", ""], ["0x10", "f", "", ""], ["0x14", "f", "", ""], ["0x18", "f", "", ""], ["0x1C", "f", "", ""], ["0x20", "f", "Cargo Hold|Turn Speed", ""], ["0x24", "f", "Cargo Hold|Jump Startup Lag", ""], ["0x28", "f", "Cargo Hold|Landing Lag", ""], ["0x2C", "I", "Giant Punch|Amount of arm swings to fully charge", ""], ["0x30", "I", "Giant Punch|Damage increase per charge", "Does not apply to first swing"], ["0x34", "f", "Giant Punch|Grounded charged punch horizontal velocity", ""], ["0x38", "f", "Giant Punch|Landing lag / Freefall toggle", "If set to 0, <PERSON><PERSON> does not enter freefall after aerial Giant Punch, but loses his double jump"], ["0x3C", "f", "Headbutt|Minimum control stick X-Axis range required for forward momentum", "Control stick range is capped at 1.0, so this doesn't do anything. Set to equal or lower than 1.0 to allow momentum"], ["0x40", "f", "Headbutt|Momentum transition modifier", "Controls how much external momentum can be carried into Headbutt"], ["0x44", "f", "Headbutt|Aerial gravity", ""], ["0x48", "f", "", ""], ["0x4C", "f", "Spinning Kong|Aerial vertical velocity", ""], ["0x50", "f", "Spinning Kong|Aerial gravity", ""], ["0x54", "f", "Spinning Kong|Grounded horizontal velocity", ""], ["0x58", "f", "Spinning Kong|Aerial horizontal velocity", ""], ["0x5C", "f", "Spinning Kong|Grounded mobility", ""], ["0x60", "f", "Spinning Kong|Aerial mobility", ""], ["0x64", "f", "Spinning Kong|Landing lag / Freefall toggle", ""], ["0x68", "f", "Hand Slap|X-Offset of hitbox #1", ""], ["0x6C", "f", "Hand Slap|X-Offset of hitbox #2", ""], ["0x70", "f", "Hand Slap|Height difference-related?", ""]], "Pk": [["-1", "character", "<PERSON><PERSON><PERSON>", ""], ["0x0", "f", "Thunder Jolt|Grounded Jolt spawn X-offset", ""], ["0x4", "f", "Thunder Jolt|Grounded Jolt spawn Y-offset", ""], ["0x8", "f", "Thunder Jolt|Aerial Jolt spawn X-offset", ""], ["0xC", "f", "Thunder Jolt|Aerial Jolt spawn Y-offset", ""], ["0x10", "f", "Thunder Jolt|Landing lag / Freefall toggle", ""], ["0x14", "I", "Thunder Jolt|Item ID?", "For aerial?"], ["0x18", "I", "Thunder Jolt|Item ID?", "For grounded?"], ["0x1C", "f", "Skull Bash|Smash window", "In frames"], ["0x20", "f", "Skull Bash|Charge rate", ""], ["0x24", "f", "Skull Bash|Max charge duration", "In frames"], ["0x28", "f", "Skull Bash|Tilt damage", ""], ["0x2C", "f", "Skull Bash|Slope damage multiplier", "Slope damage = Base damage + frames spent charging * this value at 0x14"], ["0x30", "f", "Skull Bash|Traction multiplier", ""], ["0x34", "f", "Skull Bash|Unknown 0x34", ""], ["0x38", "f", "Skull Bash|Falling speed", ""], ["0x3C", "f", "Skull Bash|Initial horizontal launch momentum", ""], ["0x40", "f", "Skull Bash|Horizontal momentum multiplier", ""], ["0x44", "f", "Skull Bash|Initial vertical launch momentum", ""], ["0x48", "f", "Skull Bash|Vertical momentum multiplier", ""], ["0x4C", "f", "Skull Bash|Gravity on launch animation transition", ""], ["0x50", "f", "Skull Bash|Ending friction modifier", "0 = Instant Self-Destruct at end of animation or bonus crash on floor interaction/hit"], ["0x54", "f", "Skull Bash|Horizontal deceleration after launch animation", ""], ["0x58", "f", "Skull Bash|Gravity multiplier at end of launch animation", ""], ["0x5C", "I", "Quick Attack|Unknown 0x5C", "Read on startup"], ["0x60", "I", "Quick Attack|Travel distance", ""], ["0x64", "f", "Quick Attack|Momentum subtracted on frames 11-13", "During start-up"], ["0x68", "f", "Quick Attack|Model rotation for grounded dash", ""], ["0x6C", "f", "Quick Attack|Model width multiplier for grounded dash", ""], ["0x70", "f", "Quick Attack|Model height multiplier for grounded dash", ""], ["0x74", "f", "Quick Attack|Model length multiplier for grounded dash", ""], ["0x78", "f", "Quick Attack|Model rotation for aerial dash", ""], ["0x7C", "f", "Quick Attack|Model width multiplier for aerial dash", ""], ["0x80", "f", "Quick Attack|Model height multiplier for aerial dash", ""], ["0x84", "f", "Quick Attack|Model lenght multiplier for aerial dash", ""], ["0x88", "f", "Quick Attack|Unknown 0x88", ""], ["0x8C", "f", "Quick Attack|Control stick range required for direction change", ""], ["0x90", "f", "Quick Attack|Base momentum of both dashes", ""], ["0x94", "f", "Quick Attack|Additional momentum applied to starting momentum", ""], ["0x98", "f", "Quick Attack|2nd dash length multiplier", ""], ["0x9C", "f", "Quick Attack|Horizontal momentum preservation", ""], ["0xA0", "I", "Quick Attack|Surface angle slide decision", "Degrees of surface angle for deciding whether to halt momentum or to continue sliding"], ["0xA4", "f", "Quick Attack|Horizontal momentum subtraction", "Per frame, after frame 5 of second dash"], ["0xA8", "I", "Quick Attack|Minimum direction change for 2nd dash", "Minimum angle difference required for 2nd dash"], ["0xAC", "f", "Quick Attack|Unknown 0xAC", ""], ["0xB0", "f", "Quick Attack|Landing lag / Freefall toggle", ""], ["0xB4", "f", "Thunder|Vertical momentum gain on thunder self-hit", ""], ["0xB8", "f", "Thunder|Fall acceleration on Thunder self-hit", ""], ["0xBC", "f", "Thunder|Vertical range needed for thunder self-hit?", ""], ["0xC0", "f", "Thunder|Travel speed", ""], ["0xC4", "f", "Thunder|Horizontal range needed for Thunder self-hit?", ""], ["0xC8", "f", "Thunder|Vertical range needed for Thunder self-hit?", ""], ["0xCC", "f", "Thunder|Vertical displacement of Thunder cloud", ""], ["0xD0", "f", "Thunder|Spawn Y-Offset", ""], ["0xD4", "I", "Thunder|Number of lightning bursts", ""], ["0xD8", "I", "Thunder|Delay between lightning bursts", "In frames"], ["0xDC", "I", "Thunder|Item ID to summon?", "Appears to also accept <PERSON><PERSON>'s <PERSON>, ID=82. Other values freeze the game"], ["0xE0", "f", "Quick Attack|Unknown 0xE0", ""], ["0xE4", "f", "Quick Attack|Vertical ECB warp during Up-B", ""], ["0xE8", "f", "Quick Attack|ECB warp during Up-B dash ending animation!?", "ECB warp during Up-B dash ending animation!?"], ["0xEC", "f", "Quick Attack|ECB warp during Up-B dash ending animation!?", ""], ["0xF0", "f", "Quick Attack|ECB warp during Up-B dash ending animation!?", ""], ["0xF4", "f", "Quick Attack|ECB warp during Up-B dash ending animation!?", ""]], "Pc": [["-1", "character", "<PERSON><PERSON>", ""], ["0x0", "f", "Thunder Jolt|Grounded Jolt spawn X-offset", ""], ["0x4", "f", "Thunder Jolt|Grounded Jolt spawn Y-offset", ""], ["0x8", "f", "Thunder Jolt|Aerial Jolt spawn X-offset", ""], ["0xC", "f", "Thunder Jolt|Aerial Jolt spawn Y-offset", ""], ["0x10", "f", "Thunder Jolt|Landing lag / Freefall toggle", ""], ["0x14", "I", "Thunder Jolt|Item ID?", "For aerial?"], ["0x18", "I", "Thunder Jolt|Item ID?", "For grounded?"], ["0x1C", "f", "Skull Bash|Smash window", "In frames"], ["0x20", "f", "Skull Bash|Charge rate", ""], ["0x24", "f", "Skull Bash|Max charge duration", "In frames"], ["0x28", "f", "Skull Bash|Tilt damage", ""], ["0x2C", "f", "Skull Bash|Slope damage multiplier", "Slope damage = Base damage + frames spent charging * this value at 0x14"], ["0x30", "f", "Skull Bash|Traction multiplier", ""], ["0x34", "f", "Skull Bash|Unknown 0x34", ""], ["0x38", "f", "Skull Bash|Falling speed", ""], ["0x3C", "f", "Skull Bash|Initial horizontal launch momentum", ""], ["0x40", "f", "Skull Bash|Horizontal momentum multiplier", ""], ["0x44", "f", "Skull Bash|Initial vertical launch momentum", ""], ["0x48", "f", "Skull Bash|Vertical momentum multiplier", ""], ["0x4C", "f", "Skull Bash|Gravity on launch animation transition", ""], ["0x50", "f", "Skull Bash|Ending friction modifier", "0 = Instant Self-Destruct at end of animation or bonus crash on floor interaction/hit"], ["0x54", "f", "Skull Bash|Horizontal deceleration after launch animation", ""], ["0x58", "f", "Skull Bash|Gravity multiplier at end of launch animation", ""], ["0x5C", "I", "Quick Attack|Unknown 0x5C", "Read on startup"], ["0x60", "I", "Quick Attack|Travel distance", ""], ["0x64", "f", "Quick Attack|Momentum subtracted on frames 11-13", "During start-up"], ["0x68", "f", "Quick Attack|Model rotation for grounded dash", ""], ["0x6C", "f", "Quick Attack|Model width multiplier for grounded dash", ""], ["0x70", "f", "Quick Attack|Model height multiplier for grounded dash", ""], ["0x74", "f", "Quick Attack|Model length multiplier for grounded dash", ""], ["0x78", "f", "Quick Attack|Model rotation for aerial dash", ""], ["0x7C", "f", "Quick Attack|Model width multiplier for aerial dash", ""], ["0x80", "f", "Quick Attack|Model height multiplier for aerial dash", ""], ["0x84", "f", "Quick Attack|Model lenght multiplier for aerial dash", ""], ["0x88", "f", "Quick Attack|Unknown 0x88", ""], ["0x8C", "f", "Quick Attack|Control stick range required for direction change", ""], ["0x90", "f", "Quick Attack|Base momentum of both dashes", ""], ["0x94", "f", "Quick Attack|Additional momentum applied to starting momentum", ""], ["0x98", "f", "Quick Attack|2nd dash length multiplier", ""], ["0x9C", "f", "Quick Attack|Horizontal momentum preservation", ""], ["0xA0", "I", "Quick Attack|Surface angle slide decision", "Degrees of surface angle for deciding whether to halt momentum or to continue sliding"], ["0xA4", "f", "Quick Attack|Horizontal momentum subtraction", "Per frame, after frame 5 of second dash"], ["0xA8", "I", "Quick Attack|Minimum direction change for 2nd dash", "Minimum angle difference required for 2nd dash"], ["0xAC", "f", "Quick Attack|Unknown 0xAC", ""], ["0xB0", "f", "Quick Attack|Landing lag / Freefall toggle", ""], ["0xB4", "f", "Thunder|Vertical momentum gain on thunder self-hit", ""], ["0xB8", "f", "Thunder|Fall acceleration on Thunder self-hit", ""], ["0xBC", "f", "Thunder|Vertical range needed for thunder self-hit?", ""], ["0xC0", "f", "Thunder|Travel speed", ""], ["0xC4", "f", "Thunder|Horizontal range needed for Thunder self-hit?", ""], ["0xC8", "f", "Thunder|Vertical range needed for Thunder self-hit?", ""], ["0xCC", "f", "Thunder|Vertical displacement of Thunder cloud", ""], ["0xD0", "f", "Thunder|Spawn Y-Offset", ""], ["0xD4", "I", "Thunder|Number of lightning bursts", ""], ["0xD8", "I", "Thunder|Delay between lightning bursts", "In frames"], ["0xDC", "I", "Thunder|Item ID to summon?", "Appears to also accept <PERSON><PERSON>'s <PERSON>, ID=82. Other values freeze the game"], ["0xE0", "f", "Quick Attack|Unknown 0xE0", ""], ["0xE4", "f", "Quick Attack|Vertical ECB warp during Up-B", ""], ["0xE8", "f", "Quick Attack|ECB warp during Up-B dash ending animation!?", "ECB warp during Up-B dash ending animation!?"], ["0xEC", "f", "Quick Attack|ECB warp during Up-B dash ending animation!?", ""], ["0xF0", "f", "Quick Attack|ECB warp during Up-B dash ending animation!?", ""], ["0xF4", "f", "Quick Attack|ECB warp during Up-B dash ending animation!?", ""]], "Pe": [["-1", "character", "Peach", ""], ["0x0", "f", "Unknown 0x0", ""], ["0x4", "f", "Unknown 0x4", ""], ["0x8", "f", "Float-related?", ""], ["0xC", "f", "Float duration", "In frames"], ["0x10", "I", "Turnip|Number of item data sets to load?", ""], ["0x14", "I", "Turnip|Odds of getting an item", "1/X, where X = this value"], ["0x18", "I", "Turnip|Item #1 odds", ""], ["0x1C", "I", "Turnip|Item #1 ID", ""], ["0x20", "I", "Turnip|Item #2 odds", ""], ["0x24", "I", "Turnip|Item #2 ID", ""], ["0x28", "I", "Turnip|Item #3 odds", ""], ["0x2C", "I", "Turnip|Item #3 ID", ""], ["0x30", "I", "Unknown|Unknown 0x30", ""], ["0x34", "f", "Unknown|Unknown 0x34", ""], ["0x38", "f", "Unknown|Unknown 0x38", ""], ["0x3C", "f", "Unknown|Unknown 0x3C", ""], ["0x40", "f", "Peach Bomber|Initial vertical momemtum", "While airborne during the start of the move"], ["0x44", "f", "Peach Bomber|Tilt horizontal momentum", ""], ["0x48", "f", "Peach Bomber|Smash horizontal momentum", ""], ["0x4C", "f", "Peach Bomber|Vertical momentum", ""], ["0x50", "f", "Peach Bomber|Gravity on aerial miss", "Negative goes upwards"], ["0x54", "f", "Peach Bomber|Momentum acceleration", "If set high peach will stop after traveling a little. If set low or negative peach will travel A LOT of distance"], ["0x58", "f", "Peach Bomber|Gravity related?", ""], ["0x5C", "f", "Peach Bomber|Gravity related?", ""], ["0x60", "f", "Peach Bomber|Horizontal acceleration during recoil", ""], ["0x64", "f", "Peach Bomber|Vertical acceleration during recoil", ""], ["0x68", "f", "Peach Bomber|", ""], ["0x6C", "f", "Peach Bomber|", ""], ["0x70", "f", "Peach Parasol|", ""], ["0x74", "f", "Peach Parasol|Landing lag", ""], ["0x78", "f", "Peach Parasol|", ""], ["0x7C", "f", "Peach Parasol|", ""], ["0x80", "f", "Peach Parasol|Direction input; maximum angle difference", "Directionalazation distance"], ["0x84", "f", "Peach Parasol|Displacement/Horizontal momentum from direction input", ""], ["0x88", "f", "Peach Parasol|", ""], ["0x8C", "f", "Peach Parasol|Aerial jump height ratio", "Relative to grounded version"], ["0x90", "f", "", ""], ["0x94", "f", "Toad|", ""], ["0x98", "f", "Toad|Aerial horizontal momentum preservation?", "Very strict conditions, only works with fractions beginning with 0?"], ["0x9C", "f", "Toad|Aerial initial vertical momentum", ""], ["0xA0", "f", "Toad|Fall acceleration?", ""], ["0xA4", "f", "Toad|", ""], ["0xA8", "f", "Toad|", ""], ["0xAC", "f", "Toad|Detection Bubble Bone ID", ""], ["0xB0", "f", "Toad|Detection Bubble X-offset", ""], ["0xB4", "f", "Toad|Detection Bubble Y-offset", ""], ["0xB8", "f", "Toad|Detection Bubble Z-offset", ""], ["0xBC", "f", "Toad|Detection Bubble size", ""]], "Gw": [["-1", "character", "Game & Watch", ""], ["0x0", "f", "Model Thickness", ""], ["0x4", "I", "", ""], ["0x8", "f", "", ""], ["0xC", "f", "", ""], ["0x10", "f", "", ""], ["0x14", "f", "", ""], ["0x18", "f", "Chef|Hit loop start", "Frame on which repeated hit begins"], ["0x1C", "f", "Chef|Max sausages per use", ""], ["0x20", "f", "Judgement|Momentum preservation", "Higher values = less preservation. Values close to or = 0 accelerate to the point of instant Self-Destruct"], ["0x24", "f", "Judgement|Momentum preservation modifier", "Higher values = less preservation"], ["0x28", "f", "Judgement|", ""], ["0x2C", "f", "Judgement|", ""], ["0x30", "f", "Judgement|", ""], ["0x34", "I", "Judgement|Allow number 1", "This is a 0/1 toggle that determine whether this number can show up in Judgement. At least 3 judgement numbers must be enabled to prevent crashes"], ["0x38", "I", "Judgement|Allow number 2", ""], ["0x3C", "I", "Judgement|Allow number 3", ""], ["0x40", "I", "Judgement|Allow number 4", ""], ["0x44", "I", "Judgement|Allow number 5", ""], ["0x48", "I", "Judgement|Allow number 6", ""], ["0x4C", "I", "Judgement|Allow number 7", ""], ["0x50", "I", "Judgement|Allow number 8", ""], ["0x54", "I", "Judgement|Allow number 9", ""], ["0x58", "f", "Fire|Launch angle modifier?", "Lower = more control"], ["0x5C", "f", "Fire|Maximum launch angle difference", "In radians"], ["0x60", "f", "Fire|Landing lag / Freefall toggle", ""], ["0x64", "f", "Oil Panic|Momentum preservation", "Higher values = less preservation. Values close to or = 0 accelerate to the point of instant Self-Destruct"], ["0x68", "f", "Oil Panic|Momentum preservation modifier", ""], ["0x6C", "f", "Oil Panic|Fall acceleration", ""], ["0x70", "f", "Oil Panic|", ""], ["0x74", "f", "Oil Panic|Damage added to multiplier", "Added separately to multiplier"], ["0x78", "f", "Oil Panic|Damage multiplier", ""], ["0x7C", "f", "Oil Panic|", ""], ["0x80", "I", "Oil Panic|Absorption Bubble Bone ID", ""], ["0x84", "f", "Oil Panic|Absorption Bubble X-Position", ""], ["0x88", "f", "Oil Panic|Absorption Bubble Y-Position", ""], ["0x8C", "f", "Oil Panic|Absorption Bubble Z-Position", ""], ["0x90", "f", "Oil Panic|Absorption Bubble size", ""]], "Pp": [["-1", "character", "Popo", ""], ["0x0", "f", "Spawn horizontal offset", "Only applies to Player Climber, even though it also exists in CPU Climber's array"], ["0x4", "f", "Ice Shot|Aerial vertical momentum", ""], ["0x8", "f", "Ice Shot|Landing lag", ""], ["0xC", "f", "Ice Shot|Ice Shot spawn X-Offset", ""], ["0x10", "f", "Ice Shot|Ice Shot spawn Y-Offset", ""], ["0x14", "f", "Unknown|", ""], ["0x18", "f", "", ""], ["0x1C", "f", "", ""], ["0x20", "f", "Squall Hammer|Height gain from B tap", ""], ["0x24", "f", "Squall Hammer|Base vertical velocity", ""], ["0x28", "f", "Squall Hammer|", ""], ["0x2C", "f", "Squall Hammer|Initial horizontal velocity/Horizontal warp?", ""], ["0x30", "f", "Squall Hammer|Slope angle sensitivity-related", ""], ["0x34", "f", "Squall Hammer|Aerial horizontal mobility", ""], ["0x38", "f", "Squall Hammer|Ground horizontal mobility", ""], ["0x3C", "f", "Squall Hammer|Momentum gain from B tap", ""], ["0x40", "f", "Squall Hammer|Minimum control stick X-Axis range required for movement influence", ""], ["0x44", "f", "Squall Hammer|Horizontal wall bounce", ""], ["0x48", "f", "Squall Hammer|Vertical wall bounce", ""], ["0x4C", "f", "Squall Hammer|Solo gravity", ""], ["0x50", "f", "Squall Hammer|Duo gravity", ""], ["0x54", "f", "Squall Hammer|Solo terminal velocity", ""], ["0x58", "f", "Squall Hammer|Duo terminal velocity", ""], ["0x5C", "f", "Squall Hammer|Duration of modified gravity", ""], ["0x60", "f", "Squall Hammer|Uphill friction", ""], ["0x64", "f", "Squall Hammer|Aerial inital horizontal velocity", ""], ["0x68", "I", "Squall Hammer|", ""], ["0x6C", "f", "Squall Hammer|Ground friction", ""], ["0x70", "f", "Squall Hammer|Landing lag / Freefall toggle", ""], ["0x74", "f", "Belay|Belay - Freefall air speed multiplier", "Modifies Climber's MaxAerialHorizontalSpeed attribute"], ["0x78", "f", "Belay|Landing lag", ""], ["0x7C", "f", "Belay|", ""], ["0x80", "f", "Belay|Minimum control stick X-Axis range required for direction control", ""], ["0x84", "f", "Belay|Horizontal velocity deceleration?", "Setting this to 0 causes <PERSON> C<PERSON> to instantly Self-Destruct if horizontal momentum carries over. Crashes the game if no momentum is carried?"], ["0x88", "f", "Belay|", ""], ["0x8C", "f", "Belay|Fall acceleration?", ""], ["0x90", "f", "Belay|", ""], ["0x94", "f", "Belay|Duo positive vertical momentum?", "Higher values = more momentum. Like, way more"], ["0x98", "f", "Belay|Duo negative vertical momentum?", "Higher values = less momentum"], ["0x9C", "f", "Belay|Gravity", ""], ["0xA0", "f", "Belay|Terminal velocity", ""], ["0xA4", "f", "Belay|Solo Player Climber vertical momentum", ""], ["0xA8", "f", "Belay|Solo Player Climber gravity", ""], ["0xAC", "f", "Belay|Solo Player Climber terminal", ""], ["0xB0", "f", "Belay|Air mobility multiplier", ""], ["0xB4", "f", "Belay|Air speed multiplier", ""], ["0xB8", "f", "Blizzard|Delay between shots", "In frames"], ["0xBC", "f", "Blizzard|X-Offset of hitboxes", ""], ["0xC0", "f", "Blizzard|Y-Offset of hitboxes", ""], ["0xC4", "f", "Unknown|", ""], ["0xC8", "f", "", ""], ["0xCC", "f", "", ""], ["0xD0", "f", "CPU Squall Hammer|", ""], ["0xD4", "f", "CPU Squall Hammer|", ""], ["0xD8", "f", "CPU Squall Hammer|", ""], ["0xDC", "f", "CPU Squall Hammer|Height gain from B tap", ""], ["0xE0", "f", "CPU Squall Hammer|Base vertical velocity", ""], ["0xE4", "f", "CPU Squall Hammer|", ""], ["0xE8", "f", "CPU Squall Hammer|Initial horizontal velocity", ""], ["0xEC", "f", "CPU Squall Hammer|Slope angle sensitivity-related", ""], ["0xF0", "f", "CPU Squall Hammer|Aerial horizontal mobility", ""], ["0xF4", "f", "CPU Squall Hammer|Ground horizontal mobility", ""], ["0xF8", "f", "CPU Squall Hammer|Momentum gain from B tap", ""], ["0xFC", "f", "CPU Squall Hammer|Minimum control stick X-Axis range required for movement influence", ""], ["0x100", "f", "CPU Squall Hammer|Horizontal wall bounce", ""], ["0x104", "f", "CPU Squall Hammer|Vertical wall bounce", ""], ["0x108", "f", "CPU Squall Hammer|Gravity", ""], ["0x10C", "f", "CPU Squall Hammer|Duo gravity?", ""], ["0x110", "f", "CPU Squall Hammer|Terminal velocity", ""], ["0x114", "f", "CPU Squall Hammer|Duo terminal velocity?", ""], ["0x118", "f", "CPU Squall Hammer|Duration of modified gravity", ""], ["0x11C", "f", "CPU Squall Hammer|Uphill friction", ""], ["0x120", "f", "CPU Squall Hammer|Aerial initial horizontal velocity", ""], ["0x124", "I", "CPU Squall Hammer|", ""], ["0x128", "f", "CPU Squall Hammer|Traction", ""], ["0x12C", "f", "CPU Squall Hammer|Landing lag / Freefall toggle", ""], ["0x130", "f", "CPU Belay|Freefall air speed multiplier", "Modifies Climber’s MaxAerialHorizontalSpeed attribute"], ["0x134", "f", "CPU Belay|Landing lag", ""], ["0x138", "f", "CPU Belay|Minimum control stick X-Axis range required for direction control", ""], ["0x13C", "f", "CPU Belay|Horizontal velocity deceleration?", "Setting this to 0 causes CPU Climber to instantly Self-Destruct if horizontal momentum carries over. Crashes the game if no momentum is carried?"], ["0x140", "f", "CPU Belay|Surface sliding decision", "Surface angle to determine whether to stop or to continue sliding? (in radians)"], ["0x144", "f", "CPU Belay|Gravity", ""], ["0x148", "f", "CPU Belay|Terminal velocity", ""], ["0x14C", "f", "CPU Belay|Vertical momentum", ""], ["0x150", "f", "Unknown|", ""], ["0x154", "f", "", ""], ["0x158", "f", "", ""]], "Nn": [["-1", "character", "<PERSON>", ""], ["0x0", "f", "Spawn horizontal offset", "Only applies to Player Climber, even though it also exists in CPU Climber's array"], ["0x4", "f", "Ice Shot|Aerial vertical momentum", ""], ["0x8", "f", "Ice Shot|Landing lag", ""], ["0xC", "f", "Ice Shot|Ice Shot spawn X-Offset", ""], ["0x10", "f", "Ice Shot|Ice Shot spawn Y-Offset", ""], ["0x14", "f", "Unknown|", ""], ["0x18", "f", "", ""], ["0x1C", "f", "", ""], ["0x20", "f", "Squall Hammer|Height gain from B tap", ""], ["0x24", "f", "Squall Hammer|Base vertical velocity", ""], ["0x28", "f", "Squall Hammer|", ""], ["0x2C", "f", "Squall Hammer|Initial horizontal velocity/Horizontal warp?", ""], ["0x30", "f", "Squall Hammer|Slope angle sensitivity-related", ""], ["0x34", "f", "Squall Hammer|Aerial horizontal mobility", ""], ["0x38", "f", "Squall Hammer|Ground horizontal mobility", ""], ["0x3C", "f", "Squall Hammer|Momentum gain from B tap", ""], ["0x40", "f", "Squall Hammer|Minimum control stick X-Axis range required for movement influence", ""], ["0x44", "f", "Squall Hammer|Horizontal wall bounce", ""], ["0x48", "f", "Squall Hammer|Vertical wall bounce", ""], ["0x4C", "f", "Squall Hammer|Solo gravity", ""], ["0x50", "f", "Squall Hammer|Duo gravity", ""], ["0x54", "f", "Squall Hammer|Solo terminal velocity", ""], ["0x58", "f", "Squall Hammer|Duo terminal velocity", ""], ["0x5C", "f", "Squall Hammer|Duration of modified gravity", ""], ["0x60", "f", "Squall Hammer|Uphill friction", ""], ["0x64", "f", "Squall Hammer|Aerial inital horizontal velocity", ""], ["0x68", "I", "Squall Hammer|", ""], ["0x6C", "f", "Squall Hammer|Ground friction", ""], ["0x70", "f", "Squall Hammer|Landing lag / Freefall toggle", ""], ["0x74", "f", "Belay|Belay - Freefall air speed multiplier", "Modifies Climber's MaxAerialHorizontalSpeed attribute"], ["0x78", "f", "Belay|Landing lag", ""], ["0x7C", "f", "Belay|", ""], ["0x80", "f", "Belay|Minimum control stick X-Axis range required for direction control", ""], ["0x84", "f", "Belay|Horizontal velocity deceleration?", "Setting this to 0 causes <PERSON> C<PERSON> to instantly Self-Destruct if horizontal momentum carries over. Crashes the game if no momentum is carried?"], ["0x88", "f", "Belay|", ""], ["0x8C", "f", "Belay|Fall acceleration?", ""], ["0x90", "f", "Belay|", ""], ["0x94", "f", "Belay|Duo positive vertical momentum?", "Higher values = more momentum. Like, way more"], ["0x98", "f", "Belay|Duo negative vertical momentum?", "Higher values = less momentum"], ["0x9C", "f", "Belay|Gravity", ""], ["0xA0", "f", "Belay|Terminal velocity", ""], ["0xA4", "f", "Belay|Solo Player Climber vertical momentum", ""], ["0xA8", "f", "Belay|Solo Player Climber gravity", ""], ["0xAC", "f", "Belay|Solo Player Climber terminal", ""], ["0xB0", "f", "Belay|Air mobility multiplier", ""], ["0xB4", "f", "Belay|Air speed multiplier", ""], ["0xB8", "f", "Blizzard|Delay between shots", "In frames"], ["0xBC", "f", "Blizzard|X-Offset of hitboxes", ""], ["0xC0", "f", "Blizzard|Y-Offset of hitboxes", ""], ["0xC4", "f", "Unknown|", ""], ["0xC8", "f", "", ""], ["0xCC", "f", "", ""], ["0xD0", "f", "CPU Squall Hammer|", ""], ["0xD4", "f", "CPU Squall Hammer|", ""], ["0xD8", "f", "CPU Squall Hammer|", ""], ["0xDC", "f", "CPU Squall Hammer|Height gain from B tap", ""], ["0xE0", "f", "CPU Squall Hammer|Base vertical velocity", ""], ["0xE4", "f", "CPU Squall Hammer|", ""], ["0xE8", "f", "CPU Squall Hammer|Initial horizontal velocity", ""], ["0xEC", "f", "CPU Squall Hammer|Slope angle sensitivity-related", ""], ["0xF0", "f", "CPU Squall Hammer|Aerial horizontal mobility", ""], ["0xF4", "f", "CPU Squall Hammer|Ground horizontal mobility", ""], ["0xF8", "f", "CPU Squall Hammer|Momentum gain from B tap", ""], ["0xFC", "f", "CPU Squall Hammer|Minimum control stick X-Axis range required for movement influence", ""], ["0x100", "f", "CPU Squall Hammer|Horizontal wall bounce", ""], ["0x104", "f", "CPU Squall Hammer|Vertical wall bounce", ""], ["0x108", "f", "CPU Squall Hammer|Gravity", ""], ["0x10C", "f", "CPU Squall Hammer|Duo gravity?", ""], ["0x110", "f", "CPU Squall Hammer|Terminal velocity", ""], ["0x114", "f", "CPU Squall Hammer|Duo terminal velocity?", ""], ["0x118", "f", "CPU Squall Hammer|Duration of modified gravity", ""], ["0x11C", "f", "CPU Squall Hammer|Uphill friction", ""], ["0x120", "f", "CPU Squall Hammer|Aerial initial horizontal velocity", ""], ["0x124", "I", "CPU Squall Hammer|", ""], ["0x128", "f", "CPU Squall Hammer|Traction", ""], ["0x12C", "f", "CPU Squall Hammer|Landing lag / Freefall toggle", ""], ["0x130", "f", "CPU Belay|Freefall air speed multiplier", "Modifies Climber’s MaxAerialHorizontalSpeed attribute"], ["0x134", "f", "CPU Belay|Landing lag", ""], ["0x138", "f", "CPU Belay|Minimum control stick X-Axis range required for direction control", ""], ["0x13C", "f", "CPU Belay|Horizontal velocity deceleration?", "Setting this to 0 causes CPU Climber to instantly Self-Destruct if horizontal momentum carries over. Crashes the game if no momentum is carried?"], ["0x140", "f", "CPU Belay|Surface sliding decision", "Surface angle to determine whether to stop or to continue sliding? (in radians)"], ["0x144", "f", "CPU Belay|Gravity", ""], ["0x148", "f", "CPU Belay|Terminal velocity", ""], ["0x14C", "f", "CPU Belay|Vertical momentum", ""], ["0x150", "f", "Unknown|", ""], ["0x154", "f", "", ""], ["0x158", "f", "", ""]]}}