------------------------------------------------------------------------
-- reduce.decTest -- remove trailing zeros                            --
-- Copyright (c) IBM Corporation, 2003, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
-- [This used to be called normalize.]

version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 999
minexponent: -999

redx001 reduce '1'      -> '1'
redx002 reduce '-1'     -> '-1'
redx003 reduce '1.00'   -> '1'
redx004 reduce '-1.00'  -> '-1'
redx005 reduce '0'      -> '0'
redx006 reduce '0.00'   -> '0'
redx007 reduce '00.0'   -> '0'
redx008 reduce '00.00'  -> '0'
redx009 reduce '00'     -> '0'
redx010 reduce '0E+1'   -> '0'
redx011 reduce '0E+5'   -> '0'

redx012 reduce '-2'     -> '-2'
redx013 reduce '2'      -> '2'
redx014 reduce '-2.00'  -> '-2'
redx015 reduce '2.00'   -> '2'
redx016 reduce '-0'     -> '-0'
redx017 reduce '-0.00'  -> '-0'
redx018 reduce '-00.0'  -> '-0'
redx019 reduce '-00.00' -> '-0'
redx020 reduce '-00'    -> '-0'
redx021 reduce '-0E+5'   -> '-0'
redx022 reduce '-0E+1'  -> '-0'

redx030 reduce '+0.1'            -> '0.1'
redx031 reduce '-0.1'            -> '-0.1'
redx032 reduce '+0.01'           -> '0.01'
redx033 reduce '-0.01'           -> '-0.01'
redx034 reduce '+0.001'          -> '0.001'
redx035 reduce '-0.001'          -> '-0.001'
redx036 reduce '+0.000001'       -> '0.000001'
redx037 reduce '-0.000001'       -> '-0.000001'
redx038 reduce '+0.000000000001' -> '1E-12'
redx039 reduce '-0.000000000001' -> '-1E-12'

redx041 reduce 1.1        -> 1.1
redx042 reduce 1.10       -> 1.1
redx043 reduce 1.100      -> 1.1
redx044 reduce 1.110      -> 1.11
redx045 reduce -1.1       -> -1.1
redx046 reduce -1.10      -> -1.1
redx047 reduce -1.100     -> -1.1
redx048 reduce -1.110     -> -1.11
redx049 reduce 9.9        -> 9.9
redx050 reduce 9.90       -> 9.9
redx051 reduce 9.900      -> 9.9
redx052 reduce 9.990      -> 9.99
redx053 reduce -9.9       -> -9.9
redx054 reduce -9.90      -> -9.9
redx055 reduce -9.900     -> -9.9
redx056 reduce -9.990     -> -9.99

-- some trailing fractional zeros with zeros in units
redx060 reduce  10.0        -> 1E+1
redx061 reduce  10.00       -> 1E+1
redx062 reduce  100.0       -> 1E+2
redx063 reduce  100.00      -> 1E+2
redx064 reduce  1.1000E+3   -> 1.1E+3
redx065 reduce  1.10000E+3  -> 1.1E+3
redx066 reduce -10.0        -> -1E+1
redx067 reduce -10.00       -> -1E+1
redx068 reduce -100.0       -> -1E+2
redx069 reduce -100.00      -> -1E+2
redx070 reduce -1.1000E+3   -> -1.1E+3
redx071 reduce -1.10000E+3  -> -1.1E+3

-- some insignificant trailing zeros with positive exponent
redx080 reduce  10E+1       -> 1E+2
redx081 reduce  100E+1      -> 1E+3
redx082 reduce  1.0E+2      -> 1E+2
redx083 reduce  1.0E+3      -> 1E+3
redx084 reduce  1.1E+3      -> 1.1E+3
redx085 reduce  1.00E+3     -> 1E+3
redx086 reduce  1.10E+3     -> 1.1E+3
redx087 reduce -10E+1       -> -1E+2
redx088 reduce -100E+1      -> -1E+3
redx089 reduce -1.0E+2      -> -1E+2
redx090 reduce -1.0E+3      -> -1E+3
redx091 reduce -1.1E+3      -> -1.1E+3
redx092 reduce -1.00E+3     -> -1E+3
redx093 reduce -1.10E+3     -> -1.1E+3

-- some significant trailing zeros, were we to be trimming
redx100 reduce  11          -> 11
redx101 reduce  10          -> 1E+1
redx102 reduce  10.         -> 1E+1
redx103 reduce  1.1E+1      -> 11
redx104 reduce  1.0E+1      -> 1E+1
redx105 reduce  1.10E+2     -> 1.1E+2
redx106 reduce  1.00E+2     -> 1E+2
redx107 reduce  1.100E+3    -> 1.1E+3
redx108 reduce  1.000E+3    -> 1E+3
redx109 reduce  1.000000E+6 -> 1E+6
redx110 reduce -11          -> -11
redx111 reduce -10          -> -1E+1
redx112 reduce -10.         -> -1E+1
redx113 reduce -1.1E+1      -> -11
redx114 reduce -1.0E+1      -> -1E+1
redx115 reduce -1.10E+2     -> -1.1E+2
redx116 reduce -1.00E+2     -> -1E+2
redx117 reduce -1.100E+3    -> -1.1E+3
redx118 reduce -1.000E+3    -> -1E+3
redx119 reduce -1.00000E+5  -> -1E+5
redx120 reduce -1.000000E+6 -> -1E+6
redx121 reduce -10.00000E+6 -> -1E+7
redx122 reduce -100.0000E+6 -> -1E+8
redx123 reduce -1000.000E+6 -> -1E+9
redx124 reduce -10000.00E+6 -> -1E+10
redx125 reduce -100000.0E+6 -> -1E+11
redx126 reduce -1000000.E+6 -> -1E+12

-- examples from decArith
redx140 reduce '2.1'     ->  '2.1'
redx141 reduce '-2.0'    ->  '-2'
redx142 reduce '1.200'   ->  '1.2'
redx143 reduce '-120'    ->  '-1.2E+2'
redx144 reduce '120.00'  ->  '1.2E+2'
redx145 reduce '0.00'    ->  '0'

-- overflow tests
maxexponent: 999999999
minexponent: -999999999
precision: 3
redx160 reduce 9.999E+999999999  ->  Infinity Inexact Overflow Rounded
redx161 reduce -9.999E+999999999 -> -Infinity Inexact Overflow Rounded

-- subnormals and underflow
precision: 3
maxexponent: 999
minexponent: -999
redx210 reduce  1.00E-999        ->   1E-999
redx211 reduce  0.1E-999         ->   1E-1000   Subnormal
redx212 reduce  0.10E-999        ->   1E-1000   Subnormal
redx213 reduce  0.100E-999       ->   1E-1000   Subnormal Rounded
redx214 reduce  0.01E-999        ->   1E-1001   Subnormal
-- next is rounded to Emin
redx215 reduce  0.999E-999       ->   1E-999    Inexact Rounded Subnormal Underflow
redx216 reduce  0.099E-999       ->   1E-1000   Inexact Rounded Subnormal Underflow
redx217 reduce  0.009E-999       ->   1E-1001   Inexact Rounded Subnormal Underflow
redx218 reduce  0.001E-999       ->   0         Inexact Rounded Subnormal Underflow Clamped
redx219 reduce  0.0009E-999      ->   0         Inexact Rounded Subnormal Underflow Clamped
redx220 reduce  0.0001E-999      ->   0         Inexact Rounded Subnormal Underflow Clamped

redx230 reduce -1.00E-999        ->  -1E-999
redx231 reduce -0.1E-999         ->  -1E-1000   Subnormal
redx232 reduce -0.10E-999        ->  -1E-1000   Subnormal
redx233 reduce -0.100E-999       ->  -1E-1000   Subnormal Rounded
redx234 reduce -0.01E-999        ->  -1E-1001   Subnormal
-- next is rounded to Emin
redx235 reduce -0.999E-999       ->  -1E-999    Inexact Rounded Subnormal Underflow
redx236 reduce -0.099E-999       ->  -1E-1000   Inexact Rounded Subnormal Underflow
redx237 reduce -0.009E-999       ->  -1E-1001   Inexact Rounded Subnormal Underflow
redx238 reduce -0.001E-999       ->  -0         Inexact Rounded Subnormal Underflow Clamped
redx239 reduce -0.0009E-999      ->  -0         Inexact Rounded Subnormal Underflow Clamped
redx240 reduce -0.0001E-999      ->  -0         Inexact Rounded Subnormal Underflow Clamped

-- more reshaping
precision: 9
redx260 reduce '56260E-10'   -> '0.000005626'
redx261 reduce '56260E-5'    -> '0.5626'
redx262 reduce '56260E-2'    -> '562.6'
redx263 reduce '56260E-1'    -> '5626'
redx265 reduce '56260E-0'    -> '5.626E+4'
redx266 reduce '56260E+0'    -> '5.626E+4'
redx267 reduce '56260E+1'    -> '5.626E+5'
redx268 reduce '56260E+2'    -> '5.626E+6'
redx269 reduce '56260E+3'    -> '5.626E+7'
redx270 reduce '56260E+4'    -> '5.626E+8'
redx271 reduce '56260E+5'    -> '5.626E+9'
redx272 reduce '56260E+6'    -> '5.626E+10'
redx280 reduce '-56260E-10'  -> '-0.000005626'
redx281 reduce '-56260E-5'   -> '-0.5626'
redx282 reduce '-56260E-2'   -> '-562.6'
redx283 reduce '-56260E-1'   -> '-5626'
redx285 reduce '-56260E-0'   -> '-5.626E+4'
redx286 reduce '-56260E+0'   -> '-5.626E+4'
redx287 reduce '-56260E+1'   -> '-5.626E+5'
redx288 reduce '-56260E+2'   -> '-5.626E+6'
redx289 reduce '-56260E+3'   -> '-5.626E+7'
redx290 reduce '-56260E+4'   -> '-5.626E+8'
redx291 reduce '-56260E+5'   -> '-5.626E+9'
redx292 reduce '-56260E+6'   -> '-5.626E+10'

-- FL test
precision: 40
redx295 reduce 9892345673.0123456780000000000 -> 9892345673.012345678

-- specials
redx820 reduce 'Inf'    -> 'Infinity'
redx821 reduce '-Inf'   -> '-Infinity'
redx822 reduce   NaN    ->  NaN
redx823 reduce  sNaN    ->  NaN    Invalid_operation
redx824 reduce   NaN101 ->  NaN101
redx825 reduce  sNaN010 ->  NaN10  Invalid_operation
redx827 reduce  -NaN    -> -NaN
redx828 reduce -sNaN    -> -NaN    Invalid_operation
redx829 reduce  -NaN101 -> -NaN101
redx830 reduce -sNaN010 -> -NaN10  Invalid_operation

-- payload decapitate
precision: 5
redx62100 reduce  sNaN1234567890 -> NaN67890  Invalid_operation

-- Null test
redx900 reduce  # -> NaN Invalid_operation
