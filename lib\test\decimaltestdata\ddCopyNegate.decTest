------------------------------------------------------------------------
-- ddCopyNegate.decTest -- quiet decDouble copy and negate            --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decDoubles.
precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- Sanity check
ddcpn001 copynegate       +7.50  -> -7.50

-- Infinities
ddcpn011 copynegate  Infinity    -> -Infinity
ddcpn012 copynegate  -Infinity   -> Infinity

-- NaNs, 0 payload
ddcpn021 copynegate         NaN  -> -NaN
ddcpn022 copynegate        -NaN  -> NaN
ddcpn023 copynegate        sNaN  -> -sNaN
ddcpn024 copynegate       -sNaN  -> sNaN

-- NaNs, non-0 payload
ddcpn031 copynegate       NaN13  -> -NaN13
ddcpn032 copynegate      -NaN13  -> NaN13
ddcpn033 copynegate      sNaN13  -> -sNaN13
ddcpn034 copynegate     -sNaN13  -> sNaN13
ddcpn035 copynegate       NaN70  -> -NaN70
ddcpn036 copynegate      -NaN70  -> NaN70
ddcpn037 copynegate      sNaN101 -> -sNaN101
ddcpn038 copynegate     -sNaN101 -> sNaN101

-- finites
ddcpn101 copynegate          7   -> -7
ddcpn102 copynegate         -7   -> 7
ddcpn103 copynegate         75   -> -75
ddcpn104 copynegate        -75   -> 75
ddcpn105 copynegate       7.50   -> -7.50
ddcpn106 copynegate      -7.50   -> 7.50
ddcpn107 copynegate       7.500  -> -7.500
ddcpn108 copynegate      -7.500  -> 7.500

-- zeros
ddcpn111 copynegate          0   -> -0
ddcpn112 copynegate         -0   -> 0
ddcpn113 copynegate       0E+4   -> -0E+4
ddcpn114 copynegate      -0E+4   -> 0E+4
ddcpn115 copynegate     0.0000   -> -0.0000
ddcpn116 copynegate    -0.0000   -> 0.0000
ddcpn117 copynegate      0E-141  -> -0E-141
ddcpn118 copynegate     -0E-141  -> 0E-141

-- full coefficients, alternating bits
ddcpn121 copynegate  2682682682682682         -> -2682682682682682
ddcpn122 copynegate  -2682682682682682        -> 2682682682682682
ddcpn123 copynegate  1341341341341341         -> -1341341341341341
ddcpn124 copynegate  -1341341341341341        -> 1341341341341341

-- Nmax, Nmin, Ntiny
ddcpn131 copynegate  9.999999999999999E+384   -> -9.999999999999999E+384
ddcpn132 copynegate  1E-383                   -> -1E-383
ddcpn133 copynegate  1.000000000000000E-383   -> -1.000000000000000E-383
ddcpn134 copynegate  1E-398                   -> -1E-398

ddcpn135 copynegate  -1E-398                  -> 1E-398
ddcpn136 copynegate  -1.000000000000000E-383  -> 1.000000000000000E-383
ddcpn137 copynegate  -1E-383                  -> 1E-383
ddcpn138 copynegate  -9.999999999999999E+384  -> 9.999999999999999E+384
