------------------------------------------------------------------------
-- ddMin.decTest -- decDouble minnum                                  --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- we assume that base comparison is tested in compare.decTest, so
-- these mainly cover special cases and rounding
precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- sanity checks
ddmin001 min  -2  -2  -> -2
ddmin002 min  -2  -1  -> -2
ddmin003 min  -2   0  -> -2
ddmin004 min  -2   1  -> -2
ddmin005 min  -2   2  -> -2
ddmin006 min  -1  -2  -> -2
ddmin007 min  -1  -1  -> -1
ddmin008 min  -1   0  -> -1
ddmin009 min  -1   1  -> -1
ddmin010 min  -1   2  -> -1
ddmin011 min   0  -2  -> -2
ddmin012 min   0  -1  -> -1
ddmin013 min   0   0  ->  0
ddmin014 min   0   1  ->  0
ddmin015 min   0   2  ->  0
ddmin016 min   1  -2  -> -2
ddmin017 min   1  -1  -> -1
ddmin018 min   1   0  ->  0
ddmin019 min   1   1  ->  1
ddmin020 min   1   2  ->  1
ddmin021 min   2  -2  -> -2
ddmin022 min   2  -1  -> -1
ddmin023 min   2   0  ->  0
ddmin025 min   2   1  ->  1
ddmin026 min   2   2  ->  2

-- extended zeros
ddmin030 min   0     0   ->  0
ddmin031 min   0    -0   -> -0
ddmin032 min   0    -0.0 -> -0.0
ddmin033 min   0     0.0 ->  0.0
ddmin034 min  -0     0   -> -0
ddmin035 min  -0    -0   -> -0
ddmin036 min  -0    -0.0 -> -0
ddmin037 min  -0     0.0 -> -0
ddmin038 min   0.0   0   ->  0.0
ddmin039 min   0.0  -0   -> -0
ddmin040 min   0.0  -0.0 -> -0.0
ddmin041 min   0.0   0.0 ->  0.0
ddmin042 min  -0.0   0   -> -0.0
ddmin043 min  -0.0  -0   -> -0
ddmin044 min  -0.0  -0.0 -> -0.0
ddmin045 min  -0.0   0.0 -> -0.0

ddmin046 min   0E1  -0E1 -> -0E+1
ddmin047 min  -0E1   0E2 -> -0E+1
ddmin048 min   0E2   0E1 ->  0E+1
ddmin049 min   0E1   0E2 ->  0E+1
ddmin050 min  -0E3  -0E2 -> -0E+3
ddmin051 min  -0E2  -0E3 -> -0E+3

-- Specials
ddmin090 min  Inf  -Inf   -> -Infinity
ddmin091 min  Inf  -1000  -> -1000
ddmin092 min  Inf  -1     -> -1
ddmin093 min  Inf  -0     -> -0
ddmin094 min  Inf   0     ->  0
ddmin095 min  Inf   1     ->  1
ddmin096 min  Inf   1000  ->  1000
ddmin097 min  Inf   Inf   ->  Infinity
ddmin098 min -1000  Inf   -> -1000
ddmin099 min -Inf   Inf   -> -Infinity
ddmin100 min -1     Inf   -> -1
ddmin101 min -0     Inf   -> -0
ddmin102 min  0     Inf   ->  0
ddmin103 min  1     Inf   ->  1
ddmin104 min  1000  Inf   ->  1000
ddmin105 min  Inf   Inf   ->  Infinity

ddmin120 min -Inf  -Inf   -> -Infinity
ddmin121 min -Inf  -1000  -> -Infinity
ddmin122 min -Inf  -1     -> -Infinity
ddmin123 min -Inf  -0     -> -Infinity
ddmin124 min -Inf   0     -> -Infinity
ddmin125 min -Inf   1     -> -Infinity
ddmin126 min -Inf   1000  -> -Infinity
ddmin127 min -Inf   Inf   -> -Infinity
ddmin128 min -Inf  -Inf   -> -Infinity
ddmin129 min -1000 -Inf   -> -Infinity
ddmin130 min -1    -Inf   -> -Infinity
ddmin131 min -0    -Inf   -> -Infinity
ddmin132 min  0    -Inf   -> -Infinity
ddmin133 min  1    -Inf   -> -Infinity
ddmin134 min  1000 -Inf   -> -Infinity
ddmin135 min  Inf  -Inf   -> -Infinity

-- 2004.08.02 754r chooses number over NaN in mixed cases
ddmin141 min  NaN -Inf    ->  -Infinity
ddmin142 min  NaN -1000   ->  -1000
ddmin143 min  NaN -1      ->  -1
ddmin144 min  NaN -0      ->  -0
ddmin145 min  NaN  0      ->  0
ddmin146 min  NaN  1      ->  1
ddmin147 min  NaN  1000   ->  1000
ddmin148 min  NaN  Inf    ->  Infinity
ddmin149 min  NaN  NaN    ->  NaN
ddmin150 min -Inf  NaN    -> -Infinity
ddmin151 min -1000 NaN    -> -1000
ddmin152 min -1   -NaN    -> -1
ddmin153 min -0    NaN    -> -0
ddmin154 min  0   -NaN    ->  0
ddmin155 min  1    NaN    ->  1
ddmin156 min  1000 NaN    ->  1000
ddmin157 min  Inf  NaN    ->  Infinity

ddmin161 min  sNaN -Inf   ->  NaN  Invalid_operation
ddmin162 min  sNaN -1000  ->  NaN  Invalid_operation
ddmin163 min  sNaN -1     ->  NaN  Invalid_operation
ddmin164 min  sNaN -0     ->  NaN  Invalid_operation
ddmin165 min -sNaN  0     -> -NaN  Invalid_operation
ddmin166 min -sNaN  1     -> -NaN  Invalid_operation
ddmin167 min  sNaN  1000  ->  NaN  Invalid_operation
ddmin168 min  sNaN  NaN   ->  NaN  Invalid_operation
ddmin169 min  sNaN sNaN   ->  NaN  Invalid_operation
ddmin170 min  NaN  sNaN   ->  NaN  Invalid_operation
ddmin171 min -Inf  sNaN   ->  NaN  Invalid_operation
ddmin172 min -1000 sNaN   ->  NaN  Invalid_operation
ddmin173 min -1    sNaN   ->  NaN  Invalid_operation
ddmin174 min -0    sNaN   ->  NaN  Invalid_operation
ddmin175 min  0    sNaN   ->  NaN  Invalid_operation
ddmin176 min  1    sNaN   ->  NaN  Invalid_operation
ddmin177 min  1000 sNaN   ->  NaN  Invalid_operation
ddmin178 min  Inf  sNaN   ->  NaN  Invalid_operation
ddmin179 min  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
ddmin181 min  NaN9   -Inf   -> -Infinity
ddmin182 min -NaN8    9990  ->  9990
ddmin183 min  NaN71   Inf   ->  Infinity

ddmin184 min  NaN1    NaN54 ->  NaN1
ddmin185 min  NaN22  -NaN53 ->  NaN22
ddmin186 min -NaN3    NaN6  -> -NaN3
ddmin187 min -NaN44   NaN7  -> -NaN44

ddmin188 min -Inf     NaN41 -> -Infinity
ddmin189 min -9999   -NaN33 -> -9999
ddmin190 min  Inf     NaN2  ->  Infinity

ddmin191 min  sNaN99 -Inf    ->  NaN99 Invalid_operation
ddmin192 min  sNaN98 -11     ->  NaN98 Invalid_operation
ddmin193 min -sNaN97  NaN8   -> -NaN97 Invalid_operation
ddmin194 min  sNaN69 sNaN94  ->  NaN69 Invalid_operation
ddmin195 min  NaN95  sNaN93  ->  NaN93 Invalid_operation
ddmin196 min -Inf    sNaN92  ->  NaN92 Invalid_operation
ddmin197 min  088    sNaN91  ->  NaN91 Invalid_operation
ddmin198 min  Inf   -sNaN90  -> -NaN90 Invalid_operation
ddmin199 min  NaN    sNaN86  ->  NaN86 Invalid_operation

-- old rounding checks
ddmin221 min -12345678000 1  -> -12345678000
ddmin222 min 1 -12345678000  -> -12345678000
ddmin223 min -1234567800  1  -> -1234567800
ddmin224 min 1 -1234567800   -> -1234567800
ddmin225 min -1234567890  1  -> -1234567890
ddmin226 min 1 -1234567890   -> -1234567890
ddmin227 min -1234567891  1  -> -1234567891
ddmin228 min 1 -1234567891   -> -1234567891
ddmin229 min -12345678901 1  -> -12345678901
ddmin230 min 1 -12345678901  -> -12345678901
ddmin231 min -1234567896  1  -> -1234567896
ddmin232 min 1 -1234567896   -> -1234567896
ddmin233 min 1234567891  1   -> 1
ddmin234 min 1 1234567891    -> 1
ddmin235 min 12345678901 1   -> 1
ddmin236 min 1 12345678901   -> 1
ddmin237 min 1234567896  1   -> 1
ddmin238 min 1 1234567896    -> 1

-- from examples
ddmin280 min '3'   '2'  ->  '2'
ddmin281 min '-10' '3'  ->  '-10'
ddmin282 min '1.0' '1'  ->  '1.0'
ddmin283 min '1' '1.0'  ->  '1.0'
ddmin284 min '7' 'NaN'  ->  '7'

-- expanded list from min/max 754r purple prose
-- [explicit tests for exponent ordering]
ddmin401 min  Inf    1.1     ->  1.1
ddmin402 min  1.1    1       ->  1
ddmin403 min  1      1.0     ->  1.0
ddmin404 min  1.0    0.1     ->  0.1
ddmin405 min  0.1    0.10    ->  0.10
ddmin406 min  0.10   0.100   ->  0.100
ddmin407 min  0.10   0       ->  0
ddmin408 min  0      0.0     ->  0.0
ddmin409 min  0.0   -0       -> -0
ddmin410 min  0.0   -0.0     -> -0.0
ddmin411 min  0.00  -0.0     -> -0.0
ddmin412 min  0.0   -0.00    -> -0.00
ddmin413 min  0     -0.0     -> -0.0
ddmin414 min  0     -0       -> -0
ddmin415 min -0.0   -0       -> -0
ddmin416 min -0     -0.100   -> -0.100
ddmin417 min -0.100 -0.10    -> -0.10
ddmin418 min -0.10  -0.1     -> -0.1
ddmin419 min -0.1   -1.0     -> -1.0
ddmin420 min -1.0   -1       -> -1
ddmin421 min -1     -1.1     -> -1.1
ddmin423 min -1.1   -Inf     -> -Infinity
-- same with operands reversed
ddmin431 min  1.1    Inf     ->  1.1
ddmin432 min  1      1.1     ->  1
ddmin433 min  1.0    1       ->  1.0
ddmin434 min  0.1    1.0     ->  0.1
ddmin435 min  0.10   0.1     ->  0.10
ddmin436 min  0.100  0.10    ->  0.100
ddmin437 min  0      0.10    ->  0
ddmin438 min  0.0    0       ->  0.0
ddmin439 min -0      0.0     -> -0
ddmin440 min -0.0    0.0     -> -0.0
ddmin441 min -0.0    0.00    -> -0.0
ddmin442 min -0.00   0.0     -> -0.00
ddmin443 min -0.0    0       -> -0.0
ddmin444 min -0      0       -> -0
ddmin445 min -0     -0.0     -> -0
ddmin446 min -0.100 -0       -> -0.100
ddmin447 min -0.10  -0.100   -> -0.10
ddmin448 min -0.1   -0.10    -> -0.1
ddmin449 min -1.0   -0.1     -> -1.0
ddmin450 min -1     -1.0     -> -1
ddmin451 min -1.1   -1       -> -1.1
ddmin453 min -Inf   -1.1     -> -Infinity
-- largies
ddmin460 min  1000   1E+3    ->  1000
ddmin461 min  1E+3   1000    ->  1000
ddmin462 min  1000  -1E+3    -> -1E+3
ddmin463 min  1E+3  -384    -> -384
ddmin464 min -384   1E+3    -> -384
ddmin465 min -1E+3   1000    -> -1E+3
ddmin466 min -384  -1E+3    -> -1E+3
ddmin467 min -1E+3  -384    -> -1E+3

-- misalignment traps for little-endian
ddmin471 min      1.0       0.1  -> 0.1
ddmin472 min      0.1       1.0  -> 0.1
ddmin473 min     10.0       0.1  -> 0.1
ddmin474 min      0.1      10.0  -> 0.1
ddmin475 min      100       1.0  -> 1.0
ddmin476 min      1.0       100  -> 1.0
ddmin477 min     1000      10.0  -> 10.0
ddmin478 min     10.0      1000  -> 10.0
ddmin479 min    10000     100.0  -> 100.0
ddmin480 min    100.0     10000  -> 100.0
ddmin481 min   100000    1000.0  -> 1000.0
ddmin482 min   1000.0    100000  -> 1000.0
ddmin483 min  1000000   10000.0  -> 10000.0
ddmin484 min  10000.0   1000000  -> 10000.0

-- subnormals
ddmin510 min  1.00E-383       0  ->   0
ddmin511 min  0.1E-383        0  ->   0
ddmin512 min  0.10E-383       0  ->   0
ddmin513 min  0.100E-383      0  ->   0
ddmin514 min  0.01E-383       0  ->   0
ddmin515 min  0.999E-383      0  ->   0
ddmin516 min  0.099E-383      0  ->   0
ddmin517 min  0.009E-383      0  ->   0
ddmin518 min  0.001E-383      0  ->   0
ddmin519 min  0.0009E-383     0  ->   0
ddmin520 min  0.0001E-383     0  ->   0

ddmin530 min -1.00E-383       0  ->  -1.00E-383
ddmin531 min -0.1E-383        0  ->  -1E-384    Subnormal
ddmin532 min -0.10E-383       0  ->  -1.0E-384  Subnormal
ddmin533 min -0.100E-383      0  ->  -1.00E-384 Subnormal
ddmin534 min -0.01E-383       0  ->  -1E-385    Subnormal
ddmin535 min -0.999E-383      0  ->  -9.99E-384 Subnormal
ddmin536 min -0.099E-383      0  ->  -9.9E-385  Subnormal
ddmin537 min -0.009E-383      0  ->  -9E-386    Subnormal
ddmin538 min -0.001E-383      0  ->  -1E-386    Subnormal
ddmin539 min -0.0009E-383     0  ->  -9E-387    Subnormal
ddmin540 min -0.0001E-383     0  ->  -1E-387    Subnormal


-- Null tests
ddmin900 min 10  # -> NaN Invalid_operation
ddmin901 min  # 10 -> NaN Invalid_operation
