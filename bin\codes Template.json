{"codes": [{"name": "", "authors": ["??"], "description": [""], "webLinks": [["https://www.youtube.com/watch?v=19JYOVPmwDM", "This is a comment."], ["https://github.com/DRGN-DRC/DAT-Texture-Wizard", "GitHub link to DTW."]], "build": [{"type": "replace", "address": "80001234", "value": "3800000E", "annotation": "An example 4-byte static overwrite"}, {"type": "inject", "address": "80001234", "sourceFile": "InjectCode.asm", "annotation": "Example injection"}, {"type": "injectFolder", "sourceFolder": ".\\", "isRecursive": true}, {"type": "replaceCodeBlock", "address": "", "sourceFile": "\\fileName.s", "annotation": ""}, {"type": "binary", "sourceFile": "..\\ASM\\Additional Codes\\PRIM LITE\\main.bin", "annotation": "PRIM LITE [Punkline]"}]}]}