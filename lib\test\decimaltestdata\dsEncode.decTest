------------------------------------------------------------------------
-- dsEncode.decTest -- decimal four-byte format testcases             --
-- Copyright (c) IBM Corporation, 2000, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
-- [Previously called decimal32.decTest]
version: 2.59

-- This set of tests is for the four-byte concrete representation.
-- Its characteristics are:
--
--  1 bit  sign
--  5 bits combination field
--  6 bits exponent continuation
-- 20 bits coefficient continuation
--
-- Total exponent length 8 bits
-- Total coefficient length 24 bits (7 digits)
--
-- Elimit =  191 (maximum encoded exponent)
-- Emax   =   96 (largest exponent value)
-- Emin   =  -95 (smallest exponent value)
-- bias   =  101 (subtracted from encoded exponent) = -Etiny

-- The testcases here have only exactly representable data on the
-- 'left-hand-side'; rounding from strings is tested in 'base'
-- testcase groups.

extended:    1
clamp:       1
precision:   7
rounding:    half_up
maxExponent: 96
minExponent: -95

-- General testcases
-- (mostly derived from the Strawman 4 document and examples)
decs001 apply   #A23003D0          -> -7.50
decs002 apply   -7.50              -> #A23003D0
-- derivative canonical plain strings
decs003 apply   #A26003D0         -> -7.50E+3
decs004 apply   -7.50E+3          -> #A26003D0
decs005 apply   #A25003D0         -> -750
decs006 apply   -750              -> #A25003D0
decs007 apply   #A24003D0         -> -75.0
decs008 apply   -75.0             -> #A24003D0
decs009 apply   #A22003D0         -> -0.750
decs010 apply   -0.750            -> #A22003D0
decs011 apply   #A21003D0         -> -0.0750
decs012 apply   -0.0750           -> #A21003D0
decs013 apply   #A1f003D0         -> -0.000750
decs014 apply   -0.000750         -> #A1f003D0
decs015 apply   #A1d003D0         -> -0.00000750
decs016 apply   -0.00000750       -> #A1d003D0
decs017 apply   #A1c003D0         -> -7.50E-7
decs018 apply   -7.50E-7          -> #A1c003D0

-- Normality
decs020 apply   1234567            -> #2654d2e7
decs021 apply  -1234567            -> #a654d2e7
decs022 apply   1111111            -> #26524491

-- Nmax and similar
decs031 apply   9.999999E+96            -> #77f3fcff
decs032 apply   #77f3fcff               -> 9.999999E+96
decs033 apply   1.234567E+96            -> #47f4d2e7
decs034 apply   #47f4d2e7               -> 1.234567E+96
-- fold-downs (more below)
decs035 apply   1.23E+96                -> #47f4c000 Clamped
decs036 apply   #47f4c000               -> 1.230000E+96
decs037 apply   1E+96                   -> #47f00000 Clamped
decs038 apply   #47f00000               -> 1.000000E+96

decs051 apply   12345                   -> #225049c5
decs052 apply   #225049c5               -> 12345
decs053 apply   1234                    -> #22500534
decs054 apply   #22500534               -> 1234
decs055 apply   123                     -> #225000a3
decs056 apply   #225000a3               -> 123
decs057 apply   12                      -> #22500012
decs058 apply   #22500012               -> 12
decs059 apply   1                       -> #22500001
decs060 apply   #22500001               -> 1
decs061 apply   1.23                    -> #223000a3
decs062 apply   #223000a3               -> 1.23
decs063 apply   123.45                  -> #223049c5
decs064 apply   #223049c5               -> 123.45

-- Nmin and below
decs071 apply   1E-95                   -> #00600001
decs072 apply   #00600001               -> 1E-95
decs073 apply   1.000000E-95            -> #04000000
decs074 apply   #04000000               -> 1.000000E-95
decs075 apply   1.000001E-95            -> #04000001
decs076 apply   #04000001               -> 1.000001E-95

decs077 apply   0.100000E-95            -> #00020000     Subnormal
decs07x apply   1.00000E-96             -> 1.00000E-96   Subnormal
decs078 apply   #00020000               -> 1.00000E-96   Subnormal
decs079 apply   0.000010E-95            -> #00000010     Subnormal
decs080 apply   #00000010               -> 1.0E-100      Subnormal
decs081 apply   0.000001E-95            -> #00000001     Subnormal
decs082 apply   #00000001               -> 1E-101        Subnormal
decs083 apply   1e-101                  -> #00000001     Subnormal
decs084 apply   #00000001               -> 1E-101        Subnormal
decs08x apply   1e-101                  -> 1E-101        Subnormal

-- underflows cannot be tested; just check edge case
decs090 apply   1e-101                  -> #00000001  Subnormal

-- same again, negatives --

-- Nmax and similar
decs122 apply  -9.999999E+96            -> #f7f3fcff
decs123 apply   #f7f3fcff               -> -9.999999E+96
decs124 apply  -1.234567E+96            -> #c7f4d2e7
decs125 apply   #c7f4d2e7               -> -1.234567E+96
-- fold-downs (more below)
decs130 apply  -1.23E+96                -> #c7f4c000 Clamped
decs131 apply   #c7f4c000               -> -1.230000E+96
decs132 apply  -1E+96                   -> #c7f00000 Clamped
decs133 apply   #c7f00000               -> -1.000000E+96

decs151 apply  -12345                   -> #a25049c5
decs152 apply   #a25049c5               -> -12345
decs153 apply  -1234                    -> #a2500534
decs154 apply   #a2500534               -> -1234
decs155 apply  -123                     -> #a25000a3
decs156 apply   #a25000a3               -> -123
decs157 apply  -12                      -> #a2500012
decs158 apply   #a2500012               -> -12
decs159 apply  -1                       -> #a2500001
decs160 apply   #a2500001               -> -1
decs161 apply  -1.23                    -> #a23000a3
decs162 apply   #a23000a3               -> -1.23
decs163 apply  -123.45                  -> #a23049c5
decs164 apply   #a23049c5               -> -123.45

-- Nmin and below
decs171 apply  -1E-95                   -> #80600001
decs172 apply   #80600001               -> -1E-95
decs173 apply  -1.000000E-95            -> #84000000
decs174 apply   #84000000               -> -1.000000E-95
decs175 apply  -1.000001E-95            -> #84000001
decs176 apply   #84000001               -> -1.000001E-95

decs177 apply  -0.100000E-95            -> #80020000     Subnormal
decs178 apply   #80020000               -> -1.00000E-96  Subnormal
decs179 apply  -0.000010E-95            -> #80000010     Subnormal
decs180 apply   #80000010               -> -1.0E-100     Subnormal
decs181 apply  -0.000001E-95            -> #80000001     Subnormal
decs182 apply   #80000001               -> -1E-101       Subnormal
decs183 apply  -1e-101                  -> #80000001     Subnormal
decs184 apply   #80000001               -> -1E-101       Subnormal

-- underflow edge case
decs190 apply  -1e-101                  -> #80000001  Subnormal

-- zeros
decs400 apply   0E-400                  -> #00000000  Clamped
decs401 apply   0E-101                  -> #00000000
decs402 apply   #00000000               -> 0E-101
decs403 apply   0.000000E-95            -> #00000000
decs404 apply   #00000000               -> 0E-101
decs405 apply   0E-2                    -> #22300000
decs406 apply   #22300000               -> 0.00
decs407 apply   0                       -> #22500000
decs408 apply   #22500000               -> 0
decs409 apply   0E+3                    -> #22800000
decs410 apply   #22800000               -> 0E+3
decs411 apply   0E+90                   -> #43f00000
decs412 apply   #43f00000               -> 0E+90
-- clamped zeros...
decs413 apply   0E+91                   -> #43f00000  Clamped
decs414 apply   #43f00000               -> 0E+90
decs415 apply   0E+96                   -> #43f00000  Clamped
decs416 apply   #43f00000               -> 0E+90
decs417 apply   0E+400                  -> #43f00000  Clamped
decs418 apply   #43f00000               -> 0E+90

-- negative zeros
decs420 apply   -0E-400                 -> #80000000  Clamped
decs421 apply   -0E-101                 -> #80000000
decs422 apply   #80000000               -> -0E-101
decs423 apply   -0.000000E-95           -> #80000000
decs424 apply   #80000000               -> -0E-101
decs425 apply   -0E-2                   -> #a2300000
decs426 apply   #a2300000               -> -0.00
decs427 apply   -0                      -> #a2500000
decs428 apply   #a2500000               -> -0
decs429 apply   -0E+3                   -> #a2800000
decs430 apply   #a2800000               -> -0E+3
decs431 apply   -0E+90                  -> #c3f00000
decs432 apply   #c3f00000               -> -0E+90
-- clamped zeros...
decs433 apply   -0E+91                  -> #c3f00000  Clamped
decs434 apply   #c3f00000               -> -0E+90
decs435 apply   -0E+96                  -> #c3f00000  Clamped
decs436 apply   #c3f00000               -> -0E+90
decs437 apply   -0E+400                 -> #c3f00000  Clamped
decs438 apply   #c3f00000               -> -0E+90

-- Specials
decs500 apply   Infinity  -> #78000000
decs501 apply   #78787878 -> #78000000
decs502 apply   #78000000 -> Infinity
decs503 apply   #79797979 -> #78000000
decs504 apply   #79000000 -> Infinity
decs505 apply   #7a7a7a7a -> #78000000
decs506 apply   #7a000000 -> Infinity
decs507 apply   #7b7b7b7b -> #78000000
decs508 apply   #7b000000 -> Infinity
decs509 apply   #7c7c7c7c -> #7c0c7c7c

decs510 apply   NaN       -> #7c000000
decs511 apply   #7c000000 -> NaN
decs512 apply   #7d7d7d7d -> #7c0d7d7d
decs513 apply   #7d000000 -> NaN
decs514 apply   #7e7e7e7e -> #7e0e7c7e
decs515 apply   #7e000000 -> sNaN
decs516 apply   #7f7f7f7f -> #7e0f7c7f
decs517 apply   #7f000000 -> sNaN
decs518 apply   #7fffffff -> sNaN999999
decs519 apply   #7fffffff -> #7e03fcff

decs520 apply   -Infinity -> #f8000000
decs521 apply   #f8787878 -> #f8000000
decs522 apply   #f8000000 -> -Infinity
decs523 apply   #f9797979 -> #f8000000
decs524 apply   #f9000000 -> -Infinity
decs525 apply   #fa7a7a7a -> #f8000000
decs526 apply   #fa000000 -> -Infinity
decs527 apply   #fb7b7b7b -> #f8000000
decs528 apply   #fb000000 -> -Infinity

decs529 apply   -NaN      -> #fc000000
decs530 apply   #fc7c7c7c -> #fc0c7c7c
decs531 apply   #fc000000 -> -NaN
decs532 apply   #fd7d7d7d -> #fc0d7d7d
decs533 apply   #fd000000 -> -NaN
decs534 apply   #fe7e7e7e -> #fe0e7c7e
decs535 apply   #fe000000 -> -sNaN
decs536 apply   #ff7f7f7f -> #fe0f7c7f
decs537 apply   #ff000000 -> -sNaN
decs538 apply   #ffffffff -> -sNaN999999
decs539 apply   #ffffffff -> #fe03fcff

-- diagnostic NaNs
decs540 apply   NaN       -> #7c000000
decs541 apply   NaN0      -> #7c000000
decs542 apply   NaN1      -> #7c000001
decs543 apply   NaN12     -> #7c000012
decs544 apply   NaN79     -> #7c000079
decs545 apply   NaN12345   -> #7c0049c5
decs546 apply   NaN123456  -> #7c028e56
decs547 apply   NaN799799  -> #7c0f7fdf
decs548 apply   NaN999999  -> #7c03fcff


-- fold-down full sequence
decs601 apply   1E+96                   -> #47f00000 Clamped
decs602 apply   #47f00000               -> 1.000000E+96
decs603 apply   1E+95                   -> #43f20000 Clamped
decs604 apply   #43f20000               -> 1.00000E+95
decs605 apply   1E+94                   -> #43f04000 Clamped
decs606 apply   #43f04000               -> 1.0000E+94
decs607 apply   1E+93                   -> #43f00400 Clamped
decs608 apply   #43f00400               -> 1.000E+93
decs609 apply   1E+92                   -> #43f00080 Clamped
decs610 apply   #43f00080               -> 1.00E+92
decs611 apply   1E+91                   -> #43f00010 Clamped
decs612 apply   #43f00010               -> 1.0E+91
decs613 apply   1E+90                   -> #43f00001
decs614 apply   #43f00001               -> 1E+90


-- Selected DPD codes
decs700 apply   #22500000       -> 0
decs701 apply   #22500009       -> 9
decs702 apply   #22500010       -> 10
decs703 apply   #22500019       -> 19
decs704 apply   #22500020       -> 20
decs705 apply   #22500029       -> 29
decs706 apply   #22500030       -> 30
decs707 apply   #22500039       -> 39
decs708 apply   #22500040       -> 40
decs709 apply   #22500049       -> 49
decs710 apply   #22500050       -> 50
decs711 apply   #22500059       -> 59
decs712 apply   #22500060       -> 60
decs713 apply   #22500069       -> 69
decs714 apply   #22500070       -> 70
decs715 apply   #22500071       -> 71
decs716 apply   #22500072       -> 72
decs717 apply   #22500073       -> 73
decs718 apply   #22500074       -> 74
decs719 apply   #22500075       -> 75
decs720 apply   #22500076       -> 76
decs721 apply   #22500077       -> 77
decs722 apply   #22500078       -> 78
decs723 apply   #22500079       -> 79

decs730 apply   #2250029e       -> 994
decs731 apply   #2250029f       -> 995
decs732 apply   #225002a0       -> 520
decs733 apply   #225002a1       -> 521

-- DPD: one of each of the huffman groups
decs740 apply   #225003f7       -> 777
decs741 apply   #225003f8       -> 778
decs742 apply   #225003eb       -> 787
decs743 apply   #2250037d       -> 877
decs744 apply   #2250039f       -> 997
decs745 apply   #225003bf       -> 979
decs746 apply   #225003df       -> 799
decs747 apply   #2250006e       -> 888


-- DPD all-highs cases (includes the 24 redundant codes)
decs750 apply   #2250006e       -> 888
decs751 apply   #2250016e       -> 888
decs752 apply   #2250026e       -> 888
decs753 apply   #2250036e       -> 888
decs754 apply   #2250006f       -> 889
decs755 apply   #2250016f       -> 889
decs756 apply   #2250026f       -> 889
decs757 apply   #2250036f       -> 889

decs760 apply   #2250007e       -> 898
decs761 apply   #2250017e       -> 898
decs762 apply   #2250027e       -> 898
decs763 apply   #2250037e       -> 898
decs764 apply   #2250007f       -> 899
decs765 apply   #2250017f       -> 899
decs766 apply   #2250027f       -> 899
decs767 apply   #2250037f       -> 899

decs770 apply   #225000ee       -> 988
decs771 apply   #225001ee       -> 988
decs772 apply   #225002ee       -> 988
decs773 apply   #225003ee       -> 988
decs774 apply   #225000ef       -> 989
decs775 apply   #225001ef       -> 989
decs776 apply   #225002ef       -> 989
decs777 apply   #225003ef       -> 989

decs780 apply   #225000fe       -> 998
decs781 apply   #225001fe       -> 998
decs782 apply   #225002fe       -> 998
decs783 apply   #225003fe       -> 998
decs784 apply   #225000ff       -> 999
decs785 apply   #225001ff       -> 999
decs786 apply   #225002ff       -> 999
decs787 apply   #225003ff       -> 999

-- narrowing case
decs790 apply 2.00E-99 -> #00000100 Subnormal
decs791 apply #00000100 -> 2.00E-99 Subnormal
