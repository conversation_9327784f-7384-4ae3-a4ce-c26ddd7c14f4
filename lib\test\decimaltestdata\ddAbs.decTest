------------------------------------------------------------------------
-- ddAbs.decTest -- decDouble absolute value, heeding sNaN            --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

ddabs001 abs '1'      -> '1'
ddabs002 abs '-1'     -> '1'
ddabs003 abs '1.00'   -> '1.00'
ddabs004 abs '-1.00'  -> '1.00'
ddabs005 abs '0'      -> '0'
ddabs006 abs '0.00'   -> '0.00'
ddabs007 abs '00.0'   -> '0.0'
ddabs008 abs '00.00'  -> '0.00'
ddabs009 abs '00'     -> '0'

ddabs010 abs '-2'     -> '2'
ddabs011 abs '2'      -> '2'
ddabs012 abs '-2.00'  -> '2.00'
ddabs013 abs '2.00'   -> '2.00'
ddabs014 abs '-0'     -> '0'
ddabs015 abs '-0.00'  -> '0.00'
ddabs016 abs '-00.0'  -> '0.0'
ddabs017 abs '-00.00' -> '0.00'
ddabs018 abs '-00'    -> '0'

ddabs020 abs '-2000000' -> '2000000'
ddabs021 abs '2000000'  -> '2000000'

ddabs030 abs '+0.1'            -> '0.1'
ddabs031 abs '-0.1'            -> '0.1'
ddabs032 abs '+0.01'           -> '0.01'
ddabs033 abs '-0.01'           -> '0.01'
ddabs034 abs '+0.001'          -> '0.001'
ddabs035 abs '-0.001'          -> '0.001'
ddabs036 abs '+0.000001'       -> '0.000001'
ddabs037 abs '-0.000001'       -> '0.000001'
ddabs038 abs '+0.000000000001' -> '1E-12'
ddabs039 abs '-0.000000000001' -> '1E-12'

-- examples from decArith
ddabs040 abs '2.1'     ->  '2.1'
ddabs041 abs '-100'    ->  '100'
ddabs042 abs '101.5'   ->  '101.5'
ddabs043 abs '-101.5'  ->  '101.5'

-- more fixed, potential LHS swaps/overlays if done by subtract 0
ddabs060 abs '-56267E-10'  -> '0.0000056267'
ddabs061 abs '-56267E-5'   -> '0.56267'
ddabs062 abs '-56267E-2'   -> '562.67'
ddabs063 abs '-56267E-1'   -> '5626.7'
ddabs065 abs '-56267E-0'   -> '56267'

-- subnormals and underflow

-- long operand tests
ddabs321 abs 1234567890123456  -> 1234567890123456
ddabs322 abs 12345678000  -> 12345678000
ddabs323 abs 1234567800   -> 1234567800
ddabs324 abs 1234567890   -> 1234567890
ddabs325 abs 1234567891   -> 1234567891
ddabs326 abs 12345678901  -> 12345678901
ddabs327 abs 1234567896   -> 1234567896

-- zeros
ddabs111 abs          0   -> 0
ddabs112 abs         -0   -> 0
ddabs113 abs       0E+6   -> 0E+6
ddabs114 abs      -0E+6   -> 0E+6
ddabs115 abs     0.0000   -> 0.0000
ddabs116 abs    -0.0000   -> 0.0000
ddabs117 abs      0E-141  -> 0E-141
ddabs118 abs     -0E-141  -> 0E-141

-- full coefficients, alternating bits
ddabs121 abs  2682682682682682         -> 2682682682682682
ddabs122 abs  -2682682682682682        -> 2682682682682682
ddabs123 abs  1341341341341341         -> 1341341341341341
ddabs124 abs  -1341341341341341        -> 1341341341341341

-- Nmax, Nmin, Ntiny
ddabs131 abs  9.999999999999999E+384   -> 9.999999999999999E+384
ddabs132 abs  1E-383                   -> 1E-383
ddabs133 abs  1.000000000000000E-383   -> 1.000000000000000E-383
ddabs134 abs  1E-398                   -> 1E-398 Subnormal

ddabs135 abs  -1E-398                  -> 1E-398 Subnormal
ddabs136 abs  -1.000000000000000E-383  -> 1.000000000000000E-383
ddabs137 abs  -1E-383                  -> 1E-383
ddabs138 abs  -9.999999999999999E+384  -> 9.999999999999999E+384

-- specials
ddabs520 abs 'Inf'    -> 'Infinity'
ddabs521 abs '-Inf'   -> 'Infinity'
ddabs522 abs   NaN    ->  NaN
ddabs523 abs  sNaN    ->  NaN   Invalid_operation
ddabs524 abs   NaN22  ->  NaN22
ddabs525 abs  sNaN33  ->  NaN33 Invalid_operation
ddabs526 abs  -NaN22  -> -NaN22
ddabs527 abs -sNaN33  -> -NaN33 Invalid_operation

-- Null tests
ddabs900 abs  # -> NaN Invalid_operation

