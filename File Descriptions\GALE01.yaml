# Videos
MvHowto.mth: The How to Play video
MvOmake15.mth: The 15-Minute "Special Movie"
MvOpen.mth: The Game's Opening Movie

# Vanilla Music Tracks
1p_qk.hps: All-Star Rest Area
akaneia.hps: Fire Emblem
baloon.hps: Balloon Fight
bigblue.hps: Big Blue
castle.hps: Princess Peach's Castle
continue.hps: Continue
corneria.hps: <PERSON><PERSON>
docmari.hps: Dr. Mario
ending.hps: Ending Fanfare
famidemo.hps: Demo Fanfare (Unused)
ff_1p01.hps: 1P Mode Fanfare 1
ff_1p02.hps: 1P Mode Fanfare 2
ff_bad.hps: Unused Bad Fanfare
ff_dk.hps: Donkey Kong Victory Theme
ff_emb.hps: Fire Emblem Victory Theme
ff_flat.hps: Game & Watch Victory Theme
ff_fox.hps: Star Fox Victory Theme
ff_fzero.hps: F-Zero Victory Theme
ff_good.hps: Unused Good Fanfare
ff_ice.hps: Ice Climber Victory Theme
ff_kirby.hps: Kirby Victory Theme
ff_link.hps: Legend of Zelda Victory Theme
ff_mario.hps: Super Mario Victory Theme
ff_nes.hps: Earthbound Victory Theme
ff_poke.hps: "Pok\xE9mon Victory Theme"
ff_samus.hps: Metroid Victory Theme
ff_step1.hps: Unused Fanfare 1
ff_step2.hps: Unused Fanfare 2
ff_step3.hps: Unused Fanfare 3
ff_yoshi.hps: Yoshi Victory Theme
flatzone.hps: Flat Zone
fourside.hps: Fourside
gameover.hps: Game Over
garden.hps: Kongo Jungle
greatbay.hps: Great Bay
greens.hps: Green Greens
howto.hps: How to Play (music w/sound effects)
howto_s.hps: How to Play (music only)
hyaku.hps: Multi-Man Melee 1
hyaku2.hps: Multi-Man Melee 2
icemt.hps: Icicle Mountain
inis1_01.hps: Mushroom Kingdom
inis1_02.hps: Mushroom Kingdom (Finale)
inis2_01.hps: Mushroom Kingdom II
inis2_02.hps: Mushroom Kingdom II (Finale)
intro_es.hps: Classic Fanfare
intro_nm.hps: Adventure Fanfare
item_h.hps: Hammer Theme
item_s.hps: Star Theme
izumi.hps: Fountain of Dreams
kongo.hps: Jungle Japes
kraid.hps: Brinstar Depths
menu01.hps: Main Menu (Default)
menu02.hps: Lottery
menu3.hps: Main Menu (Alternate)
mrider.hps: Mach Rider
mutecity.hps: Mute City
old_dk.hps: Kongo Jungle N64
old_kb.hps: Dream Land 64
old_ys.hps: Yoshi's Island 64
onetto.hps: Onett
onetto2.hps: Mother 2
opening.hps: Opening Movie audio
pokesta.hps: "Battle Theme (Pok\xE9mon)"
pstadium.hps: "Pok\xE9mon Stadium"
pura.hps: "Pok\xE9 Floats"
rcruise.hps: Rainbow Cruise
s_info1.hps: Info Fanfare 1
s_info2.hps: Info Fanfare 2
s_info3.hps: Info Fanfare 3
s_new1.hps: Trophy Fanfare
s_new2.hps: Unused Fanfare
s_newcom.hps: Challenger Approaching
s_select.hps: Unused Song (Hammer Theme)
saria.hps: Saria's Theme
shrine.hps: Temple
siren.hps: Brinstar Escape
smari3.hps: Super Mario Bros. 3
sp_end.hps: Final Destination
sp_giga.hps: Giga Bowser
sp_metal.hps: Metal Battle
sp_zako.hps: Battlefield (Fighting Wire Frames)
swm_15min.hps: Special Movie audio
target.hps: Targets!
venom.hps: Venom
vl_battle.hps: Metal Mario Cutscene
vl_castle.hps: Luigi Adventure Cutscene
vl_corneria.hps: Corneria Adventure Cutscene
vl_cosmos.hps: Space Adventure Cutscene
vl_figure1.hps: Bowser Destroyed Cutscene
vl_figure2.hps: Giga Bowser Destroyed Cutscene
vl_fzero.hps: F-Zero Adventure Cutscene
vl_last_v2.hps: Giga Bowser Cutscene
vs_hyou1.hps: Tournament Mode 1
vs_hyou2.hps: Tournament Mode 2
yorster.hps: Yoshi's Island
ystory.hps: Yoshi's Story
zebes.hps: Brinstar

# Vanilla Sound Effects
nr_name.ssm: CSS Character Name Announcements

# Miscellaneous Vanilla Files (applies to .usd as well):
GmGover.dat: '1P Mode: Game Over Screen'
GmKumite.dat: Multi-Man Melee
GmPause.dat: Pause Screen
GmRst.dat: Results Screen
GmStRoll.dat: Credits Screen/Minigame
GmTitle.dat: Unused Beta Title Screen
GmTou1p.dat: Tournament Mode File 1
GmTou2p.dat: Tournament Mode File 2
GmTou3p.dat: Tournament Mode File 3
GmTou4p.dat: Tournament Mode File 4
GmTrain.dat: Training Mode
GmTtAll.dat: Title Screen
IfAll.dat: Match HUD and Interface
IfComSn.dat: '"Coming Soon" Screen'
IfHrNoCn.dat: Home Run Contest File 1
IfHrReco.dat: Home Run Contest File 2
IfPrize.dat: Special Achievement Messages
IfVsCam.dat: 'Special Melee: Camera Mode'
IrAls.dat: '1P Mode: "VS." Intro Screens'
IrEzFigG.dat: Snag the Trophies splash screen
IrEzTarg.dat: Break the Targets splash screen
IrNml.dat: Adventure Mode stage splash screens
ItCo.dat: Items
LbAd.dat: Audio-loading data
LbMcGame.dat: Memory card banners and icon
LbMcSnap.dat: Memory card snapshot banner/icon
LbRb.dat: Controller rumble data
MnExtAll.dat: Extra menu graphics for the CSS
MnMaAll.dat: Main menu graphics file
MnNamedef.dat: Name entry data
MnSlChr.dat: Character Select Screen
MnSlMap.dat: Stage Select Screen
NtAppro.dat: "'New Challenger' Screens"
opening.bnr: Game banner, title, and description texts
PlCo.dat: Textures & global data common to the cast
usa.ini: Language indicator (tells game to use .usd)

# UI / Menu text strings
SdClr.dat: Target Test stage clear text
SdDec.dat: Bonuses text
SdIntro.dat: Bonus stage intro screen text
SdMenu.dat: Lots of general menu text
SdMsgBox.dat: Memory card management text
SdPrize.dat: Special messages and character/trophy names
SdProge.dat: Enable progressive scan mode text
SdRst.dat: Results screen and bonuses
SdSlChr.dat: Stage names and VS menu text
SdStRoll.dat: Staff roll / credits shooting game
SdTou.dat: Tournament mode strings
SdToy.dat: Trophy names
SdToyExp.dat: Trophy descriptions
SdTrain.dat: Training mode strings
SdVsCam.dat: Camera mode snapshot management text
SmSt.dat: Sound Test

# Vanilla Stages
GrBb.dat: Big Blue
GrCn.dat: Corneria
GrCs.dat: Princess Peach's Castle
GrEF1.dat: Goomba Trophy Stage
GrEF2.dat: Entei Trophy Stage
GrEF3.dat: Majora Trophy Stage
GrFs.dat: Fourside
GrFz.dat: Flat Zone
GrGb.dat: Great Bay
GrGd.dat: Jungle Japes [Garden]
GrGr.dat: Green Greens
GrHe.dat: All-Star Rest Area [Heal]
GrHr.dat: Homerun Contest
GrI1.dat: Mushroom Kingdom
GrI2.dat: Mushroom Kingdom II (Subcon)
GrTIc.dat: Icetop (unused stage)
GrIm.dat: Icicle Mountain
GrIz.dat: Fountain of Dreams [Izumi]
GrKg.dat: Kongo Jungle
GrKr.dat: Brinstar Depths [Kraid]
GrMc.dat: Mute City
GrNBa.dat: Battlefield
GrNBr.dat: F-Zero Grand Prix
GrNFg.dat: Trophy Collector [Figure Get]
GrNKr.dat: Mushroom Kingdom Adventure
GrNLa.dat: Final Destination
GrNPo.dat: Race to the Finish [Pushon]
GrNSr.dat: Hyrule Maze
GrNZr.dat: Brinstar Escape Shaft [Zebes]
GrOk.dat: Kongo Jungle (N64)
GrOp.dat: Dream Land (N64)
GrOt.dat: Onett
GrOy.dat: Yoshi's Island (N64)
GrPs.dat: "Pok\xE9mon Stadium"
GrPs1.dat: "Pok\xE9mon Stadium - Fire Form"
GrPs2.dat: "Pok\xE9mon Stadium - Grass Form"
GrPs3.dat: "Pok\xE9mon Stadium - Water Form"
GrPs4.dat: "Pok\xE9mon Stadium - Rock Form"
GrPu.dat: "Pok\xE9 Floats [Pura]"
GrRc.dat: Rainbow Cruise
GrSh.dat: Hyrule Temple [Shrine]
GrSt.dat: Yoshi's Story
GrTe.dat: TEST (a.k.a. The Coffee Shop)
GrVe.dat: Venom
GrYt.dat: Yoshi's Island
GrZe.dat: Brinstar [Zebes]

# Misc. Custom Files in 20XX HP (v4.05 and/or earlier)
IfComS0.dat: Dual 1v1 Infographic
IfComS1.dat: Chess Melee Infographic
IfComS2.dat: Turbo Mode Infographic
IfComS3.dat: NBA Jam Infographic
IfComS4.dat: SD Remix Infographic
IfComS5.dat: 2015 Teir List
SdMenu.1sd: General menu strings (with SSS Page 1 names)
SdMenu.2sd: General menu strings (with SSS Page 2 names)
SdMenu.3sd: General menu strings (with SSS Page 3 names)
SdMenu.4sd: General menu strings (with SSS Page 4 names)
SdSlChr.1sd: Stage names and VS menu text
SdSlChr.2sd: Stage names and VS menu text
SdSlChr.3sd: Stage names and VS menu text
SdSlChr.4sd: Stage names and VS menu text

# Misc. Custom Files in 20XX HP (v5.0+)
AI_Engine.bin: Main Code for the 20XX AI
IfComAM.dat: About Achilles
IfComCM.dat: Chess Melee
IfComD1.dat: Dual 1v1
IfComDb.dat: Turbo Mode
IfComDI.dat: Directional Influence Draw
IfComDW.dat: DAT Texture Wizard
IfComM1.dat: Basic Unlock-All Save File
IfComM2.dat: 'Memory Card Mod: Stage Expansion'
IfComM3.dat: 'Memory Card Mod: 20XX TE'
IfComM4.dat: 'Memory Card Mod: SD Remix Lite'
IfComM5.dat: 'Memory Card Mod: Minigames Mod'
IfComM6.dat: 'Memory Card Mod: Crazy Mod'
IfComM7.dat: 'Memory Card Mod: Stage Expansion'
IfComNI.dat: Nintendont
IfComNJ.dat: NBA Jam
IfComSD.dat: SD Remix
IfComT1.dat: General 20XX Hotkeys/Toggles
IfComT2.dat: Develop Mode Toggles
IfComTL.dat: SSBM Tier List
IfComTT.dat: Tag Team
MnSlChr.0sd: Character Select Screen - 5.0 Style with Tri-CSPs
MnSlChr.1sd: Character Select Screen - 5.0 Style with Vanilla CSPs
MnSlChr.2sd: Character Select Screen - 4.0 Style with Tri-CSPs
MnSlChr.3sd: Character Select Screen - 4.0 Style with Vanilla CSPs
MnSlMap.1sd: Stage Select Screen, Page 1
MnSlMap.2sd: Stage Select Screen, Page 2
MnSlMap.3sd: Stage Select Screen, Page 3
MnSlMap.4sd: Stage Select Screen, Page 4

# Custom Stages in 20XX HP
GrC0.usd: Sector Z                       # 20XXHP 5.0+
GrCs.pat: Omega Peach's Castle           # 20XXHP 5.0+
GrCs.bat: Omega Bowser's Castle          # 20XXHP 5.0+
GrFs.1at: Smashville Fourside
GrFs.2at: Moonside                       # 20XXHP 5.0+
GrGb.0at: Turtle Stage                   # 20XXHP 5.0+
GrGb.1at: Great Bay Beach                # 20XXHP 5.0+
GrGb.hat: Great Bay Hacked
GrGd.1at: Jungle Japes Hacked (w/platform)
GrGd.2at: Jungle Japes Omega
GrGr.0at: Green Greens
GrGr.1at: Green Greens Hacked
GrHe.0at: Walk-Off Heal                  # 20XXHP 5.0+
GrI1.0at: Milun's Mushroom Kingdom       # 20XXHP 5.0+
GrI1.1at: Porygon                        # 20XXHP 5.0+
GrI1.2at: Shiny Porygon                  # 20XXHP 5.0+
GrIz.gat: Cave of Dreams                 # 20XXHP 5.0+
GrKg.hat: Kongo Jungle Hacked
GrNBa.2at: Ancient Battlefield
GrNBa.3at: Battlefield Plaza
GrNBa.4at: Matrix Battlefield            # Old 20XX
GrNBa.bat: Battlefield Plaza
GrNBa.lat: Battlefino Plaza
GrNFg.0at: Trophy Collector (Two platforms)
GrNFg.1at: Trophy Collector (Three platforms)
GrNFg.2at: Trophy Collector Omega
GrNKr.0at: Mushroom Kingdom Adventure
GrNKr.1at: Mushroom Kingdom Adventure Hacked
GrNKr.2at: Mushroom Kingdom Adventure Omega
GrNLa.0at: Final Destination
GrNLa.2at: Wii-U Final Destination       # Old 20XX
GrNLa.gat: Wii-U Final Destination       # 20XXHP 5.0+
GrNLa.hat: zankyou FD                    # 20XXHP 5.0+
GrNSr.1at: Hyrule Maze Hacked
GrOk.0at: Monster Island                 # 20XXHP 5.0+
GrOp.gat: Halberd Land                   # 20XXHP 5.0+
GrOp.kat: KirbyWare Inc.
GrOp.rat: Return to Dream Land
GrOy.hat: Yoshi's Island (N64) Milun Hack
GrOy.wat: WarioWare Inc.
GrPb.usd: "Pok\xE9mon Stadium (Blue No transforms)" # Old 20XX
GrPn.usd: "Pok\xE9mon Stadium (Blue No transforms)" # 20XXHP 5.0+
GrSh.sat: Skyrule (Redux)
GrSh.0at: Dark Temple                    # 20XXHP 5.0+
GrSt.gat: Peach's Story                  # 20XXHP 5.0+
GrTCa.gat: Silph Co. (Saffron City)      # 20XXHP 5.0+
GrTCl.bat: Smash 4 Battlefield
GrTCl.gat: Brawl Battlefield - Dusk      # 20XXHP 5.0+
GrTCl.sat: Suzaku Castle
GrTDk.0at: Meta Mine                     # 20XXHP 5.0+
GrTDr.0at: Training Room                 # 20XXHP 5.0+
GrTFe.kat: "Kalos Pok\xE9mon League"
GrTFx.0at: The Plain                     # 20XXHP 5.0+
GrTGn.0at: 75m                           # 20XXHP 5.0+
GrTKb.gat: Miiverse (variation 1)
GrTKb.hat: Miiverse (variation 2)
GrTKb.iat: Miiverse (variation 3)
GrTKb.jat: Miiverse (variation 4)
GrTKp.mat: Metroid Lab
GrTLg.1at: Giant GameCube
GrTLg.mat: Metal Cavern M
GrTLk.0at: Lylat Cruise                  # 20XXHP 5.0+
GrTNs.0at: Throne Room (Wario Land)      # 20XXHP 5.0+
GrTPe.hat: Hyrule Castle (N64)
GrTSk.0at: Meta Crystal                  # 20XXHP 5.0+
GrYt.0at: Yoshi's Island
GrYt.1at: Omega Yoshi's Island
GrYt.2at: Milun's Island - Form A        # 20XXHP 5.0+
GrYt.3at: Milun's Island - Form B        # 20XXHP 5.0+
GrYt.4at: Milun's Island - Form C        # 20XXHP 5.0+
GrNBa.tat: The End of Time
GrTKb.mat: Mirror Chamber
GrSh.mat: Mementos
GrTZd.pat: Great Plateau Tower
GrTCa.sat: Icicle Mountain Smashville
GrTSk.cat: Castle Siege
GrGd.0at: Jungle Japes [Garden]
GrFs.0at: Fourside
GrTSk.iat: SSB64 Intro Stage             # 20XXHP 5.0+
GrNSr.0at: Underground Maze (original)
GrEF2.rat: Ripple Star
GrEF3.fat: Falcon's Realm

# Trophies
TyMnBg.dat: Trophies Gallery Backgrounds
TyMnDisp.dat: Trophy Collection Room
TyMnFigp.dat: Trophy Lottery
TyMnView.dat: Trophy Gallery Menu View

# Adventure Mode Cutscenes
Vi0102.dat: Adventure Mode cutscenes (Mario & Luigi)
Vi0401.dat: Adventure Mode cutscenes data
Vi0402.dat: Adventure Mode cutscenes data
Vi0501.dat: Adventure Mode cutscenes (Kirby)
Vi0502.dat: Adventure Mode cutscenes (Kirby)
Vi0601.dat: Adventure Mode cutscenes data
Vi0801.dat: Adventure Mode cutscenes data
Vi1101.dat: Adventure Mode cutscenes (Mario & Luigi)
Vi1201v1.dat: Adventure Mode cutscenes data
Vi1201v2.dat: Adventure Mode cutscenes (Giga Bowser)
Vi1202.dat: Adventure Mode cutscenes data