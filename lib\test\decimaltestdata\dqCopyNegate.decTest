------------------------------------------------------------------------
-- dqCopyNegate.decTest -- quiet decQuad copy and negate              --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decQuads.
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check
dqcpn001 copynegate       +7.50  -> -7.50

-- Infinities
dqcpn011 copynegate  Infinity    -> -Infinity
dqcpn012 copynegate  -Infinity   -> Infinity

-- NaNs, 0 payload
dqcpn021 copynegate         NaN  -> -NaN
dqcpn022 copynegate        -NaN  -> NaN
dqcpn023 copynegate        sNaN  -> -sNaN
dqcpn024 copynegate       -sNaN  -> sNaN

-- NaNs, non-0 payload
dqcpn031 copynegate       NaN13  -> -NaN13
dqcpn032 copynegate      -NaN13  -> NaN13
dqcpn033 copynegate      sNaN13  -> -sNaN13
dqcpn034 copynegate     -sNaN13  -> sNaN13
dqcpn035 copynegate       NaN70  -> -NaN70
dqcpn036 copynegate      -NaN70  -> NaN70
dqcpn037 copynegate      sNaN101 -> -sNaN101
dqcpn038 copynegate     -sNaN101 -> sNaN101

-- finites
dqcpn101 copynegate          7   -> -7
dqcpn102 copynegate         -7   -> 7
dqcpn103 copynegate         75   -> -75
dqcpn104 copynegate        -75   -> 75
dqcpn105 copynegate       7.50   -> -7.50
dqcpn106 copynegate      -7.50   -> 7.50
dqcpn107 copynegate       7.500  -> -7.500
dqcpn108 copynegate      -7.500  -> 7.500

-- zeros
dqcpn111 copynegate          0   -> -0
dqcpn112 copynegate         -0   -> 0
dqcpn113 copynegate       0E+4   -> -0E+4
dqcpn114 copynegate      -0E+4   -> 0E+4
dqcpn115 copynegate     0.0000   -> -0.0000
dqcpn116 copynegate    -0.0000   -> 0.0000
dqcpn117 copynegate      0E-141  -> -0E-141
dqcpn118 copynegate     -0E-141  -> 0E-141

-- full coefficients, alternating bits
dqcpn121 copynegate   2682682682682682682682682682682682    -> -2682682682682682682682682682682682
dqcpn122 copynegate  -2682682682682682682682682682682682    ->  2682682682682682682682682682682682
dqcpn123 copynegate   1341341341341341341341341341341341    -> -1341341341341341341341341341341341
dqcpn124 copynegate  -1341341341341341341341341341341341    ->  1341341341341341341341341341341341

-- Nmax, Nmin, Ntiny
dqcpn131 copynegate  9.999999999999999999999999999999999E+6144   -> -9.999999999999999999999999999999999E+6144
dqcpn132 copynegate  1E-6143                                     -> -1E-6143
dqcpn133 copynegate  1.000000000000000000000000000000000E-6143   -> -1.000000000000000000000000000000000E-6143
dqcpn134 copynegate  1E-6176                                     -> -1E-6176

dqcpn135 copynegate  -1E-6176                                    ->  1E-6176
dqcpn136 copynegate  -1.000000000000000000000000000000000E-6143  ->  1.000000000000000000000000000000000E-6143
dqcpn137 copynegate  -1E-6143                                    ->  1E-6143
dqcpn138 copynegate  -9.999999999999999999999999999999999E+6144  ->  9.999999999999999999999999999999999E+6144
