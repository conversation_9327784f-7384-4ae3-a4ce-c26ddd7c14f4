Disable Container Spawns
Prevents capsules, boxes, barrels, and eggs from spawning randomly.
[Punkline]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8026C784 --- 7C7C1B78 -> Branch

609C0E34 7C03E040
40820008 60830E50
7C7C1B78 00000000


	-==-


Containers Spawn Containers
Containers can contain more containers.
[Punkline]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8026C784 ---- 7C7C1B78 -> Branch

609C0E50 7C03E040
40820008 60830E34
7C7C1B78 00000000


	-==-


Disable Capsule Explosions
(Normal chance for explosion: 12.5%)
[Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8027CF8C --- 40820018 -> 48000018


	-==-


Disable Barrel Explosions
(Normal chance for explosion: 10%)
[Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x80288b28 --- 4080000C -> 60000000


	-==-


Disable Box Explosions
(Normal chance for explosion: 10%)
[Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8028639c --- 38600000 -> 38600001


	-==-


No Items in Classic Mode
[Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8017eb5c --- 88630000 -> 386000ff


	-==-


Item Rain in Classic Mode
[Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8017eb5c --- 88630000 -> 38600007


	-==-


Hot Potato Mr. Saturn mode
If you are holding a Mr. Saturn when the seconds remaining in the match is a multiple of 5, you die. If you are hit with a Mr. Saturn while not holding another item, you are forced to catch it and are stunned for a second or so.
[wParam]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0x28059C ---- 7C0802A6 -> Branch

7C0802A6 90010004
9421FFE8 BFA10008
7C7F1B78 80A3002C
83C50CF4 2C1E0000
4182004C 83BE002C
811D1974 2C080000
4082003C 7FE4FB78
7FC3F378 3C608031
60633618 7C6903A6
4E800421 7FC3F378
38800001 3C608034
60631EF0 7C6903A6
4E800421 3C804170
909D1A4C 7FE3FB78
BBA10008 8001001C
38210018 7C0803A6
7C0802A6 4827ED44

1.02 ------ 0x66f40 ---- 7C0802A6 -> Branch

80C3002C 80E61974
2C070000 4182004C
8107002C 81080010
2C080007 4082003C
3CC08047 80C6B6C8
2C060000 4182002C
38E00005 7D063BD6
7D2839D6 7C093000
40820018 3C608013
60639264 7C6903A6
4E800421 7C0802A6
60000000 48065698


	-==-


Stock Control/Crew Battle Mode
Essentially, this hack gets rid of the damage modifier for handicap and replaces it with controllable stocks so that one player can spawn with say 6 stocks and another can spawn with 4.

When handicap is set to "ON" you can set custom stocks per player (1 stock - 9 stocks).

When handicap is set to "AUTO" you can set custom stock per player (1 stock - 9 stocks) just like when handicap is "ON." However, upon re-entering the character select screen, your handicap will be set to the amount of stocks you had at the end of the game. If you had zero stocks (as in you lost), the game will set your stock to the current stock setting (any stock amount higher than nine will be set nine). This allows for easy crew battles. You can still change the stocks manually if needed (rage quits etc.).
[Jorgasms]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x80036bb4 --- 9803008E -> 60000000
1.02 ----- 0x80266678 --- 3803FFFF -> Branch

3DC0803F 61CEA3E6
89CE0000 2C0E0000
4182008C 3DC08045
39CE310E 3DE08048
39EF0828 3E008045
6210BF14 8A100000
2C100009 40810008
3A000009 8A2E0000
2C110000 4182000C
9A2F0000 48000008
9A0F0000 8A2E0E90
2C110000 4182000C
9A2F0024 48000008
9A0F0024 8A2E1D20
2C110000 4182000C
9A2F0048 48000008
9A0F0048 8A2E2BB0
2C110000 4182000C
9A2F006C 48000008
9A0F006C 3803FFFF
60000000 48000000

1.02 ----- 0x801A57DC --- 38BEFFF8 -> Branch

3E008048 621006E0
3A200009 9A300000
9A300024 9A300048
9A30006C 38BEFFF8
60000000 48000000

1.02 ----- 0x80230D54 --- 88030005 -> Branch

88030005 3DC0803F
61CEA3E6 3DE08045
61EFBF15 3A000001
89CE0000 2C0E00FF
4082000C 9A0F0000
38000001 48000000

1.02 ----- 0x8022F76C --- 881C0004 -> Branch

881C0004 3DC0803F
61CEA3E6 3E008045
6210BF15 8A300000
2C110001 40820014
38000002 39E000FF
99EE0000 4800000C
39E00000 99EE0000
60000000 48000000

1.02 ----- 0x8022F6BC --- 881C0004 -> Branch

881C0004 3DC0803F
61CEA3E6 3E008045
6210BF15 8A300000
2C110001 40820014
38000002 39E000FF
99EE0000 4800000C
39E00000 99EE0000
60000000 48000000

1.02 ----- 0x80033CC4 --- 98A3008E -> Branch

3DC08045 61CEBF15
89CE0000 2C0E0002
40820038 3DC08045
39CE310E 3DE08048
39EF0828 8A4F0000
9A4E0000 8A4F0024
9A4E0E90 8A4F0048
9A4E1D20 8A4F006C
9A4E2BB0 48000008
98A3008E 48000000


	-==-


!

Taunt Battle (r2)
Demo video: http://www.youtube.com/watch?v=SZ2zuXmvygY
[Dan Salvato]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x80261BE0 --- 9899000A -> Branch

3E208045 6231BF12
8A310000 2C110003
40820010 2C040001
40820008 38840001
9899000A 48000000

1.02 ----- 0x802617CC --- 540007FE -> Branch

3E208045 6231BF12
8A310000 2C110003
4082000C 38000001
48000008 540007FE
60000000 48000000

1.02 ----- 0x80167C08 --- 881E0002 -> Branch

3E208045 6231BF12
8A310000 2C110003
4082000C 38000000
48000008 881E0002
60000000 48000000

1.02 ----- 0x80167F70 --- 881E000B -> Branch

3E208045 6231BF12
8A310000 2C110003
4082000C 38000001
48000008 881E000B
60000000 48000000

1.02 ----- 0x80167F7C --- 881E0002 -> Branch

3E208045 6231BF12
8A310000 2C110003
4082000C 38000000
48000008 881E0002
60000000 48000000

1.02 ----- 0x800DED14 --- 7FE3FB78 -> Branch

3E208045 6231BF12
8A310000 2C110003
40820050 887E067B
809E008C 80BE0020
7C042800 40820008
48000018 2C030000
4082000C 38600002
48000008 38600000
3CC0803C 60C66630
2C030000 41820008
38C60004 80E60000
38E70001 90E60000
7FE3FB78 48000000

1.02 ----- 0x800DEBD0 --- 7C0802A6 -> Branch

8063008C 907E0020
7FC3F378 7C0802A6
60000000 48000000

1.02 ----- 0x80165A64 --- 7C0601D6 -> Branch

3E208045 6231BF12
8A310000 2C110003
40820054 3E20803C
62316630 3E4080BD
6252A4A0 82520000
7F53D378 2C130000
41820010 3A73FFFF
82520008 4BFFFFF0
8A72067B 2C130000
41820008 3A310004
80110000 3E208016
62315AA4 7E2903A6
4E800420 7C0601D6
60000000 48000000

1.02 ----- 0x80036D70 --- 3BE00000 -> Branch

3BE00000 3C60803C
60636630 93E30000
93E30004 48000000

1.02 ----- 0x800DED40 --- 7C7F1B78 -> Branch

7C7F1B78 3E208045
6231BF12 8A310000
2C110003 4082007C
887F067B 809F008C
80BF0020 7C042800
40820008 48000018
2C030000 4082000C
38600001 48000008
38600000 2C030002
40820008 38600001
5463103A 38630518
38800000 909F0518
909F051C 909F0520
909F0528 909F052C
909F0530 909F0534
3C804344 909F0524
38800091 989F0564
3C80437F 7C9F192E
7FE3FB78 48000000

1.02 ----- 0x800D50CC --- 800505D0 -> Branch

3E208045 6231BF12
8A310000 2C110003
4082000C 38000096
48000008 800505D0
60000000 48000000

1.02 ----- 0x8016C348 --- BB610024 -> Branch

3FC08045 63DEBF14
881E0000 3FE0803C
63FF6630 83BF0000
7C1D0000 40800018
83BF0004 7C1D0000
4080000C 38600000
48000008 38600002
BB610024 48000000

1.02 ----- 0x804807c8 --- ?? -> 01


	-==-


500% Stamina Mode - All Players
[achilles]
1.02
024530E4 000001F4
02453F74 000001F4
02454E04 000001F4
02455C94 000001F4


	-==-


Random Hitbox Elements - Every Non-projectile Attack
All Non-projectile attacks will have a random effect, rather than their normal one.
[Achilles]
1.02
C20715B0 00000009
7C0F0378 38600010
3C808038 60840580
7C8903A6 4E800421
2C030004 41A2FFE8
2C030007 41A2FFE0
2C030008 41A2FFD8
2C03000B 41A2FFD0
907E0030 7DE07B78
28000009 00000000


	-==-


Big Head Mode
<https://smashboards.com/threads/big-head-mode.505030/>
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8006B80C ---- 8001007C -> Branch

7FE3FB78 38800023
3D808007 618C500C
7D8903A6 4E800421
7C630774 2C03FFFF
40820024 807F010C
80630008 88630012
809F05E8 1C630010
7C63202E 8063000C
48000010 809F05E8
1C630010 7C63202E
4800001D 7C8802A6
C0840000 D083002C
D0830030 D0830034
4800000C 4E800021
40A00000 8001007C
60000000 00000000



	-==-


Turbo Mode (Replaces Single Button Mode) v1.3
<https://smashboards.com/threads/turbo-mode-replaces-single-button-mode-v1-3-20xx-4-07-compatible.449823/>
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x803F0AA8 ---- 00007541 -> 00000100
------------- 0x8006B02C ---- 481000D1 -> 38600000
------------- 0x8006B798 ---- 881F2219 -> Branch

3DC08048 89CE9D30
2C0E002C 40820018
881F2293 2C000028
4182000C 38000000
48000008 881F2219
60000000 00000000

------------- 0x800966D0 ---- 4E800021 -> Branch

3DC08048 89CE9D30
2C0E002C 408200CC
881F2292 540007FF
418200C0 801F0004
2C000012 41820060
2C00001A 41820058
2C000000 41820058
2C000015 41820050
2C000011 41820050
2C000005 41820050
2C000009 41820050
2C000006 41820050
2C000014 41820048
2C00000D 41820048
2C000002 41820048
2C000019 41820040
48000058 39C00170
48000044 39C0015C
4800003C 39C00164
48000034 39C00168
4800002C 39C0016B
48000024 39C00165
4800001C 39C00162
48000014 81DF0010
2C0E0163 41820018
48000010 A01F2290
7C007000 41820008
4E800021 00000000

------------- 0x800D69A4 ---- 4E800021 -> Branch

3DC08048 89CE9D30
2C0E002C 40820044
88052292 540007FF
41820038 80050004
2C000002 41820010
2C000019 41820008
48000020 81C50010
2C0E0163 41820018
48000010 A0052290
7C007000 41820008
4E800021 00000000

------------- 0x800C9724 ---- 7FE3FB78 -> Branch

3DC08048 89CE9D30
2C0E002C 40820058
88052292 2C000001
4082004C A0052290
81C50010 7C007000
4182002C 7FE3FB78
3DC0800D 61CE5FB0
7DC903A6 4E800421
7FE3FB78 3DC0800C
61CE9468 7DC903A6
4E800421 3DC0800C
61CE9754 7DC903A6
4E800420 7FE3FB78
60000000 00000000

------------- 0x800693AC ---- 7C0802A6 -> Branch

3DC08048 89CE9D30
2C0E002C 408200F4
81E3002C 880F2292
540007FF 418200DC
A1CF2290 7C0E2000
40820008 4E800020
C1AF0080 3DC04000
61CE0000 91C1FFFC
C1C1FFFC FC0D7040
4180000C D1CF0080
48000014 FDC07050
FC0D7040 41810008
D1CF0080 C1AF0084
3DC04040 61CE0000
91C1FFFC C1C1FFFC
FC0D7040 41800008
D1CF0084 80028BE8
900F195C 3821FFD0
90610020 90810024
90A10008 90C1000C
D0210010 D0410014
D0610018 7C0802A6
9001001C 3DC08006
61CEA1BC 7DC903A6
4E800421 80610020
80810024 80A10008
80C1000C C0210010
C0410014 C0610018
8001001C 7C0803A6
38210030 38000028
980F2293 B08F2290
3800FFFF B00F2290
7C0802A6 00000000

------------- 0x8006B7F8 ---- 280C0000 -> Branch

3DC08048 89CE9D30
2C0E002C 40820070
89DF2293 2C0E0028
41820064 39CE0001
99DF2293 81DF1A58
2C0E0000 41820008
4800004C 881F2292
39C00001 51C007FE
981F2292 7FC3F378
801F00E0 2C000001
41820018 3DC0800C
61CE9614 7DC903A6
4E800421 48000020
3DC0800C 61CECAAC
7DC903A6 4E800421
4800000C 2C0C0000
48000028 881F2292
39C00000 51C007FE
981F2292 3DC08006
61CEB80C 7DC903A6
4E800420 4E800021
60000000 00000000

------------- 0x80068684 ---- 887F000C -> Branch

3DC08048 89CE9D30
2C0E002C 4082001C
3800FFFF B0062290
38000000 98062292
38000028 98062293
887F000C 00000000

------------- 0x8006CB14 ---- 4E800021 -> Branch

3DC08048 89CE9D30
2C0E002C 40820010
81C3002C 38000028
980E2293 4E800021
60000000 00000000

------------- 0x80081370 ---- 7C0802A6 -> Branch

3DC08048 89CE9D30
2C0E002C 40820010
81C3002C 38000028
980E2293 7C0802A6
60000000 00000000

------------- 0x80077254 ---- 931A1914 -> Branch

3DC08048 89CE9D30
2C0E002C 40820020
81DA0010 2C0E00D9
41820014 39C00000
99DA2293 81DA0010
B1DA2290 931A1914
60000000 00000000

------------- 0x80076DC8 ---- 907F19A4 -> Branch

3DC08048 89CE9D30
2C0E002C 40820014
39C00000 99DD2293
81DD0010 B1DD2290
907F19A4 00000000

------------- 0x802705A8 ---- 92BC1914 -> Branch

3DC08048 89CE9D30
2C0E002C 40820014
39C00000 99DC2293
81DC0010 B1DC2290
92BC1914 00000000

------------- 0x8006CB7C ---- 8001001C -> Branch

3DE08048 89EF9D30
2C0F002C 40820030
38800023 48000004
7FE3FB78 3CA0800B
60A5FFD0 7CA803A6
38A00000 4E800021
38600000 907F0430
48000004 8001001C
60000000 00000000

------------- 0x800BFFD0 ---- 7C0802A6 -> Branch

2C040023 40820020
38C00023 80EDAE9C
54C61838 7CC63A14
80E60000 38C00197
B0C70008 7C0802A6
60000000 00000000

------------- 0x80266984 ---- 806DB630 -> Branch

3DC08048 89CE9D30
2C0E002C 4082007C
7DC802A6 48000061
7CA802A6 7DC803A6
80650000 3C8080BE
6084D06C 90640000
80650004 3C8080F3
6084572C 90640000
90640004 80650008
3C8080F3 608455AC
90640000 90640004
8065000C 3C8080F3
6084566C 90640000
38600000 90640078
48000018 4E800021
931797FF 202020FF
202020FF FFFC00FF
806DB630 00000000

------------- 0x80079970 ---- 1C75004C -> Branch

3DC08048 89CE9D30
2C0E002C 4082002C
88970042 5484EFFF
40820020 809A002C
80840518 8084002C
38A00000 98A42293
80A40010 B0A42290
1C75004C 00000000

------------- 0x80079820 ---- 5460DFFF -> Branch

3DC08048 89CE9D30
2C0E002C 4082002C
88970042 5484EFFF
40820020 809A002C
80840518 8084002C
38A00000 98A42293
80A40010 B0A42290
5460DFFF 00000000
