Unrestricted Pause Camera
[Achilles]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x80224C24 ---- C0230040 -> 39E00000
------------- 0x8002F5B0 ---- D03F02F8 -> 91FF02F8
------------- 0x80224C44 ---- C0230048 -> 3DE04700
------------- 0x8002F5BC ---- D03F02FC -> 91FF02FC
------------- 0x80224C5C ---- EC210032 -> 3DE04700
------------- 0x8002F58C ---- D03F02E8 -> 91FF02E8
------------- 0x80224C74 ---- EC210032 -> 3DE04700
------------- 0x8002F594 ---- D03F02EC -> 91FF02EC
------------- 0x80224C8C ---- EC210032 -> 3DE04700
------------- 0x8002F5A4 ---- D03F02F4 -> 91FF02F4
------------- 0x80224CA4 ---- EC210032 -> 3DE04700
------------- 0x8002F59C ---- D03F02F0 -> 91FF02F0


	-==-


All Characters are 2D
Makes all characters the same thickness as G&W.
<https://smashboards.com/threads/all-characters-are-2d.452667/>
[DRGN]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x80068fe4 ---- 2C00001B -> 48000008


	-==-


Disable Character Rendering
[Achilles]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 ---- 0x66F40 ------ 7C0802A6 -> 4E800020


	-==-


L-Cancel Training Wheels (Flash White on Success)
This code automatically L-cancels all aerials, but your character will flash white if your input was correct. This allows players to practice L-canceling but suffer no penalty if the L-cancel is missed, allowing the player to practice combos and mental game even without yet having mastered L-canceling.
*Do not use with "Flash Red on Unsuccessful L-Cancel"*
[Dan Salvato]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8008D69C --- 40800048 -> Branch

4080000C 39E000D4
99E30564 48000000

1.02 ----- 0x800C0148 --- 387F0488 -> Branch

387F0488 89FE0564
2C0F00D4 41820008
48000048 39E00091
99FE0564 3DE0C200
91FE0518 91FE051C
91FE0520 91FE0524
3DE00000 91FE0528
91FE052C 91FE0530
3DE0C280 91FE0534
3DE0800C 61EF0150
7DE903A6 4E800420
60000000 48000000


	-==-


Flash Red on Unsuccessful L-Cancel
*Do not use with "L-Cancel Training Wheels (Flash White on Success)"*
[Achilles, Dan Salvato]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8008D690 --- 88A5067F -> Branch

88A5067F 2C050007
4180000C 39E000D4
99E30564 48000000

1.02 ----- 0x800C0148 --- 387F0488 -> Branch

387F0488 89FE0564
2C0F00D4 41820008
4800004C 39E00091
99FE0564 3DE0437F
91FE0518 3DE0C200
91FE0524 3DE00000
91FE051C 91FE0520
91FE0528 91FE052C
91FE0530 3DE0C280
91FE0534 3DE0800C
61EF0150 7DE903A6
4E800420 48000000


	-==-


Turn White During Shield Stun
[Dan Salvato, standardtoaster]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8006B80C --- 8001007C -> Branch

7F03C378 81C30070
2C0E00B5 40820034
3DC0C200 91C30518
91C3051C 91C30520
91C30524 39E00000
91E30528 91E3052C
91E30530 91E30534
39E00091 99E30564
8001007C 48000000

1.00 ------ 0x68294 ----- 8001007C -> Branch

7F03C378 81C30070
2C0E00B5 40820034
3DC0C200 91C30518
91C3051C 91C30520
91C30524 39E00000
91E30528 91E3052C
91E30530 91E30534
39E00091 99E30564
8001007C 48066F74


	-==-


Turn Yellow During Shield Stun
[Dan Salvato, standardtoaster, Stratocaster]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ----- 0x8006B80C --- 8001007C -> Branch

7F03C378 81C30070
2C0E00B5 40820038
3DC0C200 91C30518
91C3051C 91C30520
91C30524 39E00000
91E30528 91E3052C
91E30530 91E30534
91E30520 39E00091
99E30564 8001007C
48066F70

1.00 ------ 0x68294 ----- 8001007C -> Branch

7F03C378 81C30070
2C0E00B5 40820038
3DC0C200 91C30518
91C3051C 91C30520
91C30524 39E00000
91E30528 91E3052C
91E30530 91E30534
91E30520 39E00091
99E30564 8001007C
48066F70


	-==-


Yellow Color Overlay During IASA Frames
[Achilles]
1.02
C2071960 00000007
98032218 98030504
3C00437F 900304B8
900304BC 900304C4
38000000 900304C0
900304C8 900304CC
900304D0 900304D4
60000000 00000000


	-==-


Stock Dependent Revival Platform Colors
The coloring of the revival platform changes based on how many stocks a player has remaining.
3 stocks left = green platform
2 stocks left = orange platform
1 stock left = red platform (as in, danger....you're about to lose)
This only gets applied in a 4 stock vs. match. The revival platform is normal colored in any other game mode.
If two players die at roughly the same time, the same color will be used for both reviving players regardless as to whether or not their stock count differs. The color applied will be based on stock count of whoever technically died last. There can be multiple different colored revival platforms out at once, just as long as those characters don't die within a few frames of each other. I don't think I can fix this.
[achilles]
1.02
284530C8 FF000000
0046B6AF 00000000
******** 00000000
2A45BF14 00FF0400
0046B6AF 00000000
******** 00000000
2846B6AE FF000003
48000000 804D64FC
12000AC4 000000FF
12000AC6 000000FF
12000AF0 00000059
12000AF2 000000FF
12000B90 000000FF
12000B92 000000FF
12000CE0 000000FF
12000CE2 000000FF
12000D18 000000FF
12000D1A 000000FF
******** 80008000
2846B6AF FF000002
48000000 804D64FC
12000AC4 0000FF99
12000AC6 000000FF
12000AF0 00009F5F
12000AF2 000000FF
12000B90 0000FF99
12000B92 000000FF
12000CE0 0000FF99
12000CE2 000000FF
12000D18 0000FF99
12000D1A 000000FF
******** 80008000
2846B6AF FF000001
48000000 804D64FC
12000AC4 0000FF00
12000AC6 000000FF
12000AF0 00009000
12000AF2 000000FF
12000B90 0000FF00
12000B92 000000FF
12000CE0 0000FF00
12000CE2 000000FF
12000D18 0000FF00
12000D1A 000000FF
******** 80008000
2846B6AD FF000000
2845310E 00FF0300
0046B6AF 00000003
2845310F 00FF0200
0046B6AF 00000002
2845310F 00FF0100
0046B6AF 00000001
E2000002 80008000
2846B6AD FF000001
28453F9E 00FF0300
0046B6AF 00000003
28453F9F 00FF0200
0046B6AF 00000002
28453F9F 00FF0100
0046B6AF 00000001
E2000002 80008000
2846B6AD FF000002
28454E2E 00FF0300
0046B6AF 00000003
28454E2F 00FF0200
0046B6AF 00000002
28454E2F 00FF0100
0046B6AF 00000001
E2000002 80008000
2846B6AD FF000003
28455CBE 00FF0300
0046B6AF 00000003
28455CBF 00FF0200
0046B6AF 00000002
28455CBF 00FF0100
0046B6AF 00000001
E0000000 80008000


	-==-


Remove HUD (Percentages, Stock Icons, Timer)
Has the same effect as it does in the debug menu.
[Achilles]
1.02
004D6D58 00000001


	-==-


X+D-Pad Down (P1 Only) Toggles HUD On/Off
Can only be activated by Player 1.
[Achilles]
1.02
2846B108 00000404
004D6D58 00000000
CC000000 00000000
E2100000 00000000
004D6D58 00000001
******** 00000000


	-==-


Disable Color Overlays
ex. Spacies bodies turn blue during shine
<https://smashboards.com/threads/the-complete-csp-compendium.380315/post-20845251>
[Achilles]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x800BFFD0 ---- 7C0802A6 -> 4e800020


	-==-


Disable Effect Textures
ex. Falcon Punch fire eagle, landing cloud poof, Spacies shine (although this isn't technically a texture), etc.
<https://smashboards.com/threads/the-complete-csp-compendium.380315/post-20845251>
[Achilles]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x8005FDDC ---- 7C0802A6 -> 4e800020


	-==-


HUD Transparency v1.1
This code makes the fighter's HUD elements transparent when a fighter is positioned behind it.
<https://smashboards.com/threads/transparent-hud-v1-1.508509/>
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x802F6690 ---- 8001001C -> Branch

7C0802A6 90010004
9421FF00 BE810008
3860000E 3880000F
38A00000 3D808039
618C01F0 7D8903A6
4E800421 7C7F1B78
38600090 3D808037
618CF1E4 7D8903A6
4E800421 7C7E1B78
38800090 3D808000
618CC160 7D8903A6
4E800421 7FC6F378
7FE3FB78 38800004
3CA08037 60A5F1B0
3D808039 618C0B68
7D8903A6 4E800421
7FE3FB78 48000059
7C8802A6 38A00013
3D808038 618CFD54
7D8903A6 4E800421
4800040D 7C6802A6
7FC4F378 38A00000
C0230008 D0240000
C023000C D0240008
C0230010 D0240010
38A50001 38840018
2C050006 4180FFDC
480003F8 4E800021
7C0802A6 90010004
9421FF00 BE810008
83E3002C 480003B9
7FC802A6 3A800000
7FF5FB78 C03E0008
D0350004 C03E000C
D035000C C03E0010
D0350014 3A940001
3AB50018 2C140006
4180FFDC 806DC18C
82830020 480001D8
82B4002C 8875221F
54600673 408201C4
8875221E 54600631
408201B8 C03506F8
C0550778 EC41102A
D0410080 C0550780
EC41102A D0410084
C03506F4 C055078C
EC41102A D0410088
C0550784 EC41102A
D041008C 3D808003
618C0A50 7D8903A6
4E800421 83A30028
38600000 90610068
C0210088 D0210060
C0210080 D0210064
7FA3EB78 38810060
38A10070 38C00000
3D808000 618CE210
7D8903A6 4E800421
C0210070 D0210088
C0210074 D0210080
C021008C D0210060
C0210084 D0210064
7FA3EB78 38810060
38A10070 38C00000
3D808000 618CE210
7D8903A6 4E800421
C0210070 D021008C
C0210074 D0210084
3AC00000 7EC3B378
3D80802F 618C3424
7D8903A6 4E800421
7C641B78 3C60804A
60630FD8 80630000
80630028 38A10090
38C00000 3D808000
618CE210 7D8903A6
4E800421 C0210090
C05E0014 EC21102A
C0410088 FC020840
4080007C C0210090
C05E0014 EC211028
C041008C FC020840
40810064 C0210094
C05E0018 EC211028
C0410084 FC020840
4081004C C0210094
C05E0018 EC21102A
C0410080 FC020840
40800034 1C760018
7C63FA14 C05E0000
C03E0008 EC2100B2
D0230004 C03E000C
EC2100B2 D023000C
C03E0010 EC2100B2
D0230014 3AD60001
2C160006 4180FF28
82940008 2C140000
4082FE28 3A800000
1C740018 7EA3FA14
3C60804A 60631380
1C940050 7C632214
80630000 2C030000
4182000C 38950000
4800006D 3C60804A
606310C8 1C940064
7C632214 80630004
2C030000 4182000C
38950008 48000049
3C60804A 606310C8
1C940064 7C632214
80630000 2C030000
4182000C 38950010
48000025 3A940001
2C140006 4180FF84
BA810008 80010104
38210100 7C0803A6
4E800020 7C0802A6
90010004 9421FF00
BE810008 83E30028
7C9E2378 480000E9
7FA802A6 C03E0000
C05E0004 C07D0004
FC011040 4182003C
41810018 EC21182A
FC011040 40810020
FC201090 48000018
EC211828 FC011040
4080000C FC201090
48000004 D03E0000
7FE3FB78 48000019
BA810008 80010104
38210100 7C0803A6
4E800020 7C0802A6
90010004 9421FFE4
93E10014 93C10018
7C7F1B78 83DF0018
48000024 807E0008
2C030000 41820014
8063000C 2C030000
41820008 D023000C
83DE0004 2C1E0000
4082FFDC 807F0010
2C030000 41820008
4BFFFFAD 807F0008
2C030000 41820008
4BFFFF9D 83C10018
83E10014 80010020
3821001C 7C0803A6
4E800020 4E800021
3ECCCCCD 3DA3D70A
3F7FBE77 3F32F1AA
3F7FBE77 42820000
42820000 40000000
BA810008 80010104
38210100 7C0803A6
8001001C 00000000


	-==-


Smaller "Ready, GO!"
Slightly raises and shrinks the "Ready, GO!" text that appears at the start of matches, so it's less intrusive and the action happening behind them is more visible.
<https://smashboards.com/threads/smaller-ready-go.509740/>
[UnclePunch]
Revision ---- DOL Offset ---- Hex to Replace ---------- ASM Code -
NTSC 1.02 --- 0x802F71E0 ---- 80030010 -> Branch

4800004D 7D8802A6
8083000C 80840000
C02C0000 D0240020
D0240024 D0240028
C02C0008 D0240030
80830010 80840000
C02C0004 D0240020
D0240024 D0240028
C02C0008 D0240030
48000014 4E800021
3F19999A 3ECCCCCD
41200000 80030010
60000000 00000000
